# Result模式最佳实践指南

## 📖 概述

Result模式是一种类型安全的错误处理模式，通过将错误作为类型系统的一部分来替代传统的异常抛出机制。本文档基于对Result模式核心类的深入分析和tactic模块的成功实践，总结了完整的接入最佳实践。

## 🎯 核心理念

Result模式的核心思想是：**用Result<T>替代异常处理，让错误成为类型系统的一部分**。

### 核心优势
- ✅ **类型安全**：编译时检查错误处理
- ✅ **显式错误**：错误处理不能被遗忘
- ✅ **链式调用**：支持优雅的错误传播
- ✅ **性能监控**：内置业务操作监控
- ✅ **统一响应**：标准化的微服务响应格式

## 🏗️ 架构分层

### 1. Repository层
**职责**：数据访问，所有异常转换为Result
**核心类**：`BaseRepository`，`RepositoryResultWrapper`

### 2. Service层
**职责**：业务逻辑，检查Repository结果并进行相应处理
**核心类**：`BaseService`，`ServiceResultHandler`

### 3. Controller层
**职责**：接口适配，将Result转换为HTTP响应
**核心类**：`BaseController`，`XResponseUtils`

## 📋 最佳实践

### 1. 参数验证实践

#### ✅ 正确做法：使用装饰器统一验证
```typescript
@MessagePattern('tactic.activateTactic')
@UsePipes(StandardMicroserviceValidationPipe)  // ← 统一参数验证
@CacheEvict({...})
async activateTactic(@Payload() payload: ActivateTacticPayloadDto): Promise<XResponse<any>> {
  return this.handleRequest(async () => {
    const result = await this.tacticService.activateTactic(
      payload.characterId,
      payload.tacticKey
    );
    return this.fromResult(result);
  }, payload);
}
```

#### ❌ 错误做法：手动参数验证
```typescript
// 不要这样做
async activateTactic(@Payload() payload: any): Promise<XResponse<any>> {
  if (!payload.characterId) {
    return { code: -1, message: '角色ID不能为空' };
  }
  // ...
}
```

**最佳实践**：
- **不要手动验证参数**，Controller层统一使用`@UsePipes(StandardMicroserviceValidationPipe)`装饰器
- 参数验证逻辑放在DTO类中，通过装饰器实现
- 让框架自动处理验证错误

### 2. 返回类型实践

#### ✅ 正确做法：保持完整的返回类型
```typescript
// Service层
async activateTactic(characterId: string, tacticKey: string): Promise<XResult<any>> {
  return this.executeBusinessOperation(async () => {
    // 业务逻辑...
    return XResultUtils.ok({ message: '战术激活成功' });
  });
}

// Controller层
async activateTactic(@Payload() payload: ActivateTacticPayloadDto): Promise<XResponse<any>> {
  // 保持 Promise<XResponse<any>> 类型
}
```

#### ❌ 错误做法：删除返回类型
```typescript
// 不要删除接口的返回类型
async activateTactic(@Payload() payload: ActivateTacticPayloadDto) {
  // 缺少 Promise<XResponse<any>> 类型声明
}
```

**最佳实践**：
- **不要删除接口的返回类型**，特别是`Promise<XResponse<any>>`
- Service层始终返回`Promise<XResult<T>>`
- Controller层始终返回`Promise<XResponse<T>>`

### 3. 错误码实践

#### ✅ 正确做法：使用标准错误码
```typescript
// 导入标准错误码
import { ErrorCode } from '@libs/game-constants';

// 使用标准错误码
return XResultUtils.error('战术未解锁或不存在', ErrorCode.TACTIC_DEFINITION_NOT_FOUND);
```

#### ❌ 错误做法：使用魔法数字
```typescript
// 不要使用魔法数字
return XResultUtils.error('战术不存在', 'TACTIC_NOT_FOUND_001');
```

**最佳实践**：
- **错误码尽量使用**`libs\game-constants\src\codes\error-codes.ts`中定义的标准错误码
- 自定义错误码要有意义的前缀，避免冲突
- 错误码应该与前端约定一致

## 🚀 快速接入指南

### 步骤1：继承基础类

#### Service类继承BaseService
```typescript
@Injectable()
export class YourService extends BaseService {
  constructor(microserviceClient: MicroserviceClientService) {
    super('YourService', microserviceClient);
  }

  async yourMethod(params: any): Promise<XResult<YourData>> {
    return this.executeBusinessOperation(async () => {
      // 业务逻辑...
      return XResultUtils.ok(resultData);
    });
  }
}
```

#### Controller类继承BaseController
```typescript
@Controller()
export class YourController extends BaseController {
  constructor(private readonly yourService: YourService) {
    super('YourController');
  }

  @MessagePattern('your.pattern')
  @UsePipes(StandardMicroserviceValidationPipe)
  async yourHandler(@Payload() payload: YourPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      const result = await this.yourService.yourMethod(payload);
      return this.fromResult(result);
    }, payload);
  }
}
```

### 步骤2：Repository层转换

#### 转换前（异常模式）
```typescript
async findById(id: string): Promise<UserDocument | null> {
  try {
    return await this.userModel.findById(id).exec();
  } catch (error) {
    throw new Error('查询失败');
  }
}
```

#### 转换后（Result模式）
```typescript
async findById(id: string): Promise<XResult<UserDocument | null>> {
  return RepositoryResultWrapper.wrapNullable(async () => {
    return await this.userModel.findById(id).exec();
  });
}
```

### 步骤3：Service层转换

#### 转换前（异常模式）
```typescript
async updateUser(id: string, dto: UpdateDto): Promise<UserDocument> {
  const user = await this.userRepository.findById(id);
  if (!user) {
    throw new NotFoundException('用户不存在');
  }
  // 更新逻辑...
}
```

#### 转换后（Result模式）
```typescript
async updateUser(id: string, dto: UpdateDto): Promise<XResult<UserDocument>> {
  return this.executeBusinessOperation(async () => {
    const userResult = await this.userRepository.findById(id);
    const userError = ServiceResultHandler.propagateError<UserDocument>(userResult);
    if (userError) return userError;

    const user = userResult.data;
    // 更新逻辑...
    return XResultUtils.ok(updatedUser);
  });
}
```

### 步骤4：Controller层转换

#### 转换前（异常模式）
```typescript
@MessagePattern('user.update')
async updateUser(@Payload() payload: UpdatePayload) {
  try {
    const result = await this.userService.updateUser(payload.id, payload.dto);
    return { code: 0, data: result };
  } catch (error) {
    return { code: -1, message: error.message };
  }
}
```

#### 转换后（Result模式）
```typescript
@MessagePattern('user.update')
@UsePipes(StandardMicroserviceValidationPipe)
async updateUser(@Payload() payload: UpdatePayloadDto): Promise<XResponse<any>> {
  return this.handleRequest(async () => {
    const result = await this.userService.updateUser(payload.id, payload.dto);
    return this.fromResult(result);
  }, payload);
}
```

## 🛠️ 核心工具类使用指南

### XResultUtils - 核心工具

| 方法 | 用途 | 示例 |
|------|------|------|
| `XResultUtils.ok(data)` | 创建成功结果 | `return XResultUtils.ok(user);` |
| `XResultUtils.error(msg, code)` | 创建失败结果 | `return XResultUtils.error('用户不存在', 'USER_NOT_FOUND');` |
| `XResultUtils.empty()` | 创建空成功结果 | `return XResultUtils.empty();` |
| `XResultUtils.isSuccess(result)` | 检查是否成功 | `if (XResultUtils.isSuccess(result))` |
| `XResultUtils.isFailure(result)` | 检查是否失败 | `if (XResultUtils.isFailure(result))` |

### RepositoryResultWrapper - Repository专用

| 方法 | 用途 | 示例 |
|------|------|------|
| `wrap(operation)` | 通用包装 | `return RepositoryResultWrapper.wrap(async () => {...});` |
| `wrapNullable(operation)` | 可空查询包装 | `return RepositoryResultWrapper.wrapNullable(async () => {...});` |
| `wrapArray(operation)` | 数组查询包装 | `return RepositoryResultWrapper.wrapArray(async () => {...});` |

### ServiceResultHandler - Service专用

| 方法 | 用途 | 示例 |
|------|------|------|
| `propagateError(result)` | 传递错误 | `const error = ServiceResultHandler.propagateError(result);` |
| `checkOrFail(result, code, msg)` | 检查并转换错误 | `return ServiceResultHandler.checkOrFail(result, 'ERROR', '操作失败');` |

## 📊 成功案例分析

### Tactic模块成功实践

基于对tactic模块的深入分析，以下是成功的接入模式：

#### 1. 继承架构
```typescript
// Service继承BaseService
@Injectable()
export class TacticService extends BaseService {
  // 获得 executeBusinessOperation 方法
}

// Controller继承BaseController
@Controller()
export class TacticController extends BaseController {
  // 获得 handleRequest 和 fromResult 方法
}
```

#### 2. 统一错误处理
```typescript
// Service层：统一的业务操作包装
async activateTactic(characterId: string, tacticKey: string): Promise<XResult<any>> {
  return this.executeBusinessOperation(async () => {
    // 业务逻辑...
    return XResultUtils.ok({ message: '战术激活成功' });
  });
}

// Controller层：统一的请求处理
async activateTactic(@Payload() payload: ActivateTacticPayloadDto): Promise<XResponse<any>> {
  return this.handleRequest(async () => {
    const result = await this.tacticService.activateTactic(
      payload.characterId,
      payload.tacticKey
    );
    return this.fromResult(result);
  }, payload);
}
```

#### 3. 缓存集成
```typescript
@MessagePattern('tactic.getTactics')
@UsePipes(StandardMicroserviceValidationPipe)
@Cacheable({
  key: 'character:tactics:#{payload.characterId}',
  dataType: 'server',
  serverId: '#{payload.serverId}',
  ttl: 300
})
async getCharacterTactics(@Payload() payload: GetTacticsPayloadDto) {
  // 缓存装饰器与Result模式完美集成
}
```

## ⚠️ 常见陷阱与解决方案

### 1. 忘记继承基础类
```typescript
// ❌ 错误：没有继承BaseService
@Injectable()
export class MyService {
  // 缺少 executeBusinessOperation 方法
}

// ✅ 正确：继承BaseService
@Injectable()
export class MyService extends BaseService {
  // 获得完整的功能支持
}
```

### 2. 错误的错误处理
```typescript
// ❌ 错误：直接抛出异常
async myMethod(): Promise<XResult<any>> {
  if (condition) {
    throw new Error('错误'); // 破坏Result模式
  }
}

// ✅ 正确：返回错误结果
async myMethod(): Promise<XResult<any>> {
  if (condition) {
    return XResultUtils.error('错误信息', 'ERROR_CODE');
  }
}
```

### 3. 忘记检查Result结果
```typescript
// ❌ 错误：未检查Repository结果
async myMethod(): Promise<XResult<any>> {
  const data = await this.repository.findById(id); // 直接使用，可能为null
  return XResultUtils.ok(data);
}

// ✅ 正确：检查Repository结果
async myMethod(): Promise<XResult<any>> {
  const result = await this.repository.findById(id);
  const error = ServiceResultHandler.propagateError(result);
  if (error) return error;

  const data = result.data;
  return XResultUtils.ok(data);
}
```

### 4. 返回类型不一致
```typescript
// ❌ 错误：Service返回Promise<any>
async myMethod(): Promise<any> {
  // 丢失类型安全
}

// ✅ 正确：Service返回Promise<XResult<T>>
async myMethod(): Promise<XResult<MyData>> {
  // 类型安全保证
}
```

## 🎯 性能优化建议

### 1. 业务操作监控
```typescript
// 使用 executeBusinessOperation 获得性能监控
async complexBusinessMethod(): Promise<XResult<any>> {
  return this.executeBusinessOperation(async () => {
    // 复杂的业务逻辑
    // 自动获得执行时间、成功率等监控数据
  }, { reason: 'complex_business_operation' });
}
```

### 2. 缓存集成
```typescript
// Result模式与缓存装饰器完美配合
@MessagePattern('user.getProfile')
@Cacheable({
  key: 'user:profile:#{payload.userId}',
  ttl: 300
})
async getUserProfile(@Payload() payload: GetProfilePayloadDto): Promise<XResponse<any>> {
  return this.handleRequest(async () => {
    const result = await this.userService.getUserProfile(payload.userId);
    return this.fromResult(result);
  }, payload);
}
```

### 3. 批量操作优化
```typescript
// 使用批量操作减少网络调用
async batchUpdateUsers(updates: UpdateRequest[]): Promise<XResult<any>> {
  return this.executeBusinessOperation(async () => {
    const results = await this.batchCallMicroservices(updates.map(update => ({
      serviceName: 'user',
      pattern: 'user.update',
      payload: update
    })));

    return XResultUtils.ok({ processed: updates.length });
  }, { reason: 'batch_user_update' });
}
```

## 📚 总结

Result模式是一种强大的错误处理范式，通过将错误纳入类型系统，提供了：

- **类型安全**：编译时错误处理检查
- **代码质量**：强制处理所有错误情况
- **可维护性**：统一的错误处理模式
- **监控能力**：内置的性能和业务监控
- **开发效率**：减少调试时间，提高代码可读性

**核心三原则**：
1. **参数验证**：使用`@UsePipes(StandardMicroserviceValidationPipe)`统一验证
2. **返回类型**：保持完整的`Promise<XResponse<any>>`类型声明
3. **错误码**：使用`libs\game-constants\src\codes\error-codes.ts`标准错误码

通过遵循这些最佳实践，团队可以构建更加健壮、可维护和高效的微服务系统。
