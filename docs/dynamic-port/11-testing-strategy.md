# 动态端口分配测试策略

## 🎯 测试目标

确保动态端口分配架构的：
1. **功能正确性**：所有功能按预期工作
2. **性能稳定性**：性能影响在可接受范围内
3. **向后兼容性**：不破坏现有功能
4. **故障恢复能力**：异常情况下的处理能力

## 📊 测试矩阵

| 测试类型 | 覆盖范围 | 优先级 | 预计工时 | 自动化程度 |
|----------|----------|--------|----------|------------|
| **单元测试** | 核心算法和组件 | 🔴 极高 | 8小时 | 100% |
| **集成测试** | 模块间交互 | 🔴 极高 | 8小时 | 100% |
| **端到端测试** | 完整业务流程 | 🔴 极高 | 6小时 | 80% |
| **性能测试** | 响应时间和资源使用 | 🟡 高 | 4小时 | 90% |
| **压力测试** | 高负载场景 | 🟡 高 | 4小时 | 90% |
| **故障测试** | 异常和恢复 | 🟡 高 | 4小时 | 70% |
| **兼容性测试** | 向后兼容验证 | 🔴 极高 | 4小时 | 80% |

## 🧪 单元测试策略

### 1. PortManager核心算法测试

```typescript
// tests/unit/port-manager.test.ts
describe('PortManager', () => {
  describe('calculatePort', () => {
    it('应该正确计算character服务的端口', () => {
      const port = PortManager.calculatePort('character', 'server_001', 0);
      expect(port).toBe(3210); // 3200 + (1 * 10) + 0
    });
    
    it('应该正确处理不同的区服ID', () => {
      expect(PortManager.calculatePort('character', 'server_001', 0)).toBe(3210);
      expect(PortManager.calculatePort('character', 'server_002', 0)).toBe(3220);
      expect(PortManager.calculatePort('character', 'server_123', 0)).toBe(4430);
    });
    
    it('应该正确处理不同的实例ID', () => {
      expect(PortManager.calculatePort('character', 'server_001', 0)).toBe(3210);
      expect(PortManager.calculatePort('character', 'server_001', 1)).toBe(3211);
      expect(PortManager.calculatePort('character', 'server_001', 9)).toBe(3219);
    });
    
    it('应该正确处理无区服ID的情况', () => {
      expect(PortManager.calculatePort('character', undefined, 0)).toBe(3200);
      expect(PortManager.calculatePort('character', '', 0)).toBe(3200);
    });
    
    it('应该抛出错误当服务名未知时', () => {
      expect(() => PortManager.calculatePort('unknown', 'server_001', 0))
        .toThrow('未找到服务 unknown 的基础端口配置');
    });
    
    it('应该抛出错误当计算端口超出范围时', () => {
      // 模拟极大的区服编号
      expect(() => PortManager.calculatePort('character', 'server_99999', 0))
        .toThrow('计算的端口');
    });
  });
  
  describe('extractServerNumber', () => {
    it('应该正确提取区服编号', () => {
      expect(PortManager.extractServerNumber('server_001')).toBe(1);
      expect(PortManager.extractServerNumber('server_123')).toBe(123);
      expect(PortManager.extractServerNumber('test_999')).toBe(999);
    });
    
    it('应该处理无数字的情况', () => {
      expect(PortManager.extractServerNumber('server')).toBe(0);
      expect(PortManager.extractServerNumber('test_abc')).toBe(0);
      expect(PortManager.extractServerNumber(undefined)).toBe(0);
    });
  });
  
  describe('validatePortAvailability', () => {
    it('应该检测端口可用性', async () => {
      // 使用一个不太可能被占用的端口
      const port = 19999;
      const isAvailable = await PortManager.validatePortAvailability(port);
      expect(typeof isAvailable).toBe('boolean');
    });
    
    it('应该检测端口被占用的情况', async () => {
      // 创建一个服务器占用端口
      const server = net.createServer();
      const port = 19998;
      
      await new Promise(resolve => server.listen(port, resolve));
      
      const isAvailable = await PortManager.validatePortAvailability(port);
      expect(isAvailable).toBe(false);
      
      server.close();
    });
  });
});
```

### 2. ServiceMesh组件测试

```typescript
// tests/unit/service-mesh.test.ts
describe('ServerAwareRegistryService', () => {
  describe('registerInstance with dynamic port', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    
    it('应该计算动态端口当未提供端口时', async () => {
      const request: ServiceRegistrationRequest = {
        serviceName: 'character',
        serverId: 'server_001',
        instanceName: 'character-1',
        host: 'localhost',
        // port 未提供，应该动态计算
      };
      
      jest.spyOn(PortManager, 'calculatePort').mockReturnValue(3210);
      jest.spyOn(PortManager, 'validatePortAvailability').mockResolvedValue(true);
      
      const instanceId = await service.registerInstance(request);
      expect(instanceId).toBeDefined();
      
      expect(PortManager.calculatePort).toHaveBeenCalledWith('character', 'server_001', 0);
      expect(PortManager.validatePortAvailability).toHaveBeenCalledWith(3210);
    });
    
    it('应该使用提供的端口而不计算', async () => {
      const request: ServiceRegistrationRequest = {
        serviceName: 'character',
        serverId: 'server_001',
        instanceName: 'character-1',
        host: 'localhost',
        port: 3333, // 提供固定端口
      };
      
      jest.spyOn(PortManager, 'calculatePort');
      jest.spyOn(PortManager, 'validatePortAvailability').mockResolvedValue(true);
      
      await service.registerInstance(request);
      
      expect(PortManager.calculatePort).not.toHaveBeenCalled();
      expect(PortManager.validatePortAvailability).toHaveBeenCalledWith(3333);
    });
    
    it('应该抛出错误当端口不可用时', async () => {
      const request: ServiceRegistrationRequest = {
        serviceName: 'character',
        serverId: 'server_001',
        instanceName: 'character-1',
        host: 'localhost',
        port: 3210,
      };
      
      jest.spyOn(PortManager, 'validatePortAvailability').mockResolvedValue(false);
      
      await expect(service.registerInstance(request))
        .rejects.toThrow('端口 3210 不可用');
    });
    
    it('应该正确设置元数据', async () => {
      const request: ServiceRegistrationRequest = {
        serviceName: 'character',
        serverId: 'server_001',
        instanceName: 'character-1',
        host: 'localhost',
      };
      
      jest.spyOn(PortManager, 'calculatePort').mockReturnValue(3210);
      jest.spyOn(PortManager, 'validatePortAvailability').mockResolvedValue(true);
      jest.spyOn(PortManager, 'getBasePort').mockReturnValue(3200);
      
      await service.registerInstance(request);
      
      const instances = await service.getHealthyInstances('character', 'server_001');
      expect(instances).toHaveLength(1);
      
      const instance = instances[0];
      expect(instance.port).toBe(3210);
      expect(instance.metadata.calculatedPort).toBe(true);
      expect(instance.metadata.portAllocationStrategy).toBe('dynamic');
      expect(instance.metadata.basePort).toBe(3200);
    });
  });
});
```

## 🔗 集成测试策略

### 1. 服务启动集成测试

```typescript
// tests/integration/service-startup.test.ts
describe('Service Startup Integration', () => {
  it('应该支持多个Character实例启动', async () => {
    const instances = [];
    
    // 启动3个不同区服的实例
    for (let i = 1; i <= 3; i++) {
      const serverId = `server_00${i}`;
      const expectedPort = 3200 + (i * 10); // 3210, 3220, 3230
      
      const instance = await startCharacterService({
        SERVER_ID: serverId,
        INSTANCE_ID: '0',
      });
      
      expect(instance.port).toBe(expectedPort);
      expect(instance.isHealthy()).toBe(true);
      
      instances.push(instance);
    }
    
    // 验证所有实例都在运行
    for (const instance of instances) {
      const response = await request(`http://localhost:${instance.port}`)
        .get('/health')
        .expect(200);
      
      expect(response.body.status).toBe('ok');
    }
    
    // 清理
    for (const instance of instances) {
      await instance.stop();
    }
  });
  
  it('应该支持同一区服的多个实例', async () => {
    const serverId = 'server_001';
    const instances = [];
    
    // 启动同一区服的3个实例
    for (let instanceId = 0; instanceId < 3; instanceId++) {
      const expectedPort = 3210 + instanceId; // 3210, 3211, 3212
      
      const instance = await startCharacterService({
        SERVER_ID: serverId,
        INSTANCE_ID: instanceId.toString(),
      });
      
      expect(instance.port).toBe(expectedPort);
      instances.push(instance);
    }
    
    // 验证所有实例都注册到ServiceMesh
    const registeredInstances = await serviceRegistry.getHealthyInstances('character', serverId);
    expect(registeredInstances).toHaveLength(3);
    
    // 清理
    for (const instance of instances) {
      await instance.stop();
    }
  });
});
```

### 2. 服务发现集成测试

```typescript
// tests/integration/service-discovery.test.ts
describe('Service Discovery Integration', () => {
  it('应该发现正确的动态端口实例', async () => {
    // 启动Character服务实例
    const characterInstance = await startCharacterService({
      SERVER_ID: 'server_001',
      INSTANCE_ID: '0',
    });
    
    // 等待服务注册完成
    await waitForServiceRegistration('character', 'server_001');
    
    // 通过服务发现获取实例
    const discoveredInstance = await serviceDiscovery.discoverService('character', {
      serverId: 'server_001'
    });
    
    expect(discoveredInstance).toBeDefined();
    expect(discoveredInstance.serviceName).toBe('character');
    expect(discoveredInstance.port).toBe(3210);
    expect(discoveredInstance.host).toBe('localhost');
    
    // 验证可以连接到发现的实例
    const response = await request(`http://${discoveredInstance.host}:${discoveredInstance.port}`)
      .get('/health')
      .expect(200);
    
    expect(response.body.status).toBe('ok');
    
    await characterInstance.stop();
  });
});
```

### 3. 网关代理集成测试

```typescript
// tests/integration/gateway-proxy.test.ts
describe('Gateway Proxy Integration', () => {
  it('应该正确代理到动态端口服务', async () => {
    // 启动Character服务
    const characterInstance = await startCharacterService({
      SERVER_ID: 'server_001',
      INSTANCE_ID: '0',
    });
    
    // 启动Gateway
    const gateway = await startGatewayService();
    
    // 等待服务注册
    await waitForServiceRegistration('character', 'server_001');
    
    // 通过网关发送请求
    const response = await request(`http://localhost:${gateway.port}`)
      .get('/api/character/health')
      .set('X-Server-ID', 'server_001')
      .expect(200);
    
    expect(response.body.status).toBe('ok');
    
    // 验证请求被代理到正确的实例
    expect(characterInstance.getRequestCount()).toBe(1);
    
    await characterInstance.stop();
    await gateway.stop();
  });
});
```

## 🚀 端到端测试策略

### 1. 完整业务流程测试

```typescript
// tests/e2e/complete-flow.test.ts
describe('Complete Dynamic Port Flow', () => {
  it('应该支持完整的多区服业务流程', async () => {
    // 1. 启动多个区服的Character和Hero服务
    const services = await Promise.all([
      startCharacterService({ SERVER_ID: 'server_001' }),
      startCharacterService({ SERVER_ID: 'server_002' }),
      startHeroService({ SERVER_ID: 'server_001' }),
      startHeroService({ SERVER_ID: 'server_002' }),
    ]);
    
    // 2. 启动Gateway
    const gateway = await startGatewayService();
    
    // 3. 等待所有服务注册
    await waitForAllServicesRegistered();
    
    // 4. 测试区服1的业务流程
    const user1 = await createTestUser('server_001');
    const character1 = await createCharacter(gateway, user1, 'server_001');
    const hero1 = await createHero(gateway, character1, 'server_001');
    
    // 5. 测试区服2的业务流程
    const user2 = await createTestUser('server_002');
    const character2 = await createCharacter(gateway, user2, 'server_002');
    const hero2 = await createHero(gateway, character2, 'server_002');
    
    // 6. 验证数据隔离
    expect(character1.serverId).toBe('server_001');
    expect(character2.serverId).toBe('server_002');
    expect(character1.id).not.toBe(character2.id);
    
    // 7. 清理
    for (const service of services) {
      await service.stop();
    }
    await gateway.stop();
  });
});
```

### 2. WebSocket消息路由测试

```typescript
// tests/e2e/websocket-routing.test.ts
describe('WebSocket Message Routing', () => {
  it('应该正确路由WebSocket消息到动态端口服务', async () => {
    // 启动服务
    const characterService = await startCharacterService({ SERVER_ID: 'server_001' });
    const gateway = await startGatewayService();
    
    // 建立WebSocket连接
    const client = new WebSocketClient(`ws://localhost:${gateway.port}`);
    await client.connect();
    
    // 发送消息到Character服务
    const response = await client.sendMessage({
      command: 'character.getProfile',
      data: { characterId: 'test_character_id' }
    });
    
    expect(response.success).toBe(true);
    expect(response.data).toBeDefined();
    
    // 验证消息被路由到正确的服务实例
    expect(characterService.getMessageCount()).toBe(1);
    
    await client.disconnect();
    await characterService.stop();
    await gateway.stop();
  });
});
```

## ⚡ 性能测试策略

### 1. 启动性能测试

```typescript
// tests/performance/startup-performance.test.ts
describe('Startup Performance', () => {
  it('动态端口计算不应显著影响启动时间', async () => {
    const iterations = 10;
    const startupTimes = [];
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      
      const service = await startCharacterService({
        SERVER_ID: `server_${String(i).padStart(3, '0')}`,
      });
      
      const endTime = Date.now();
      startupTimes.push(endTime - startTime);
      
      await service.stop();
    }
    
    const avgStartupTime = startupTimes.reduce((a, b) => a + b) / iterations;
    const maxStartupTime = Math.max(...startupTimes);
    
    console.log(`平均启动时间: ${avgStartupTime}ms`);
    console.log(`最大启动时间: ${maxStartupTime}ms`);
    
    // 验收标准：启动时间增加 < 10ms
    expect(avgStartupTime).toBeLessThan(5000); // 基准 + 10ms
    expect(maxStartupTime).toBeLessThan(6000);
  });
});
```

### 2. 代理性能测试

```typescript
// tests/performance/proxy-performance.test.ts
describe('Proxy Performance', () => {
  it('动态端口代理不应显著影响响应时间', async () => {
    const characterService = await startCharacterService({ SERVER_ID: 'server_001' });
    const gateway = await startGatewayService();
    
    const requestCount = 1000;
    const responseTimes = [];
    
    for (let i = 0; i < requestCount; i++) {
      const startTime = Date.now();
      
      await request(`http://localhost:${gateway.port}`)
        .get('/api/character/health')
        .set('X-Server-ID', 'server_001')
        .expect(200);
      
      const endTime = Date.now();
      responseTimes.push(endTime - startTime);
    }
    
    const avgResponseTime = responseTimes.reduce((a, b) => a + b) / requestCount;
    const p95ResponseTime = responseTimes.sort()[Math.floor(requestCount * 0.95)];
    
    console.log(`平均响应时间: ${avgResponseTime}ms`);
    console.log(`P95响应时间: ${p95ResponseTime}ms`);
    
    // 验收标准：代理延迟增加 < 5ms
    expect(avgResponseTime).toBeLessThan(50); // 基准 + 5ms
    expect(p95ResponseTime).toBeLessThan(100);
    
    await characterService.stop();
    await gateway.stop();
  });
});
```

## 🛡️ 故障测试策略

### 1. 端口冲突处理测试

```typescript
// tests/fault/port-conflict.test.ts
describe('Port Conflict Handling', () => {
  it('应该正确处理端口被占用的情况', async () => {
    // 先占用端口3210
    const server = net.createServer();
    await new Promise(resolve => server.listen(3210, resolve));
    
    // 尝试启动Character服务（应该计算到3210端口）
    await expect(startCharacterService({
      SERVER_ID: 'server_001',
      INSTANCE_ID: '0',
    })).rejects.toThrow('端口 3210 不可用');
    
    server.close();
  });
  
  it('应该支持端口冲突时的自动重试', async () => {
    // 启用自动重试配置
    process.env.AUTO_RETRY_PORT_ON_CONFLICT = 'true';
    
    // 占用计算出的端口
    const server = net.createServer();
    await new Promise(resolve => server.listen(3210, resolve));
    
    // 启动服务（应该自动尝试下一个端口）
    const service = await startCharacterService({
      SERVER_ID: 'server_001',
      INSTANCE_ID: '0',
    });
    
    expect(service.port).toBe(3211); // 下一个可用端口
    
    await service.stop();
    server.close();
  });
});
```

### 2. ServiceMesh故障测试

```typescript
// tests/fault/service-mesh-failure.test.ts
describe('ServiceMesh Failure Handling', () => {
  it('应该在ServiceMesh不可用时使用降级机制', async () => {
    // 模拟ServiceMesh故障
    jest.spyOn(serviceRegistry, 'registerInstance').mockRejectedValue(new Error('ServiceMesh unavailable'));
    
    // 启动服务（应该仍然能够启动，但不注册到ServiceMesh）
    const service = await startCharacterService({
      SERVER_ID: 'server_001',
      FALLBACK_MODE: 'true',
    });
    
    expect(service.isHealthy()).toBe(true);
    expect(service.port).toBe(3210);
    
    await service.stop();
  });
});
```

## 🔄 向后兼容性测试

### 1. 固定端口配置兼容性测试

```typescript
// tests/compatibility/fixed-port.test.ts
describe('Fixed Port Compatibility', () => {
  it('应该支持传统的固定端口配置', async () => {
    // 使用传统的固定端口配置
    const service = await startCharacterService({
      CHARACTER_PORT: '3002', // 固定端口
      // 不设置SERVER_ID，使用传统模式
    });
    
    expect(service.port).toBe(3002);
    expect(service.isHealthy()).toBe(true);
    
    // 验证服务注册时使用固定端口
    const instances = await serviceRegistry.getHealthyInstances('character', 'default');
    expect(instances).toHaveLength(1);
    expect(instances[0].port).toBe(3002);
    expect(instances[0].metadata.calculatedPort).toBe(false);
    
    await service.stop();
  });
});
```

## 📊 测试报告和指标

### 1. 测试覆盖率要求

| 组件 | 行覆盖率 | 分支覆盖率 | 函数覆盖率 |
|------|----------|------------|------------|
| PortManager | > 95% | > 90% | 100% |
| ServiceMesh | > 90% | > 85% | > 95% |
| Gateway Proxy | > 85% | > 80% | > 90% |
| Application Startup | > 90% | > 85% | > 95% |

### 2. 性能基准

| 指标 | 基准值 | 目标值 | 验收标准 |
|------|--------|--------|----------|
| 服务启动时间 | 2000ms | < 2010ms | < 2100ms |
| 端口计算时间 | 0ms | < 1ms | < 5ms |
| 代理响应时间 | 45ms | < 50ms | < 60ms |
| 内存使用 | 50MB | < 51MB | < 55MB |

### 3. 测试执行计划

```bash
# 1. 单元测试
npm run test:unit

# 2. 集成测试
npm run test:integration

# 3. 端到端测试
npm run test:e2e

# 4. 性能测试
npm run test:performance

# 5. 故障测试
npm run test:fault

# 6. 兼容性测试
npm run test:compatibility

# 7. 生成测试报告
npm run test:report
```

## 🎯 验收标准

### 功能验收
- [ ] 所有单元测试通过（覆盖率 > 90%）
- [ ] 所有集成测试通过
- [ ] 端到端测试场景覆盖完整
- [ ] 故障恢复机制验证通过

### 性能验收
- [ ] 启动时间影响 < 10ms
- [ ] 代理延迟影响 < 5ms
- [ ] 内存使用增加 < 1MB
- [ ] 端口计算性能 < 1ms

### 稳定性验收
- [ ] 压力测试通过（1000并发请求）
- [ ] 故障恢复测试通过
- [ ] 长时间运行测试通过（24小时）
- [ ] 内存泄漏检测通过

### 兼容性验收
- [ ] 向后兼容性测试通过
- [ ] 现有功能不受影响
- [ ] 配置迁移测试通过
- [ ] 降级机制测试通过
