# Auth服务API接口文档

## 📋 概述

本文档详细描述了Auth服务提供的所有HTTP接口，包括请求格式、响应格式、错误码和使用示例。

## 🔐 基础认证接口

### 用户登录
**POST** `/api/auth/login`

获取账号级Token，用于账号管理和区服选择。

#### 请求参数
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "deviceId": "device_123456"
}
```

#### 响应格式
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 900,
    "expiresAt": "2024-01-15T10:45:00.000Z",
    "user": {
      "id": "user_123456",
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

### 用户登出
**POST** `/api/auth/logout`

注销当前账号会话。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "登出成功"
}
```

### 刷新Token
**POST** `/api/auth/refresh`

使用刷新Token获取新的访问Token。

#### 请求参数
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 响应格式
```json
{
  "success": true,
  "message": "Token刷新成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 900,
    "expiresAt": "2024-01-15T10:45:00.000Z"
  }
}
```

## 🎮 角色认证接口

### 角色登录
**POST** `/api/auth/character-auth/login`

使用账号Token进入指定区服，获取角色级Token。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 请求参数
```json
{
  "serverId": "server_001",
  "characterId": "char_123456"  // 可选，不指定则使用默认角色
}
```

#### 响应格式
```json
{
  "success": true,
  "message": "角色登录成功",
  "data": {
    "characterToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 14400,
    "expiresAt": "2024-01-15T14:30:00.000Z",
    "character": {
      "characterId": "char_123456",
      "name": "勇敢的冒险者",
      "level": 25,
      "serverId": "server_001",
      "userId": "user_123456"
    },
    "server": {
      "id": "server_001",
      "name": "新手村",
      "status": "active",
      "openTime": "2024-01-01T00:00:00.000Z",
      "maxPlayers": 10000
    },
    "session": {
      "id": "session_123456789",
      "expiresAt": "2024-01-15T14:30:00.000Z"
    }
  }
}
```

### 角色登出
**POST** `/api/auth/character-auth/logout`

使用角色Token退出当前角色会话。

#### 请求头
```
Authorization: Bearer <character_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "角色登出成功"
}
```

### 刷新角色Token
**POST** `/api/auth/character-auth/refresh`

延长当前角色Token的有效期。

#### 请求头
```
Authorization: Bearer <character_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "角色Token刷新成功",
  "data": {
    "characterToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 14400
  }
}
```

### 获取当前会话信息
**GET** `/api/auth/character-auth/session`

获取当前角色会话的详细信息。

#### 请求头
```
Authorization: Bearer <character_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "获取会话信息成功",
  "data": {
    "sessionId": "session_123456789",
    "userId": "user_123456",
    "characterId": "char_123456",
    "serverId": "server_001",
    "serverName": "新手村",
    "lastActivity": "2024-01-15T10:25:00.000Z",
    "expiresAt": "2024-01-15T14:30:00.000Z"
  }
}
```

## 📊 用户历史接口

### 获取用户完整历史记录
**GET** `/api/auth/user-history`

获取当前用户的完整历史记录。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "获取用户历史记录成功",
  "data": {
    "userId": "user_123456",
    "username": "testuser",
    "email": "<EMAIL>",
    "lastServerId": "server_001",
    "lastCharacterId": "char_123456",
    "serverHistory": [
      {
        "serverId": "server_001",
        "serverName": "新手村",
        "firstLoginTime": "2024-01-01T10:00:00.000Z",
        "lastLoginTime": "2024-01-15T10:30:00.000Z",
        "totalPlayTime": 86400,
        "loginCount": 30,
        "characters": [
          {
            "characterId": "char_123456",
            "name": "勇敢的冒险者",
            "level": 25,
            "power": 5000,
            "lastActiveAt": "2024-01-15T10:30:00.000Z"
          }
        ],
        "serverStats": {
          "playTime": 86400,
          "loginDays": 15,
          "characterCount": 1,
          "maxLevel": 25,
          "maxPower": 5000
        }
      }
    ],
    "globalStats": {
      "totalPlayTime": 86400,
      "totalLoginDays": 15,
      "totalCharacters": 1,
      "totalServersPlayed": 1,
      "firstGameTime": "2024-01-01T10:00:00.000Z",
      "lastLoginTime": "2024-01-15T10:30:00.000Z"
    }
  }
}
```

### 获取用户区服历史
**GET** `/api/auth/user-history/servers`

获取用户在各个区服的历史记录。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "获取用户区服历史成功",
  "data": {
    "servers": [
      {
        "serverId": "server_001",
        "serverName": "新手村",
        "firstLoginTime": "2024-01-01T10:00:00.000Z",
        "lastLoginTime": "2024-01-15T10:30:00.000Z",
        "totalPlayTime": 86400,
        "loginCount": 30,
        "characterCount": 1,
        "recommendationScore": 85
      }
    ],
    "totalServers": 1
  }
}
```

### 获取指定区服的历史记录
**GET** `/api/auth/user-history/servers/{serverId}`

获取用户在指定区服的详细历史记录。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 路径参数
- `serverId`: 区服ID

#### 响应格式
```json
{
  "success": true,
  "message": "获取区服历史记录成功",
  "data": {
    "serverId": "server_001",
    "serverName": "新手村",
    "firstLoginTime": "2024-01-01T10:00:00.000Z",
    "lastLoginTime": "2024-01-15T10:30:00.000Z",
    "totalPlayTime": 86400,
    "loginCount": 30,
    "characters": [
      {
        "characterId": "char_123456",
        "name": "勇敢的冒险者",
        "level": 25,
        "power": 5000,
        "profession": "前锋",
        "lastActiveAt": "2024-01-15T10:30:00.000Z",
        "totalPlayTime": 86400
      }
    ],
    "serverStats": {
      "playTime": 86400,
      "loginDays": 15,
      "characterCount": 1,
      "maxLevel": 25,
      "maxPower": 5000,
      "totalBattles": 100,
      "winRate": 0.75
    }
  }
}
```

### 获取用户角色摘要
**GET** `/api/auth/user-history/characters`

获取用户的所有角色摘要信息。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 查询参数
- `serverId` (可选): 区服ID，不指定则返回所有区服的角色

#### 响应格式
```json
{
  "success": true,
  "message": "获取用户角色摘要成功",
  "data": {
    "characters": [
      {
        "characterId": "char_123456",
        "name": "勇敢的冒险者",
        "level": 25,
        "power": 5000,
        "profession": "前锋",
        "serverId": "server_001",
        "lastActiveAt": "2024-01-15T10:30:00.000Z",
        "totalPlayTime": 86400,
        "isMain": true,
        "status": "active"
      }
    ],
    "totalCharacters": 1,
    "serverId": "server_001"
  }
}
```

### 获取用户全局统计
**GET** `/api/auth/user-history/stats`

获取用户的全局统计数据。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "获取用户全局统计成功",
  "data": {
    "totalPlayTime": 86400,
    "totalLoginDays": 15,
    "totalCharacters": 1,
    "totalServersPlayed": 1,
    "firstGameTime": "2024-01-01T10:00:00.000Z",
    "totalRecharge": 99.99,
    "vipLevel": 3,
    "lastLoginTime": "2024-01-15T10:30:00.000Z"
  }
}
```

### 获取推荐区服
**GET** `/api/auth/user-history/recommendations`

基于用户历史记录获取推荐的区服列表。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "获取推荐区服成功",
  "data": {
    "servers": [
      {
        "id": "server_001",
        "name": "新手村",
        "status": "active",
        "openTime": "2024-01-01T00:00:00.000Z",
        "hasCharacter": true,
        "lastPlayTime": "2024-01-15T10:30:00.000Z",
        "totalPlayTime": 86400,
        "characterCount": 1,
        "recommendationScore": 85
      }
    ],
    "totalServers": 1
  }
}
```

### 手动同步用户历史数据
**POST** `/api/auth/user-history/sync`

手动触发用户历史数据的同步更新。

#### 请求头
```
Authorization: Bearer <account_token>
```

#### 响应格式
```json
{
  "success": true,
  "message": "用户历史数据同步成功"
}
```

## 🚨 错误码说明

### HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权或Token无效
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误码
```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": "详细错误信息"
}
```

#### 常见错误码
- `INVALID_TOKEN`: Token无效或已过期
- `INVALID_SCOPE`: Token作用域不匹配
- `SESSION_EXPIRED`: 会话已过期
- `SERVER_NOT_FOUND`: 区服不存在
- `CHARACTER_NOT_FOUND`: 角色不存在
- `SERVER_FULL`: 区服已满
- `PERMISSION_DENIED`: 权限不足

## 📝 使用示例

### 完整的角色登录流程
```javascript
// 1. 账号登录
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});
const { accessToken } = await loginResponse.json();

// 2. 角色登录
const characterLoginResponse = await fetch('/api/auth/character-auth/login', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    serverId: 'server_001',
    characterId: 'char_123456'
  })
});
const { characterToken } = await characterLoginResponse.json();

// 3. 使用角色Token访问游戏功能
const gameResponse = await fetch('/api/game/some-action', {
  headers: {
    'Authorization': `Bearer ${characterToken}`
  }
});
```

## 🔒 安全注意事项

1. **Token存储**: Token应安全存储，避免XSS攻击
2. **HTTPS**: 生产环境必须使用HTTPS
3. **Token刷新**: 及时刷新即将过期的Token
4. **权限检查**: 严格按照Token作用域使用相应接口
5. **错误处理**: 正确处理认证失败的情况

## 📊 接口限流

- **登录接口**: 每分钟最多5次尝试
- **Token刷新**: 每分钟最多10次
- **历史查询**: 每分钟最多30次
- **其他接口**: 每分钟最多100次

## 🔄 WebSocket接口

### 实时会话状态
Auth服务支持通过WebSocket实时推送会话状态变化：

```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:3001/auth/session');

// 监听会话状态变化
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  switch (data.type) {
    case 'SESSION_EXPIRED':
      // 处理会话过期
      handleSessionExpired();
      break;
    case 'TOKEN_REFRESH_REQUIRED':
      // 处理Token刷新需求
      refreshToken();
      break;
    case 'FORCE_LOGOUT':
      // 处理强制登出
      handleForceLogout();
      break;
  }
};
```
