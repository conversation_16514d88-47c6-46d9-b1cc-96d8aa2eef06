#!/usr/bin/env node

/**
 * Gateway多实例测试脚本
 * 
 * 功能：
 * 1. 测试单实例模式
 * 2. 测试多实例模式
 * 3. 验证健康检查端点
 * 4. 验证端口分配逻辑
 */

const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');

class GatewayMultiInstanceTester {
  constructor() {
    this.processes = [];
    this.testResults = [];
  }

  /**
   * 测试单实例模式
   */
  async testSingleInstance() {
    console.log('\n🔒 测试单实例模式...');
    
    const env = {
      ...process.env,
      GATEWAY_MULTI_INSTANCE: 'false',
      GATEWAY_PORT: '3000',
      NODE_ENV: 'test'
    };

    const process = await this.startGatewayProcess(env, '单实例');
    
    // 等待服务启动
    await this.waitForService(3000);
    
    // 测试健康检查
    const health = await this.checkHealth(3000);
    
    console.log('✅ 单实例模式测试结果:');
    console.log(`   端口: ${health.instance.port}`);
    console.log(`   模式: ${health.instance.mode}`);
    console.log(`   实例ID: ${health.instance.id}`);
    
    this.testResults.push({
      mode: 'single',
      port: health.instance.port,
      instanceId: health.instance.id,
      success: health.status === 'ok'
    });

    // 停止进程
    this.stopProcess(process);
  }

  /**
   * 测试多实例模式
   */
  async testMultiInstance() {
    console.log('\n🔄 测试多实例模式...');
    
    const instances = [
      { id: 0, expectedPort: 3000 },
      { id: 1, expectedPort: 3001 },
      { id: 2, expectedPort: 3002 }
    ];

    const processes = [];

    // 启动多个实例
    for (const instance of instances) {
      const env = {
        ...process.env,
        GATEWAY_MULTI_INSTANCE: 'true',
        GATEWAY_INSTANCE_ID: instance.id.toString(),
        GATEWAY_BASE_PORT: '3000',
        GATEWAY_MAX_INSTANCES: '5',
        NODE_ENV: 'test'
      };

      console.log(`   启动实例${instance.id} (预期端口: ${instance.expectedPort})`);
      const process = await this.startGatewayProcess(env, `多实例-${instance.id}`);
      processes.push({ process, instance });

      // 等待服务启动
      await this.waitForService(instance.expectedPort);
    }

    // 测试所有实例的健康检查
    console.log('\n📊 验证多实例健康状态:');
    for (const { instance } of processes) {
      try {
        const health = await this.checkHealth(instance.expectedPort);
        
        console.log(`✅ 实例${instance.id}:`);
        console.log(`   端口: ${health.instance.port} (预期: ${instance.expectedPort})`);
        console.log(`   模式: ${health.instance.mode}`);
        console.log(`   基础端口: ${health.instance.basePort}`);
        console.log(`   计算端口: ${health.instance.calculatedPort}`);
        
        this.testResults.push({
          mode: 'multi',
          instanceId: instance.id,
          port: health.instance.port,
          expectedPort: instance.expectedPort,
          success: health.status === 'ok' && health.instance.port === instance.expectedPort
        });
      } catch (error) {
        console.log(`❌ 实例${instance.id} 健康检查失败: ${error.message}`);
        this.testResults.push({
          mode: 'multi',
          instanceId: instance.id,
          expectedPort: instance.expectedPort,
          success: false,
          error: error.message
        });
      }
    }

    // 停止所有进程
    processes.forEach(({ process }) => this.stopProcess(process));
  }

  /**
   * 测试端口冲突处理
   */
  async testPortConflict() {
    console.log('\n⚠️ 测试端口冲突处理...');
    
    // 先启动一个实例占用端口3000
    const env1 = {
      ...process.env,
      GATEWAY_MULTI_INSTANCE: 'true',
      GATEWAY_INSTANCE_ID: '0',
      GATEWAY_BASE_PORT: '3000',
      NODE_ENV: 'test'
    };

    const process1 = await this.startGatewayProcess(env1, '冲突测试-1');
    await this.waitForService(3000);

    // 尝试启动另一个实例使用相同端口
    const env2 = {
      ...process.env,
      GATEWAY_MULTI_INSTANCE: 'true',
      GATEWAY_INSTANCE_ID: '0', // 相同的实例ID，应该导致端口冲突
      GATEWAY_BASE_PORT: '3000',
      NODE_ENV: 'test'
    };

    console.log('   尝试启动冲突实例...');
    try {
      const process2 = await this.startGatewayProcess(env2, '冲突测试-2', false);
      
      // 等待一段时间看是否会报错
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      console.log('❌ 端口冲突检测失败 - 应该阻止启动');
      this.stopProcess(process2);
    } catch (error) {
      console.log('✅ 端口冲突检测正常 - 正确阻止了冲突启动');
    }

    this.stopProcess(process1);
  }

  /**
   * 启动Gateway进程
   */
  async startGatewayProcess(env, label, expectSuccess = true) {
    return new Promise((resolve, reject) => {
      const gatewayPath = path.join(__dirname, '../dist/apps/gateway/main.js');
      
      const process = spawn('node', [gatewayPath], {
        env,
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: path.join(__dirname, '..')
      });

      let startupComplete = false;
      let output = '';

      process.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        
        if (text.includes('Gateway服务已完全启动') && !startupComplete) {
          startupComplete = true;
          resolve(process);
        }
      });

      process.stderr.on('data', (data) => {
        const text = data.toString();
        output += text;
        
        if (text.includes('端口') && text.includes('不可用') && !expectSuccess) {
          reject(new Error('端口冲突检测正常'));
        }
      });

      process.on('error', (error) => {
        if (!expectSuccess) {
          reject(error);
        } else {
          console.error(`❌ ${label} 进程启动失败:`, error.message);
          reject(error);
        }
      });

      process.on('exit', (code) => {
        if (code !== 0 && !startupComplete && expectSuccess) {
          console.error(`❌ ${label} 进程异常退出，代码: ${code}`);
          console.error('输出:', output);
          reject(new Error(`进程退出，代码: ${code}`));
        }
      });

      // 超时处理
      setTimeout(() => {
        if (!startupComplete && expectSuccess) {
          console.error(`❌ ${label} 启动超时`);
          console.error('输出:', output);
          this.stopProcess(process);
          reject(new Error('启动超时'));
        }
      }, 30000);

      this.processes.push(process);
    });
  }

  /**
   * 等待服务可用
   */
  async waitForService(port, maxAttempts = 30) {
    for (let i = 0; i < maxAttempts; i++) {
      try {
        await axios.get(`http://localhost:${port}/health`, { timeout: 1000 });
        return;
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    throw new Error(`服务端口${port}在${maxAttempts}秒内未响应`);
  }

  /**
   * 检查健康状态
   */
  async checkHealth(port) {
    const response = await axios.get(`http://localhost:${port}/health`, { timeout: 5000 });
    return response.data;
  }

  /**
   * 停止进程
   */
  stopProcess(process) {
    if (process && !process.killed) {
      process.kill('SIGTERM');
      
      // 强制杀死
      setTimeout(() => {
        if (!process.killed) {
          process.kill('SIGKILL');
        }
      }, 5000);
    }
  }

  /**
   * 清理所有进程
   */
  cleanup() {
    console.log('\n🧹 清理测试进程...');
    this.processes.forEach(process => this.stopProcess(process));
    this.processes = [];
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(60));
    
    const successful = this.testResults.filter(r => r.success).length;
    const total = this.testResults.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`成功: ${successful}`);
    console.log(`失败: ${total - successful}`);
    console.log(`成功率: ${((successful / total) * 100).toFixed(1)}%`);
    
    console.log('\n详细结果:');
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.mode}模式 - 实例${result.instanceId || 0} - 端口${result.port || result.expectedPort}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始Gateway多实例测试');
    
    try {
      await this.testSingleInstance();
      await this.testMultiInstance();
      await this.testPortConflict();
      
      this.generateReport();
      
      console.log('\n✅ 所有测试完成');
    } catch (error) {
      console.error('\n❌ 测试过程中发生错误:', error.message);
    } finally {
      this.cleanup();
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new GatewayMultiInstanceTester();
  
  // 处理退出信号
  process.on('SIGINT', () => {
    console.log('\n⏹️ 收到退出信号，清理进程...');
    tester.cleanup();
    process.exit(0);
  });
  
  tester.runAllTests().catch(error => {
    console.error('测试失败:', error);
    tester.cleanup();
    process.exit(1);
  });
}

module.exports = GatewayMultiInstanceTester;
