/**
 * Created by shine on 2015/4/15.
 */
var logger = require('pomelo-logger').getLogger('__filename');
var Code = require('../../../../shared/code');
var exp = module.exports;

exp.addEvent = function(app, leagueFsm){
	addSaveEvent(app, leagueFsm);
	addEventForLeague(app, leagueFsm.league);
};

var funcSave = function(app, isflush, key, id, val, cb){
	if(isflush){
		app.get("sync").flush(key, id, val, cb);
	}else{
		app.get("sync").exec(key, id, val, cb);
	}
};

//协议更新 league-> datanode -> gamesvr->cs
var addEventForLeague = function(app, league){
	let seasonId =  league.getId();
	league.on("saveCommunity", function(isflush){
		if(isflush) {
			funcSave(app, true, "leagueSync.saveCommunity", seasonId, {uid: seasonId, e: league.community});
		}else {
			funcSave(app, false, "leagueSync.saveCommunity", seasonId, {uid: seasonId, e: league.community});
		}
	});

	league.on("saveNormal", function(isflush){
		if(isflush) {
			funcSave(app, true, "leagueSync.saveNormal", seasonId, {uid: seasonId, e: league.normal});
		}else {
			funcSave(app, false, "leagueSync.saveNormal", seasonId, {uid: seasonId, e: league.normal});
		}
	});

	league.on("saveKnockout", function(isflush){
		if(isflush) {
			funcSave(app, true, "leagueSync.saveKnockout", seasonId, {uid: seasonId, e: league.knockout});
		}else {
			funcSave(app, false, "leagueSync.saveKnockout", seasonId, {uid: seasonId, e: league.knockout});
		}
	});

	league.on("saveProfession", function(isflush){
		if(isflush) {
			funcSave(app, true, "leagueSync.saveProfession", seasonId, {uid: seasonId, e: league.profession});
		}else {
			funcSave(app, false, "leagueSync.saveProfession", seasonId, {uid: seasonId, e: league.profession});
		}
	});
};

var addSaveEvent = function(app, leagueFsm){
	var uid = leagueFsm.getId();
	leagueFsm.on("saveLeagueFsm", function(isflush){
		if(isflush) {
			funcSave(app, true, "leagueSync.saveLeagueFsm", uid, {uid: uid, e: leagueFsm});
		}else {
			funcSave(app, false, "leagueSync.saveLeagueFsm", uid, {uid: uid, e: leagueFsm});
		}
	});

	leagueFsm.on("saveEnrollTime", function(isflush){
		if(isflush) {
			funcSave(app, true, "leagueSync.saveEnrollTime", uid, {uid: uid, e: leagueFsm.enrollTime});
		}else {
			funcSave(app, false, "leagueSync.saveEnrollTime", uid, {uid: uid, e: leagueFsm.enrollTime});
		}
	});
};