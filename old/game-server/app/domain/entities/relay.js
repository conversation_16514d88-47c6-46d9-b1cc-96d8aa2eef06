/**
 * Created by sea on 2020/1/02.
 */
var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var commonEnum = require('../../../../shared/enum');
var Calc = require('../../util/calc');
var Code = require('../../../../shared/code');
var Constant = require("../../../../shared/constant");
var TimeUtils = require('../../util/timeUtils');
//赛事转播
var Relay = function (player) {
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.buyRelayTime = 0; //购买时间
    this.isJoin = 0; //是否加入赛事转播
    this.typeId = 9;//当前联赛等级
    this.ScheduleList = []; //state: 0不可领取, 1可领取, 2已领取
    this.processed = []; //已处理的轮数
};

util.inherits(Relay, EventEmitter);
module.exports = Relay;

Relay.prototype.initByDB = function (doc) {
    this.uid = doc.uid || "";
    this.buyRelayTime = doc.buyRelayTime || 0;
    this.isJoin = doc.isJoin || 0;
    this.typeId = doc.typeId || 9;
    this.ScheduleList = doc.ScheduleList || [];
    this.processed = doc.processed || [];
};

Relay.prototype.toJSONforDB = function () {
    let relayInfo = {
        uid: this.uid,
        buyRelayTime: this.buyRelayTime,
        isJoin: this.isJoin,
        typeId: this.typeId,
        ScheduleList: this.ScheduleList,
        processed: this.processed,
    };
    return relayInfo;
};
//购买转播
Relay.prototype.buyRelay = function() {

    let money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.RELAY_GOLD].Param;
    let player = this.player;
    let buyDay = TimeUtils.dayInterval(this.buyRelayTime);
    if(this.isJoin === 1 || buyDay < 7)//买过了
    {
        return Code.BUY_FAIL;
    }
    if(!player.checkMoneyIsEnough(commonEnum.PLAY_INFO.gold, money))//检查钱是否足够
    {
        logger.error('Relay.buyRelay: money not enough !', this.uid, money);
        return Code.GOLD_FALL;
    }
    this.buyRelayTime = Date.now();
    this.isJoin = 1;
    let id = commonEnum.TASK_INFO.RelayType - 1;
    player.tasks.allTaskList[id][commonEnum.TASK_INFO.taskType + id] = [];
    player.deductMoney(commonEnum.PLAY_INFO.gold, money);//扣除球币
    player.saveRelay();
    return Code.OK;
};
//检查是否过期
Relay.prototype.checkDeadline = function() {
    if(this.buyRelayTime > 0)
    {
        //logger.error("过了多少天=====================", Date.now(), this.buyRelayTime + 7 * 24 * 60 * 60 * 1000 );
        if(Date.now() > this.buyRelayTime + 7 * 24 * 60 * 60 * 1000 || this.buyRelayTime > Date.now())
        {
            //logger.error("过期发奖励==============", this.player.name);
            this.SendOutReward();
            this.player.tasks.resetRelayTask();
            //this.processed = [];
            this.player.updateRelayEndTime(this.buyRelayTime + 7 * 24 * 60 * 60 * 1000);
            //过期了
            this.buyRelayTime = 0;
            this.isJoin = 0;
            //this.player.integral = 0;
            this.typeId = 9;//当前联赛等级
            this.ScheduleList = []; //state: 0不可领取, 1可领取, 2已领取
            return true;
        }
    }
    return false;
};
//返回的赛程
Relay.prototype.getBroadcastInterface = function () {
    let ScheduleList = [];
    //logger.error("------------------------getBroadcastInterface--------------------------记录处理过的",  this.processed);
    for(let i in this.ScheduleList)
    {
        if(this.ScheduleList[i].typeId === this.typeId || this.ScheduleList[i].typeId > 6)
        {
            ScheduleList.push(this.ScheduleList[i]);
        }
    }
    return ScheduleList;
};
//得到任务列表
Relay.prototype.getTaskList = function (grepId) {
    this.typeId = grepId;
    let allTaskList = this.player.tasks.allTaskList;
    //logger.error("taskkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk", allTaskList[6].task6);
    let tmpList = [];
    let config = dataApi.allData.data["Task"];
    let taskList = [];
    for(let i in config)
    {
        if(config[i].Type === 7)
        {
            taskList.push(config[i]);
        }
    }
    if(this.isJoin === 0)//没参加显示资格赛任务
    {
        for(let i in taskList)
        {
            if(!taskList[i].Arg1 || taskList[i].Arg1 === 6)
            {
                let tmp = {
                    resId: taskList[i].Id,
                    type: taskList[i].Type,
                    Num: 0,
                    status: 0
                }
                tmpList.push(utils.deepCopy(tmp));
            }
        }
        return tmpList;
    }
    //logger.error("renwu============", this.typeId, taskList, allTaskList[6].task6);
    let flag = false;
    for(let i in this.ScheduleList)
    {
        if(this.ScheduleList[i].typeId >= 6)
        {
            flag = true
        }
    }
    for(let i in taskList)
    {
        if(!taskList[i].Arg1 || taskList[i].Arg1 === this.typeId || (flag && taskList[i].Arg1 === 6))
        {
            for(let k in allTaskList[6].task6)
            {
                if(allTaskList[6].task6[k].resId === taskList[i].Id)
                {
                    tmpList.push(utils.deepCopy(allTaskList[6].task6[k]));
                }
            }
        }
    }
    return tmpList;
};
//更新自己的联赛数据
Relay.prototype.updataScheduleData = function (HistoryInfo, grepId, flag) {
    //logger.error("更新联赛数据=============", grepId, this.typeId, HistoryInfo.length);
    if(this.isJoin === 1)
    {
        let config = dataApi.allData.data["Task"];
        let taskList = [];
        for(let i in config)
        {
            if(config[i].Type === 7)
            {
                taskList.push(config[i]);
            }
        }
        if(!grepId)
        {
            grepId = this.typeId;
        }
        this.typeId = grepId;
        let type = this.typeId;
        if(type > 6)
        {
            type = 6;
        }
        // if(HistoryInfo.length === 0)
        // {
        //     type = grepId;
        //     this.SendOutReward();
        //     this.player.tasks.resetRelayTask();
        //     this.processed = [];
        // }
        for(let i in taskList)
        {
            if((taskList[i].Arg1 === 0 || taskList[i].Arg1 === type) && this.isJoin === 1)
            {
                //logger.error("添加任务~~~~~~~~~~~~~", type, taskList[i].Id, taskList[i].Describe);
                this.player.tasks.addTask(taskList[i].Id);//添加任务
            }
        }
    }
    let tmpList = [];
    if(!!this.ScheduleList)
    {
        tmpList = utils.cloneArray(this.ScheduleList);
        this.ScheduleList = [];
    }

    for(let i in HistoryInfo)
    {
        let state = 0;
        if(HistoryInfo[i].status === 2 && HistoryInfo[i].beginTime > this.buyRelayTime && this.buyRelayTime !== 0)//打过了就变为可领取
        {
            state = 1;
        }
        for(let k in tmpList)
        {
            if(tmpList[k].roundId === HistoryInfo[i].roundId && HistoryInfo[i].typeId === tmpList[k].typeId && tmpList[k].state === 2)//原来领过的状态不变
            {
                state = tmpList[k].state;
                tmpList.splice(k, 1);
            }
        }
        let tmp = {
        typeId: HistoryInfo[i].typeId,
        groupId:  HistoryInfo[i].groupId,
        roundId:  HistoryInfo[i].roundId,
        beginTime:  HistoryInfo[i].beginTime,
        teamA:  HistoryInfo[i].teamA,
        teamB:  HistoryInfo[i].teamB,
        teamAScore:  HistoryInfo[i].teamAScore,
        teamBScore:  HistoryInfo[i].teamBScore,
        status:  HistoryInfo[i].status,
        isNull:  HistoryInfo[i].isNull,
        seasonId: HistoryInfo[i].seasonId,
        state: state,
        };
        this.ScheduleList.push(utils.deepCopy(tmp));
    }
    //logger.error("/****************************************************", this.buyRelayTime, this.ScheduleList, this.processed);
    if(this.isJoin === 1)
    {
        for(let i in this.ScheduleList)
        {
            let tmp = this.ScheduleList[i];
            if(tmp.teamA.playerUid === this.uid)
            {
                if((tmp.teamAScore > tmp.teamBScore || tmp.teamB.playerUid === "") && tmp.beginTime > this.buyRelayTime)//赢了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.WIN_LEAGIE);//加胜利次数
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamAScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
                if(tmp.teamAScore < tmp.teamBScore && tmp.beginTime > this.buyRelayTime)//输了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamAScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
                if(tmp.teamAScore === tmp.teamBScore && tmp.beginTime > this.buyRelayTime)//平了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamAScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
            }
            else if(tmp.teamB.playerUid === this.uid)
            {
                if(tmp.teamAScore > tmp.teamBScore && tmp.beginTime > this.buyRelayTime)//输了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamBScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
                if((tmp.teamAScore < tmp.teamBScore || tmp.teamA.playerUid === "") && tmp.beginTime > this.buyRelayTime)//赢了了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.WIN_LEAGIE);//加胜利次数
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamBScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
                if(tmp.teamAScore === tmp.teamBScore && tmp.beginTime > this.buyRelayTime)//平了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamBScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
            }
        }
    }
    if(flag)
    {
        this.checkDeadline();
    }
    // logger.error("=========================2", this.player.playerId, this.ScheduleList);
    // this.player.saveRelay();
};
//离线事件更新自己的联赛数据
Relay.prototype.updataScheduleDataEvent = function (HistoryInfo, msg) {
    //logger.error("离线事件更新自己的联赛数据=============", msg, this.typeId, HistoryInfo);
    let tmpList = [];
    if(!!this.ScheduleList)
    {
        tmpList = utils.cloneArray(this.ScheduleList);
        this.ScheduleList = [];
    }
    for(let i in HistoryInfo)
    {
        let state = 0;
        if(HistoryInfo[i].status === 2 && HistoryInfo[i].beginTime > this.buyRelayTime)//打过了就变为可领取
        {
            state = 1;
        }
        for(let k in tmpList)
        {
            if(tmpList[k].roundId === HistoryInfo[i].roundId && HistoryInfo[i].typeId === tmpList[k].typeId && tmpList[k].state === 2)//原来领过的状态不变
            {
                state = tmpList[k].state;
                tmpList.splice(k, 1);
            }
        }
        let tmp = {
            typeId: HistoryInfo[i].typeId,
            groupId:  HistoryInfo[i].groupId,
            roundId:  HistoryInfo[i].roundId,
            beginTime:  HistoryInfo[i].beginTime,
            teamA:  HistoryInfo[i].teamA,
            teamB:  HistoryInfo[i].teamB,
            teamAScore:  HistoryInfo[i].teamAScore,
            teamBScore:  HistoryInfo[i].teamBScore,
            status:  HistoryInfo[i].status,//commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.END,
            isNull:  HistoryInfo[i].isNull,
            seasonId: HistoryInfo[i].seasonId,
            state: state,
        };
        this.ScheduleList.push(utils.deepCopy(tmp));
    }
    //logger.error("****************************************************", this.buyRelayTime, this.ScheduleList, this.processed);
    // if(this.isJoin === 1)
    // {
        for(let i in this.ScheduleList)
        {
            let tmp = this.ScheduleList[i];
            if(tmp.teamA.playerUid === this.uid)
            {
                if((tmp.teamAScore > tmp.teamBScore || tmp.teamB.playerUid === "") && tmp.beginTime > this.buyRelayTime)//赢了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.WIN_LEAGIE);//加胜利次数
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamAScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
                if(tmp.teamAScore < tmp.teamBScore && tmp.beginTime > this.buyRelayTime)//输了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamAScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
                if(tmp.teamAScore === tmp.teamBScore && tmp.beginTime > this.buyRelayTime)//平了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamAScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
            }
            else if(tmp.teamB.playerUid === this.uid)
            {
                if(tmp.teamAScore > tmp.teamBScore && tmp.beginTime > this.buyRelayTime)//输了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamBScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
                if((tmp.teamAScore < tmp.teamBScore || tmp.teamA.playerUid === "") && tmp.beginTime > this.buyRelayTime)//赢了了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.WIN_LEAGIE);//加胜利次数
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamBScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
                if(tmp.teamAScore === tmp.teamBScore && tmp.beginTime > this.buyRelayTime)//平了
                {
                    if(!this.checkProcessed(tmp))//检查是否已处理过
                    {
                        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.GOAL_LEAGUE, 0, 0, tmp.teamBScore);//进球任务
                        this.processed.push(utils.deepCopy(tmp));//记录此轮已处理
                    }
                }
            }
        }
    // this.finalupData(msg);
    // this.checkDeadline();
    //}
};
//领取奖励
Relay.prototype.receiveAward = function (msg) {
    let round = msg.msg;
    let typeId = msg.typeId;
    let config = dataApi.allData.data["LeagueRelayReward"];
    let awardConfig = config[this.typeId];
    if(this.typeId < 9 && this.typeId >= 6)
    {
        awardConfig = config[6];
    }
    let awardId = 0;
    let awardNum = 0;
    let result = {
        Code: Code.OK,
        awardId: awardId,
        awardNum: awardNum,
    }
    if(this.isJoin !== 1)//没加入
    {
        return result;
    }
    //logger.error("-------------领取奖励----------------", awardConfig);
    for(let i in this.ScheduleList)
    {
        if(this.ScheduleList[i].roundId === round && this.ScheduleList[i].typeId === typeId && this.ScheduleList[i].state === 1)//可领取状态
        {
            let tmp = this.ScheduleList[i];
            if(tmp.teamA.playerUid === this.uid)
            {
                if(tmp.teamAScore > tmp.teamBScore || tmp.teamB.playerUid === "")//赢了
                {
                    awardId = awardConfig["Reward3"];
                    awardNum = awardConfig["Num3"];
                }
                else if(tmp.teamAScore < tmp.teamBScore)//输了
                {
                    awardId = awardConfig["Reward1"];
                    awardNum = awardConfig["Num1"];
                }
                else if(tmp.teamAScore === tmp.teamBScore)//平了
                {
                    awardId = awardConfig["Reward2"];
                    awardNum = awardConfig["Num2"];
                }
            }
            else if(tmp.teamB.playerUid === this.uid)
            {
                if(tmp.teamAScore > tmp.teamBScore)//输了
                {
                    awardId = awardConfig["Reward1"];
                    awardNum = awardConfig["Num1"];
                }
                else if(tmp.teamAScore < tmp.teamBScore || tmp.teamA.playerUid === "")//赢了了
                {
                    awardId = awardConfig["Reward3"];
                    awardNum = awardConfig["Num3"];
                }
                else if(tmp.teamAScore === tmp.teamBScore)//平了
                {
                    awardId = awardConfig["Reward2"];
                    awardNum = awardConfig["Num2"];
                }
            }
            //发奖励
            this.player.addResource(commonEnum.PLAY_INFO.cash, awardNum);
            this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: this.player.cash}]); //欧元变更通知
            this.ScheduleList[i].state = 2;//发完奖励改为以领取
            result.awardId = awardId;
            result.awardNum = awardNum;
            return result;
        }
    }
    return result;
};
//一键领取奖励
Relay.prototype.receiveAllAward = function () {

    let config = dataApi.allData.data["LeagueRelayReward"];
    let awardConfig;
    let awardId = 0;
    let awardNum = 0;
    let tempAward = {
        awardId: awardId,
        awardNum: awardNum,
    }
    let result = [];
    if(this.isJoin !== 1)//没加入
    {
        return result;
    }
    //logger.error("-------------领取奖励----------------", awardConfig);
    for(let i in this.ScheduleList)
    {
        if(this.ScheduleList[i].typeId >= 6)
        {
            awardConfig = config[6];
        }
        else
        {
            awardConfig = config[this.ScheduleList[i].typeId]
        }
        if(this.ScheduleList[i].state === 1)//可领取状态
        {
            let tmp = this.ScheduleList[i];
            if(tmp.teamA.playerUid === this.uid)
            {
                if(tmp.teamAScore > tmp.teamBScore || tmp.teamB.playerUid === "")//赢了
                {
                    awardId = awardConfig["Reward3"];
                    awardNum = awardConfig["Num3"];
                }
                else if(tmp.teamAScore < tmp.teamBScore)//输了
                {
                    awardId = awardConfig["Reward1"];
                    awardNum = awardConfig["Num1"];
                }
                else if(tmp.teamAScore === tmp.teamBScore)//平了
                {
                    awardId = awardConfig["Reward2"];
                    awardNum = awardConfig["Num2"];
                }
            }
            else if(tmp.teamB.playerUid === this.uid)
            {
                if(tmp.teamAScore > tmp.teamBScore)//输了
                {
                    awardId = awardConfig["Reward1"];
                    awardNum = awardConfig["Num1"];
                }
                else if(tmp.teamAScore < tmp.teamBScore || tmp.teamA.playerUid === "")//赢了了
                {
                    awardId = awardConfig["Reward3"];
                    awardNum = awardConfig["Num3"];
                }
                else if(tmp.teamAScore === tmp.teamBScore)//平了
                {
                    awardId = awardConfig["Reward2"];
                    awardNum = awardConfig["Num2"];
                }
            }
            //发奖励
            this.player.addResource(commonEnum.PLAY_INFO.cash, awardNum);
            this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: this.player.cash}]); //欧元变更通知
            this.ScheduleList[i].state = 2;//发完奖励改为以领取
            tempAward.awardId = awardId;
            tempAward.awardNum = awardNum;
            let flag = true;
            for(let k in result)
            {
                if(result[k].awardId === tempAward.awardId)
                {
                    result[k].awardNum += tempAward.awardNum;
                    flag = false;
                }
            }
            if(flag) result.push(utils.deepCopy(tempAward));
        }
    }
    return result;
};
//检查轮数是否已处理过
Relay.prototype.checkProcessed = function (Schedule){
    // logger.error("检查轮数是否已处理过==============", this.processed);
    if(Schedule.status !== commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.END || Schedule.beginTime < this.buyRelayTime || Schedule.beginTime > this.buyRelayTime * 7 * 24 * 60 * 60 * 1000)//还没打不做处理
    {
        return true;
    }
    let round = Schedule.roundId;
    let seasonId = Schedule.seasonId;
    let typeId = Schedule.typeId;
    for(let i in this.processed)
    {
        if(this.processed[i].seasonId < seasonId)
        {
            this.processed.splice(i, 1);
            continue;
        }
       
        if(this.processed[i].typeId === typeId && this.processed[i].roundId === round && this.processed[i].seasonId === seasonId)
        {
            return true;
        }
    }
    return false;
};
//联赛结束获得最终排名和所在的联赛等级
Relay.prototype.finalupData = function (msg){
    let player = this.player;

    this.SendOutReward();//赛季结束发未领取福利奖励
    this.player.tasks.resetRelayTask();//赛季结束成就任务发放奖励并重置
};
//赛事转播最终排名任务处理
Relay.prototype.finalTask = function (msg){
    //logger.error("赛事转播最终排名任务处理!!!!!!!!!赛事转播最终排名任务处理!!!!!!!!!", msg, this.ScheduleList);
    let player = this.player;
    if(msg.playerId === this.uid)
    {
        for(let i in this.ScheduleList)
        {
            let tmp = this.ScheduleList[i].typeId;
            if(tmp < msg.typeId)
                tmp = msg.typeId;
            if(tmp > 6)
                tmp = 6;
            player.tasks.triggerTask(commonEnum.TARGET_TYPE.PROMOTED_LEAGUE, tmp, 0, 0);//当前赛季完成38轮
        }

        switch (msg.rank) {
            case 1:
                //获得冠军
                player.tasks.triggerTask(commonEnum.TARGET_TYPE.CHAMPION_LEAGUE, msg.typeId, 0, 0);
                break;
        }
    }
};
//商城兑换
Relay.prototype.convertibility = function (msg) {
    let id = msg.Id;
    let Num = msg.Num;
    let config = dataApi.allData.data["LeagueShop"][id];
    let money = config.Price * Num;
    let result = {
        Code: Code.OK,
        awardId: 0,
        awardNum: 0,
        integral: this.player.integral,
    };
    //logger.error("=============兑换商城==============", msg, config, money, this.player.integral, this.player.checkResourceIsEnough(config.PriceType, money));
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.integral, money))//检查转播积分是否充足
    {
        result.Code = Code.INTEGRAL_FALL;
        return result;
    }
    this.player.subtractResource(commonEnum.PLAY_INFO.integral, money);//扣除积分
    this.player.bag.addItem(config.Parameters, Num);//给物品
    result.Code = Code.OK;
    result.awardId = config.Parameters;
    result.awardNum = Num;
    result.integral = this.player.integral;
    return result;
};
//赛季结束或者过期发送未领取的奖励
Relay.prototype.SendOutReward = function () {
    //logger.error("发福利奖励================1", this.ScheduleList);
    if(this.isJoin !== 1)
        return;
    let config = dataApi.allData.data["LeagueRelayReward"];
    let awardId = 1;
    let awardNum = 0;
    let awardList = [];
    for(let i in this.ScheduleList)
    {
        let tmp = this.ScheduleList[i];
        let awardConfig = config[tmp.typeId];
        if(tmp.typeId > 6)
        {
            awardConfig = config[6];
        }
        if(tmp.state === 1)
        {
            let tmp = this.ScheduleList[i];
            if(tmp.teamA.playerUid === this.uid)
            {
                if(tmp.teamAScore > tmp.teamBScore || tmp.teamB.playerUid === "")//赢了
                {
                    awardId = awardConfig["Reward3"];
                    awardNum = awardConfig["Num3"];
                }
                else if(tmp.teamAScore < tmp.teamBScore)//输了
                {
                    awardId = awardConfig["Reward1"];
                    awardNum = awardConfig["Num1"];
                }
                else if(tmp.teamAScore === tmp.teamBScore)//平了
                {
                    awardId = awardConfig["Reward2"];
                    awardNum = awardConfig["Num2"];
                }
            }
            else if(tmp.teamB.playerUid === this.uid)
            {
                if(tmp.teamAScore > tmp.teamBScore)//输了
                {
                    awardId = awardConfig["Reward1"];
                    awardNum = awardConfig["Num1"];
                }
                else if(tmp.teamAScore < tmp.teamBScore || tmp.teamA.playerUid === "")//赢了了
                {
                    awardId = awardConfig["Reward3"];
                    awardNum = awardConfig["Num3"];
                }
                else if(tmp.teamAScore === tmp.teamBScore)//平了
                {
                    awardId = awardConfig["Reward2"];
                    awardNum = awardConfig["Num2"];
                }
            }
            let award = {awardId: awardId, awardNum: awardNum};
            awardList.push(utils.deepCopy(award));
            this.ScheduleList[i].state = 2;
        }
    }
    
    let rewardList = [];
    for(let i in awardList)
    {
        let flag = true;
        for(let k in rewardList)
        {
            if(rewardList[k].ResId === awardList[i].awardId)
            {
                rewardList[k].Num += awardList[i].awardNum;
                flag = false;
            }
        }
        if(flag)
        {
            let tmp = {
                ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
                ResId: awardList[i].awardId,
                Num: awardList[i].awardNum,
            }
            rewardList.push(utils.deepCopy(tmp));
        }
    }
    
    //发邮件
    let specialAttachInfo = {
        roomUid: ""
    };
    if(rewardList.length !== 0)
    {
        this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.RELAY_SCHEDULE, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
        this.player.saveEMail();
    }
};