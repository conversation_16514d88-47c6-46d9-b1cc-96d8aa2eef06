var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');

var utils = require('../../util/utils');
var dataApi = require('../../util/dataApi');
var Player = require('./player');
var TeamFormations = require('./teamFormations');
var Heros = require('./heros');
var commonEnum = require('../../../../shared/enum');

//BattleTeam数据结构
var BattleTeam = function(playerId, type, teamSide, param){
    this.playerId = playerId;
    this.player = {};
    this.type = type;                        //
    this.teamResId = 0;                      //球队表ID: player为0
    if(param && param.teamResId) {
        this.teamResId = param.teamResId;
    }
    //推图配置专用数据, 奖励用
    this.leagueId = 0;
    if(param && param.leagueId) {
        this.leagueId = param.leagueId;
    }
    this.takeCopyRewardProcess = 0;
    if(param && param.takeCopyRewardProcess) {
        this.takeCopyRewardProcess = param.takeCopyRewardProcess;
    }
    //
    this.fixFormationUid = "";
    this.teamSide = teamSide;                //teamA, teamB
    this.teamName = "";
    if(param && param.teamName) {
        this.teamName = param.teamName;
    }
    this.formationUid = "";
    this.score = 0;                          //得分
    //this.teamFormations = {};
    this.attr = {
        morale : 0,                     //团队士气值
        moraleAcceleration : 0,         //士气槽填充速度
        moraleSlot : 0,                 //气槽值
        attackTacticID : 0,             //攻击策略ID
        defendTacticID : 0              //防守策略ID
    };
    this.roundInfo = {
        attackerType : "",
        //A1， A2， B , GK 进攻球员1,2 防守球员, 守门员信息:
        A1 : {heroUid: "", attrType1: 0, attrValue1: 0, attrType2: 0, attrValue2: 0},
        A2 : {heroUid: "", attrType1: 0, attrValue1: 0, attrType2: 0, attrValue2: 0},
        B : {heroUid: "", attrType1: 0, attrValue1: 0, attrType2: 0, attrValue2: 0},
        GK : {heroUid: "", attrType1: 0, attrValue1: 0, attrType2: 0, attrValue2: 0},
        periodInfo : [
            //动作ID， 启动评论, 结果评论, 成功率, 结果(1成功, 2失败)
            {actionID : 0, startCommentID : 0, resultCommentID : 0, percent: 0, result : 0},
            {actionID : 0, startCommentID : 0, resultCommentID : 0, percent: 0, result : 0},
            {actionID : 0, startCommentID : 0, resultCommentID : 0, percent: 0, result : 0}
        ],
        attackMode : 0
    };
    //统计信息: 射门次数, 突破次数, 突破成功次数, 定位球次数, 球员评分(Uid, Score -- 一一对应)
    //goalEvenetMap 进球事件列表 只需要存一份，放在teamA上
    this.statistic = {
        shotNum : 0,
        breakNum : 0,
        breakSucNum : 0,
        placeKickNum : 0,
        goalEventMap : {},
        ballerScoreMap : {}
    };
    //瞬时性技能列表  instantSkillMap
    //持续性技能列表  durativeSkillMap
    //下一次进攻技能列表  nextAtkSkillMap
    this.allSkillList = {
        instantSkillMap : new Map(),
        durativeSkillMap : new Map(),
        nextAtkSkillMap : new Map()
    };

    this.businessRewardInfo = {
		totalCash: 0,
		winCash: 0,
		loseCash: 0,
        drawCash: 0,
        matchWinRatio: 0,
		matchLoseRatio: 0,
		matchDrawRatio: 0,
        enemyFansCount: 0,
        myFansCount: 0,
    };
    //技能结构

    //SkillMap结构: key: heroUid   value: skillList
    //SkillList结构: key: skillResId    value: {skillInfo, buffList}
    //buffList结构: buffInfo数组

    //skillInfo结构
    //type: 1: 瞬时性技能instantSkill 2: 持续性技能durativeSkill 3: 下一次攻击性技能nextAtkSkill
    //continueTime: 持续时间 (秒)
    //radio: 触发几率
    //skillType: 0.激活  1.被动  2.几率
    //startTime: 技能触发开始时间
    //endTime: 技能结束时间

    //buffInfo结构
    //对应策划表: 3个参数
    //opType: 触发类型
    //effectType: 效果类型
    //value: 参数
    //效果判定
    //period: 触发阶段: 0:士气槽阶段 1:突破 2:射门 3:突破+射门
    //attackMode: 触发进攻方式
    //effectTeamSide: 生效哪一方
    //atkTeamSide: 进攻方
};

util.inherits(BattleTeam, EventEmitter);

module.exports = BattleTeam;

