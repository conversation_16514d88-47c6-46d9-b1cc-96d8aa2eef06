var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var utils = require("../../util/utils");
var commonEnum = require("../../../../shared/enum");
var Calc = require("../../util/calc");
var Code = require("../../../../shared/code");
var Constant = require("../../../../shared/constant");
var TimeUtils = require("../../util/timeUtils");

var MatchRank = function(globalRank, rankLimit) 
{      
    this.globalRank = globalRank;
    this.rankLimit = rankLimit;
    this.rank = [];
    this.accountList = new Map();          //uid => rankObj;
    this.actualStrengthDiffValue = [];
    this.totalTimes = 0;//免费挑战次数上限
    this.totalpassTimes = 0;//被动挑战次数上限
    this.totalbuyTimes = 0;//购买次数上限
    this.fightTimesList = new Map();
    this.lastUpdateFightTimeList =[];
};

util.inherits(MatchRank, EventEmitter);
module.exports = MatchRank;

MatchRank.prototype.initByConfig = function() 
{
    let value1 = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchActualStrengthDiffValueOne].Param;
    let value2 = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchActualStrengthDiffValueTwo].Param;
    let value3 = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchActualStrengthDiffValueThrid].Param;
    let value4 = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchActualStrengthDiffValueFour].Param;
    let value5 = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchActualStrengthDiffValueFive].Param;
    let value6 = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchActualStrengthDiffValueSix].Param;
    this.totalTimes = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchFreeFightTimes].Param;//每日免费挑战次数上限
    this.totalpassTimes = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchEveryDayFightTimes].Param;//每日被挑战次数上限
    this.totalbuyTimes = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchBuyFightTimes].Param;//每日购买次数上限
    this.actualStrengthDiffValue.push(value1);
    this.actualStrengthDiffValue.push(value2);
    this.actualStrengthDiffValue.push(value3);
    this.actualStrengthDiffValue.push(value4);
    this.actualStrengthDiffValue.push(value5);
    this.actualStrengthDiffValue.push(value6);
    //logger.info("initByConfig", this.actualStrengthDiffValue);
};

MatchRank.prototype.test = function()
{

};

MatchRank.prototype.setFightTimesList = function(uid, obj)
{
    this.fightTimesList.set(uid, obj);
};

MatchRank.prototype.getFightTimesList  = function(uid)
{
    //logger.debug("getFightTimesList obj: ", this.fightTimesList.get(uid));
    if(!this.fightTimesList.has(uid)) {
        this.flushFightTimes(uid);
    }
    return this.fightTimesList.get(uid);
};

MatchRank.prototype.hasUidInFightList = function(uid)
{
    return this.fightTimesList.has(uid);
};

MatchRank.prototype.getFightTimesByUid = function(uid, flag)
{
    let obj = this.getFightTimesList(uid);
    if (!obj)
    {
        return 0;
    }
    if(flag)//为真返回主动挑战次数，否则返回被动挑战次数
    {
        return obj.fightTimes;
    }
    else
    {
        return obj.passiveTimes;
    }

};

MatchRank.prototype.getTotalFightTimes = function()//得到免费挑战次数上限
{
    return this.totalTimes;
};
MatchRank.prototype.gettotalpassTimes = function()//得到被动挑战次数上限
{
    return this.totalpassTimes;
};
MatchRank.prototype.gettotalbuyTimes = function()//得到购买挑战次数上限
{
    return this.totalbuyTimes;
};

MatchRank.prototype.incrementFightTime = function(uid)//挑战次数加一
{
    if (!this.hasUidInFightList(uid))
    {
        this.flushFightTimes(uid);
    }

    let obj = this.getFightTimesList(uid);
    //logger.info("incrementFightTime before", uid, obj.fightTimes);
    obj.fightTimes = obj.fightTimes + 1;
    //logger.info("incrementFightTime after", uid, obj.fightTimes);
    this.addLastFightTimesList(uid);
};
//得到所有次数还剩多少
MatchRank.prototype.getAllTimes = function(uid)
{
    if (!this.hasUidInFightList(uid))
    {
        this.flushFightTimes(uid);
    }
    let obj = this.getFightTimesList(uid);
    var fightTimes = this.totalTimes - obj.fightTimes;
    var passivityTimes = this.totalpassTimes - obj.passiveTimes;
    var buyTimes = this.totalbuyTimes -  obj.buyTimes;
    return {fightTimes: fightTimes, passivityTimes: passivityTimes, buyTimes: buyTimes};
}

MatchRank.prototype.BuyFightTimes = function(uid)//购买挑战次数
{
    if (!this.hasUidInFightList(uid))
    {
        this.flushFightTimes(uid);
    }
    let obj = this.getFightTimesList(uid);
    if(obj.totalbuyTimes - obj.buyTimes <= 0 || obj.fightTimes <= 0)//大于购买上限
    {
        return false;
    }
    else
    {
        obj.buyTimes = obj.buyTimes + 1;
        obj.fightTimes = obj.fightTimes - 1;
        return true;
    }
};

MatchRank.prototype.getBuyTimes = function(uid)//得到购买次数
{
    let obj = this.getFightTimesList(uid);
    if (!obj)
    {
        return 0;
    }
    return obj.buyTimes;
};

MatchRank.prototype.resetWeekFansRank = function()//重置周最高排名
{
    for(let [k, v]  of this.fightTimesList)
    {
        v.weekFansRank = 0;
    }
};

MatchRank.prototype.getWeekFansRank = function(uid)//得到周最高排名
{
    let obj = this.getFightTimesList(uid);
    if (!obj)
    {
        return 0;
    }
    return obj.weekFansRank;
};

MatchRank.prototype.setWeekFansRank = function(uid, rank)//周最高排名改变
{
    if (!this.hasUidInFightList(uid))
    {
        this.flushFightTimes(uid);
    }
    let obj = this.getFightTimesList(uid);
    obj.weekFansRank = rank;
    this.addLastFightTimesList(uid);
};

MatchRank.prototype.incrementPassiveTimes = function(uid)//被动挑战次数加一
{
    if (!this.hasUidInFightList(uid))
    {
        this.flushFightTimes(uid);
    }
    let obj = this.getFightTimesList(uid);
    obj.passiveTimes = obj.passiveTimes + 1;
    this.addLastFightTimesList(uid);
};

MatchRank.prototype.checkUpdateFightTimes = function(uid)
{
    if (!this.hasUidInFightList(uid))
    {
        this.flushFightTimes(uid);
        return true;
    }

    let obj = this.getFightTimesList(uid);
    if (!TimeUtils.isToday(obj.lastUpdateTimes))
    {
        this.flushFightTimes(uid);
        return true;
    }

    return false;
};

MatchRank.prototype.addLastFightTimesList = function(uid)
{
    this.lastUpdateFightTimeList.push(uid);
};

MatchRank.prototype.initPlayerByDB = function(doc) 
{
    let uid = doc.uid;
    if (!uid)
    {
        logger.error("initByDB not found uid");
        return;
    }

    let newObj = {
        fightTimes: doc.fightTimes || 0,                //主动挑战次数
        passiveTimes: doc.passiveTimes || 0,            //被动挑战次数
        buyTimes: doc.buyTimes || 0,                    //购买次数
        lastUpdateTimes: doc.lastUpdateTimes || 0,      //下次刷新时间
        weekFansRank: doc.weekFansRank || 0,  //周粉丝最高排名
    }

    this.setFightTimesList(uid, newObj);
    this.addLastFightTimesList(uid);
};

MatchRank.prototype.toJSONforDB = function(){
	var doc = {
		fightTimesList: this.makeLastList(),
	};
	return doc;
};

MatchRank.prototype.makeLastList =function()
{   
    let arr = [];
    for (let index in this.lastUpdateFightTimeList) {
       let uid = this.lastUpdateFightTimeList[index];
        let obj = this.getFightTimesList(uid);
        let newObj = {
            uid: uid,
            fightTimes: obj.fightTimes,
            passiveTimes: obj.passiveTimes,
            buyTimes: obj.buyTimes,
            lastUpdateTimes: obj.lastUpdateTimes || 0,
            weekFansRank: obj.weekFansRank || 0,  //周粉丝最高排名
        };
        arr.push(newObj);
    }
    
    return arr;
};

MatchRank.prototype.checkFightTimesList = function()
{
    let count = 0;
    for (let index in this.rank) {
        const data = this.rank[index];
        const uid = data.uid;
        if (!this.hasUidInFightList(uid)) //不存在
        {
            this.flushFightTimes(uid);
            count ++;
            continue;
        }

        let obj = this.fightTimesList.get(uid);
        if (!TimeUtils.isToday(obj.lastUpdateTimes || 0)) //跨天
        {
            this.flushFightTimes(uid);
            count ++;
        }
    }

    //logger.info("checkFightTimesList", count);
};

MatchRank.prototype.resetLastTimesList = function()
{
    this.lastUpdateFightTimeList = [];
};

MatchRank.prototype.makeNewRankFightObj = function()
{
    let newObj = {
        fightTimes: 0,
        passiveTimes: 0,
        buyTimes: 0,
        lastUpdateTimes: TimeUtils.now(),
        weekFansRank: 0
    };
    return newObj;
};

MatchRank.prototype.flushFightTimes = function(uid)
{
    let newObj = this.makeNewRankFightObj();
    this.setFightTimesList(uid, newObj);
    this.addLastFightTimesList(uid);
};

MatchRank.prototype.toMap = function(arr)
{
    var map =  new Map();
    if (!arr)
    {
        return map;
    }

    for (var i in arr)
    {
       const object =  arr[i];
       var uid = object["uid"];
       var obj = {
            fightTimes: object["fightTimes"],
            passiveTimes: object["passiveTimes"],
            buyTimes: object["buyTimes"],
            lastUpdateTimes: object["lastUpdateTimes"],
       };
       map.set(uid, obj);
    }
    
    return map;
};

MatchRank.prototype.toArray = function(map) {
    var arr = [];
    if (!map)
    {
        return arr;
    }
    
    for(let [uid, obj] of map)
    {
        if (!uid) continue;

        let newObj = {
            uid: uid,
            fightTimes : obj.fightTimes,
            passiveTimes: obj.passiveTimes,
            buyTimes: obj.buyTimes,
            lastUpdateTimes : obj.lastUpdateTimes || 0,
        };
        arr.push(newObj);
    }

    return arr;
};

//战力排行
function __power_compare_func(rankObj1, rankObj2) 
{
    let groundOpenStatus1 = rankObj1.groundOpenStatus;
    let groundOpenStatus2 = rankObj2.groundOpenStatus;
    let actualStrength1 = rankObj1.actualStrength;
    let level1  = rankObj1.level;
    let actualStrength2 = rankObj2.actualStrength;
    let level2  = rankObj2.level;

    //开放状态
    if (groundOpenStatus1 !== groundOpenStatus2 ) { 
        //降序
        if (groundOpenStatus1 < groundOpenStatus2) {
            return 1;
        }else if (groundOpenStatus1 > groundOpenStatus2) {
            return -1;
        }
    }	

    //战力
    if (actualStrength1 !== actualStrength2 ) { 
        //降序
        if (actualStrength1 < actualStrength2) {
            return 1;
        }else if (actualStrength1 > actualStrength2) {
            return -1;
        }
    }	

    //等级
    if (level1 !== level2) {
        //降序
        if (level1 < level2) {
            return 1;
        }else if (level1 > level2) {
            return -1;
        }
    }

    return 0;
}

MatchRank.prototype.init = function(accountList) 
{
    if (!accountList || accountList.length <= 0)
    {
        logger.error("MatchRank: init not account data");
        return;
    }

    let count = 0;
    //for (let idx in accountList) {
    for (let [idx, data] of accountList) {
        //let data = accountList[idx];
        if (commonEnum.FOOTBALL_GROUND_STATUS.CLOSE === data.groundOpenStatus)
        {
            continue;
        }

        let obj = {
            uid: data.uid,
            level: data.level,
            actualStrength: data.actualStrength,
            groundOpenStatus: data.groundOpenStatus,
        };

        //logger.info("init", obj.uid, obj, data);
        count++;
        this.accountList.set(obj.uid, obj);
    }

    this.sort();
    //logger.info("MatchRank init: accountList count", count);
};

MatchRank.prototype.updateRank = function(uid, level, actualStrength, status)
{
    let rankObj = {
        uid: uid,
        level: level,
        actualStrength: actualStrength,
        groundOpenStatus: status,
    };

    if (commonEnum.FOOTBALL_GROUND_STATUS.CLOSE === status)
    {
        return;
    }
    //logger.info("MatchRank updateRank: uid, level, actualStrength, groundOpenStatus", uid, level, actualStrength, groundOpenStatus);

    //1.是否超过最大限制
    if (!this.accountList.has(uid) && this.rank.length < this.rankLimit)
    {
        this.flushFightTimes(uid);
        this.accountList.set(uid, rankObj);
        this.sort();
        //logger.info("updateRank", uid, rankObj, this.getRank(uid));
        return;
    }

    //2.是否存在于当前榜单中
    if (this.accountList.has(uid)) 
    {
        let data = this.accountList.get(uid);
        let rank = data.rank;
        let rankObj =  this.rank[rank - 1];
        let isUpdate = false;

        if (rankObj.level !== level)
        {
            rankObj.level = level;
            isUpdate =  true;
        }

        if (rankObj.actualStrength !== actualStrength)
        {
            rankObj.actualStrength = actualStrength;
            isUpdate =  true;
        }

        if (rankObj.groundOpenStatus !== status)
        {
            rankObj.groundOpenStatus = status;
            isUpdate = true;
        }

        if (isUpdate)
        {
            this.sort();
            //logger.info("updateRank", uid, this.getRank(uid));
        }

        let obj = this.getFightTimesList(uid);
        if(!obj.lastUpdateTimes) {
            obj.lastUpdateTimes = 0;
        }
        if (!TimeUtils.isToday(obj.lastUpdateTimes))
        {
            this.flushFightTimes(uid);
        }
        return;
    }

    //3.未在榜单中。与最后一名比较。如果大于最后一名就进榜，小于就无法进榜
    let lastOneRank = this.rank.length - 1;
    let lastRankObj = this.rank[lastOneRank];
    let lastLevel = lastRankObj.level;
    let lastActualStrength = lastRankObj.actualStrength;
    if ( lastActualStrength < actualStrength && level > lastLevel) //可以进榜最低条件
    {
        this.accountList.set(uid, rankObj);
        this.sort();
        //logger.info("updateRank", uid, this.getRank(uid));
    }else
    {
        //logger.info("MatchRank: not go rank", uid);
    }

    if (!this.hasUidInFightList(uid)) //不存在，刷新一次
    {
        this.flushFightTimes(uid);
    }else
    {
        let obj = this.getFightTimesList(uid);  //存在,但是过期了
        if (!TimeUtils.isToday(obj.lastUpdateTimes || 0))
        {
            this.flushFightTimes(uid);
        }
    }
};

MatchRank.prototype.sort = function()
{
    let tmpRankArr = [];
    for(let [k,v] of this.accountList)
    {
        //logger.info("k,v", k, v);
        tmpRankArr.push(utils.deepCopy(v));
    }
    tmpRankArr.sort(__power_compare_func);
    //logger.info("k,v",tmpRankArr);
    this.rank = [];
    this.accountList.clear();
    let rank = 1;
    for (let idx in tmpRankArr) {
        if (rank > this.rankLimit) //超过限制，退出
        {
            break; 
        }

        let data = tmpRankArr[idx];
        data.rank = rank;
        this.accountList.set(data.uid, data);
        this.rank.push(data);
        //logger.info("MatchRank sort: ", rank, data.uid, data.actualStrength, data.level);
        rank++;
    }
    //logger.info("rank", this.rank);
};

MatchRank.prototype.getRank = function(uid)
{
    let rank = 0;
    if (!this.accountList.has(uid))
    {   
        return rank;
    }  
    
    let data = this.accountList.get(uid);
    return data.rank;
};

MatchRank.prototype.checkCanFight = function(uid)
{
    //再看下是否玩家是否需要刷新时间
    let obj = this.getFightTimesList(uid);
    if (!TimeUtils.isToday(obj.lastUpdateTimes || 0))
    {
        this.flushFightTimes(uid);
        logger.info("checkCanFight.flushFightTimes", uid);
    }

    let fightTimes = this.getFightTimesByUid(uid, true);
    if (fightTimes >= this.totalTimes) //超过今天的挑战次数
    {
        return false;
    }

    return true;
};
//匹配
MatchRank.prototype.actualRangeMatch = function(destUid, start, end)
{
    let uidList = [];
    for (let index  in this.rank) {
        const data = this.rank[index];
        const uid = data.uid;
        let actualStrength = data.actualStrength;
        let groundOpenStatus = data.groundOpenStatus;
        //logger.info("actualRangeMatch: destUid, uid, groundOpenStatus",
         //destUid, uid, groundOpenStatus);
        if (groundOpenStatus === commonEnum.FOOTBALL_GROUND_STATUS.CLOSE)//球场没开放的跳过
        {   
            continue;
        }

        if (uid === destUid) //不能匹配自己
        {
            continue;
        }

        if (!this.checkCanFight(uid)) //不能打了
        {
            continue;
        }

        if (actualStrength > 0 && actualStrength >= start && actualStrength <= end)
        {
            uidList.push(uid);
        }
    }

    return uidList;
};

MatchRank.prototype.unActualRangeMatch = function(destUid, start, end)
{
    let uidList = [];
    for (let index  in this.rank) {
        const data = this.rank[index];
        const uid = data.uid;
        let actualStrength = data.actualStrength;
        let groundOpenStatus = data.groundOpenStatus;
        //logger.info("unActualRangeMatch: destUid, uid, groundOpenStatus", destUid, uid, groundOpenStatus);
        if (groundOpenStatus === commonEnum.FOOTBALL_GROUND_STATUS.CLOSE)
        {   
            continue;
        }

        if (uid === destUid) //不能匹配自己
        {
            continue;
        }

        if (!this.checkCanFight(uid)) //不能打了
        {
            continue;
        }
        
        if (actualStrength > 0 && actualStrength < start || actualStrength > end)
        {
            uidList.push(uid);
        }
    }

    return uidList;
};

MatchRank.prototype.getMaxDiffAct = function()
{
    let total = 0;
    for(let idx in this.actualStrengthDiffValue)
    {
        let value = this.actualStrengthDiffValue[idx];
        total += value;
    }
    return total;
};

//找到一个匹配玩家的uid
MatchRank.prototype.getMatchPlayerUid = function(uid, tmpActualStrength)
{
    if (!uid || uid === "")
    {
        logger.error("_getMatchPlayerUid: not playerId");
        return [];
    }

    //由于已创建角色就一定会有数据，so 玩家的实力值从这里匹配
    let rankObj = this.accountList.get(uid);
    let actualStrength = 0;
    if (rankObj)
    {
        actualStrength = rankObj.actualStrength;
    }
    else
    { 
        logger.error("_getMatchPlayerUid: accountList not playerId", uid);
        actualStrength = tmpActualStrength;
    }

    if (this.rank.length <= 1)  //没有人，或者自己
    {
        logger.error("_getMatchPlayerUid: accountList not other playerId", uid);
        return [];
    }
    //可以匹配到人
    let getList = [];
    for(let idx in this.actualStrengthDiffValue)
    {
        let actValue = this.actualStrengthDiffValue[idx];
        let min = actualStrength - actValue;
        let max = actualStrength + actValue;
        if (min < 0)
        {
            min = 0;
        }

        let uidList = this.actualRangeMatch(uid, min, max);
        let clList = utils.cloneArray(uidList);
        let lens = clList.length;
        if (lens > 10) //匹配得到20人,直接退出（这里不能大于上限 MATCH_RANK_LIMIT）
        {
            //let randomUidList = utils.arrayIndexRandom(uidList);
            for(let i = 0; i < 3; i++)//匹配玩家的数量
            {
                let num = Math.floor(Math.random()*lens);
                getList.push(clList[num]);
                clList.splice(num, 1);
                lens--;
            }
            logger.info("matchPlayerUid randomUidList 1 ", getList, lens);
            break;
        }
    }

    if (getList.length >= 3) //找到玩家了
    {
        logger.info("matchPlayerUid actualRangeMatch:", uid);
        return getList;
    }

    //还是找不到人,随机一个人
    //1.快速失败 计算出最小和最大允许实力差值范围
    let maxDiffact = this.getMaxDiffAct();
    let low = actualStrength - maxDiffact;
    let high = actualStrength + maxDiffact;
    if (low < 0)
    {
        low = 0;
    }

    let _uidList = this.unActualRangeMatch(uid, low, high);
    let clList = utils.cloneArray(_uidList);
    let lens = clList.length;
    if (lens > 0)
    {
        //let randomUidList = utils.arrayIndexRandom(_uidList);
        for(let i = 0; i < 3; i++)
        {
            let num = Math.floor(Math.random()*lens);
            getList.push(clList[num]);
            clList.splice(num, 1);
            lens--;
        }
        logger.info("matchPlayerUid randomUidList 2 ", lens);
    }
    
    if (getList.length > 0) //找到玩家了
    {
        logger.info("matchPlayerUid unActualRangeMatch:", uid, getList);
        return getList;
    }

    logger.info("matchPlayerUid not found!", uid);
    return [];
};