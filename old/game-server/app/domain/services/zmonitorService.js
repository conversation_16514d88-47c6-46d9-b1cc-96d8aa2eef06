/**
 * Idea and Persist
 * Created by June on 2019/4/9.
 */

let logger = require('pomelo-logger').getLogger(__filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let async = require('async');

let utils = require('../../util/utils');
let timeUtils = require('../../util/timeUtils');
let commonEnum = require('../../../../shared/enum');

module.exports.create = function(app){
    return new ZmonitorService(app);
};

let ZmonitorService = function(app){
    EventEmitter.call(this);
    this.app = app;
};

util.inherits(ZmonitorService, EventEmitter);

ZmonitorService.prototype.getZoneServerMonitorInfo = function(cb) {
    //1. 获取连接数
    //2. 获取CPU使用率
    //3. 获取内存使用率
    //4. 获取硬盘空间
    let self = this;
    //self.app.rpc.


};

