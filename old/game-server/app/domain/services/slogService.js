/**
 * Created by June on 2019/4/5.
 * Manage Global Player Online State
 */

var logger = require('pomelo-logger').getLogger("pomelo");
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var commonEnum = require('../../../../shared/enum');
var utils = require('../../util/utils');
var timelUtils = require('../../util/timeUtils');
var slogConfig = require('../../../config/slog.json');
var rollers = require('streamroller');

let fs = require('fs');
let readline = require('readline');

module.exports.create = function(app){
    return new SlogService(app);
};

var SlogService = function(app) {
    EventEmitter.call(this);
    this.app = app;
    this.logCache = [];
    this.recordIndex = 0;
    this.allDeleteMap = new Map();
    this.init();
    this.recordLoginIndex = 0;
    this.loginLogCache = [];
    //定时写入
    setInterval(this.write2Files, slogConfig.timeDuration, this);
};

util.inherits(SlogService, EventEmitter);

SlogService.prototype.init = function() {
    //加载去冗余用户数据Map
    logger.debug("the file dir path: ", __dirname);
    let filePath = './app/util/output_data/allDeletePlayer.json';
    let self = this;
    let readObj = readline.createInterface({
        input: fs.createReadStream(filePath)
    });
    readObj.on('line', function (line) {
        let obj = JSON.parse(line);
        self.allDeleteMap.set(obj.oid, obj.uid);
    });
    readObj.on('close', function () {
        logger.debug('allDeleteMap num: ', self.allDeleteMap.size);
    });
};

//日志记录参数 (playerId: player uid, behaviorType: 行为类型,参见commonEnum, valueArray: 收集参数, 最多3个, moreInfo: 补充信息)
SlogService.prototype.recordStatisLog = function(playerId, behaviorType, valueArray, moreInfo, cb) {
    //日志过滤
    if(!this.slogFilter(playerId, behaviorType, valueArray, moreInfo)) {
        logger.debug("slog filter do. no need record.", playerId, behaviorType, valueArray, moreInfo);
        return cb(Code.OK);
    }

    let msg;
    if(behaviorType === commonEnum.STATIS_LOG_TYPE.LOGIN) {
       msg = "{\"time\": \"" + timelUtils.timeFormat() + "\", \"playerId\": \"" + playerId + "}\n";
        // logger.error("commonEnum.STATIS_LOG_TYPE.LOGIN msg:", msg);
        this.loginLogCache.push(msg);
        cb(Code.OK);
    }else {
        msg = "{\"time\": \"" + timelUtils.timeDqdUTCFormat() + "\", \"serverip\": \"" + utils.getIPAddress()
            + "\", \"uid\": \"" + playerId + "\", \"bt\": " + behaviorType;
        for(let i=0, lens=3; i<lens; i++) {
            msg += (", \"value" + (i+1) + "\": " + (JSON.stringify(valueArray[i]) || 0));
        }
        msg += (", \"moreinfo\": " + JSON.stringify(moreInfo) + "}\n");
        //logger.debug("recordStatisLog msg:", msg);
        this.logCache.push(msg);
        cb(Code.OK);
    }
};

SlogService.prototype.write2Files = function (self) {
    //logger.debug("write2Files in.");
    self.recordIndex = self.logCache.length;
    self.recordLoginIndex = self.loginLogCache.length;
    if(self.recordIndex > 0) {
        let filePath = slogConfig.logPath + self.getTodayFileName();
        //logger.debug("slog start write. cache: ", self.logCache, filePath);
        let steam = new rollers.RollingFileStream(filePath, slogConfig.perFileSize, slogConfig.fileMaxNumber-1);
        let wrMsg = "";
        for(var i=0, lens=self.logCache.length; i<lens; i++) {
            wrMsg += self.logCache[i];
        }
        steam.write(wrMsg);
        steam.end();
        //清除已写入数据
        self.logCache.splice(0, self.recordIndex);
        self.recordIndex = 0;
        //logger.debug("slog end write. cache: ", self.logCache);
    }

    if(self.recordLoginIndex > 0) {
        let filePath = slogConfig.loginPath + self.getTodayFileName();
        let steam = new rollers.RollingFileStream(filePath, slogConfig.perFileSize, slogConfig.fileMaxNumber-1);
        let wrMsg = "";
        for(let i=0, lens=self.loginLogCache.length; i<lens; i++) {
            wrMsg += self.loginLogCache[i];
        }
        steam.write(wrMsg);
        steam.end();
        //清除已写入数据
        self.loginLogCache.splice(0, self.recordLoginIndex);
        self.recordLoginIndex = 0;
        //logger.error("loginLogCache size--- ", self.loginLogCache);
    }
};

//返回false则该条信息会被filter过滤，不进行记录
SlogService.prototype.slogFilter = function (playerId, behaviorType, valueArray, moreInfo) {
    //1. 去除之前删除的新增用户
    if(behaviorType === commonEnum.STATIS_LOG_TYPE.CLIENT_LOAD_RES && valueArray[0] === 7 && this.allDeleteMap.has(moreInfo.openid)) {
        return false;
    }
    return true;
};

SlogService.prototype.getTodayFileName = function () {
    return 'slog-' + timelUtils.dateFormat() + '.log';
};
