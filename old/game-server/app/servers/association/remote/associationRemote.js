let Code = require('../../../../../shared/code');
let logger = require('pomelo-logger').getLogger("pomelo", __filename);;
let qs = require('querystring');

module.exports = function(app) {
	return new Remote(app);
};

let Remote = function(app) {
	this.app = app;
};


Remote.prototype.createAssociation =  function(playerId, msg, next) {
	this.app.get("associationService").createAssociation(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.getAllAssociation =  function(playerId, msg, next) {
	this.app.get("associationService").getAllAssociation(playerId, msg, function(code, associationInfo){
		next(null, {code: code, associationInfo: associationInfo});
	});
}

Remote.prototype.getAssociationInfo =  function(playerId, msg, next) {
	this.app.get("associationService").getAssociationInfo(playerId, msg, function(code, associationInfo){
		next(null, {code: code, associationInfo: associationInfo});
	});
}

Remote.prototype.exitAssociation =  function(playerId, msg, next) {
	this.app.get("associationService").exitAssociation(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.getAssociationPlayerList =  function(playerId, msg, next) {
	this.app.get("associationService").getAssociationPlayerList(playerId, msg, function(code, playerList){
		next(null, {code: code, playerList: playerList});
	});
}

Remote.prototype.getAssociationPlayerListByName =  function(playerId, msg, next) {
	this.app.get("associationService").getAssociationPlayerListByName(playerId, msg, function(code, playerList, associationId){
		next(null, {code: code, playerList: playerList, associationId: associationId});
	});
}

Remote.prototype.getAssociationIdByLeagueName =  function(playerId, msg, next) {
	this.app.get("associationService").getAssociationIdByLeagueName(playerId, msg, function(code, associationId){
		next(null, {code: code, associationId: associationId});
	});
}

Remote.prototype.joinAssociation =  function(playerId, msg, next) {
	this.app.get("associationService").joinAssociation(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.removeJoinAssociation =  function(playerId, msg, next) {
	this.app.get("associationService").removeJoinAssociation(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.agreeJoinAssociation =  function(playerId, msg, next) {
	this.app.get("associationService").agreeJoinAssociation(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.getApprovalList =  function(playerId, msg, next) {
	this.app.get("associationService").getApprovalList(playerId, msg, function(code, approvalList){
		next(null, {code: code, approvalList: approvalList});
	});
}

Remote.prototype.updatePlayerStatus =  function(playerId, msg, next) {
	this.app.get("associationService").updatePlayerStatus(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.updatePlayerSessionId =  function(playerId, msg, next) {
	this.app.get("associationService").updatePlayerSessionId(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.updatePlayerExp =  function(playerId, msg, next) {
	this.app.get("associationService").updatePlayerExp(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.updateAssociationStrength =  function(playerId, msg, next) {
	this.app.get("associationService").updateAssociationStrength(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.changeAssociationPosition =  function(playerId, msg, next) {
	this.app.get("associationService").changeAssociationPosition(playerId, msg, function(code){
		next(null, {code: code});
	});
}

Remote.prototype.modifyNotice =  function(playerId, msg, next) {
	this.app.get("associationService").modifyNotice(playerId, msg, function(code){
		next(null, {code: code});
	});
}