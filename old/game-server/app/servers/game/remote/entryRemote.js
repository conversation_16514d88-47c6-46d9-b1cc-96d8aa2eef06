/**
 * Created by shine on 2015/3/10.
 */
var Code = require('../../../../../shared/code');
var dispatcher = require('../../../util/dispatcher');
var logger = require('pomelo-logger').getLogger('pomelo-rpc');
var dataApi = require('../../../util/dataApi');
var commonEnum = require('../../../../../shared/enum');
var async = require('async');
var osUtils = require('os-utils');
var utils = require('../../../util/utils');
/**
 * Gate handler that dispatch user to connectors.
 */
module.exports = function(app) {
	return new Remote(app);
};

let Remote = function(app) {
	this.app = app;
};

Remote.prototype.login = function(msg, next) {
	this.app.get("playerService").login(msg, function(playerId, isNew, step){
		if(!playerId){
			next(null, {code: Code.FAIL});
			return;
		}
		//logger.error("login", playerId, isNew, step);
		next(null, {code: Code.OK, playerId: playerId, isNew: isNew, step: step});
	});
};

Remote.prototype.leave = function(msg, next) {
	this.app.get("playerService").leave(msg.playerId, function(){
		next(null, {code: Code.OK});
	});
};

Remote.prototype.getBattleData = function(msg, next) {
	//logger.debug('getBattleData msg:', msg);
	this.app.get("playerService").getBattleData(msg.playerId, function (battleData) {
		next(null, {battleData: battleData});
	});
};

Remote.prototype.getLocalLeagueBattleData = function(msg, next) {
	//logger.debug('getLocalLeagueBattleData msg:', msg);
	this.app.get("playerService").getLocalLeagueBattleData(msg.playerId, function (battleData) {
		next(null, {battleData: battleData});
	});
};

Remote.prototype.getWarOfFaithBattleData = function(msg, next) {
	//logger.debug('getWarOfFaithBattleData msg:', msg);
	this.app.get("playerService").getWarOfFaithBattleData(msg.playerId, function (battleData) {
		next(null, {battleData: battleData});
	});
};

Remote.prototype.updatePVELeagueCopyBattle = function(msg, next) {
	let leagueId = 0;
	for(let key in dataApi.allData.data["LeagueTeam"]) {
		if(dataApi.allData.data["LeagueTeam"][key]["TeamID"] === msg.teamCopyId) {
			leagueId = dataApi.allData.data["LeagueTeam"][key]["LeagueID"];
			break;
		}
	}
	this.app.get("playerService").updatePVELeagueCopyBattle(msg.playerId, leagueId, msg.teamCopyId, msg.selfScore, msg.enemyScore, function(code){
		//logger.debug('updatePVELeagueCopyBattle code: ', code);
		next(null);
	});
};

Remote.prototype.updateWorldBossResult = function(msg, next) {
	this.app.get("playerMatchService").updateWorldBossResult(msg.playerId, {teamCopyId: msg.teamCopyId, selfScore:msg.selfScore}, function(code){
		next(null);
	});
};

Remote.prototype.yxbUnlockGoods = function (tradeNo, next) {
	logger.debug('Logic Server yxbUnlockGoods : tradeNo',tradeNo);
	this.app.get("playerService").yxbUnlockGoods(tradeNo, function (code) {
		next(null, code);
	})
};

Remote.prototype.testLeagueMessage = function (msg, next) 
{
	logger.debug('testLeagueMessage: msg',msg);
	this.app.get("playerService").testLeagueMessage(msg, function (code) {
		//logger.debug('Remote testLeagueMessage: code',code);
		next(null, code);
	})
};

Remote.prototype.getAllAccount = function(msg, next)
{
	logger.debug('getAllAccount: msg',msg);
	this.app.get("playerService").getAllAccount(msg, function (code, result) {
		//logger.debug('Remote testLeagueMessage: code',code);
		next(null, code, result);
	})
};

Remote.prototype.getAllAccountToBelief = function(msg, next)
{
	this.app.get("playerService").getAllAccountToBelief(msg, function (code, result) {
		next(code, result);
	})
};

Remote.prototype.broadcastChatMsg = function (msg, next) {
	this.app.get("playerService").broadcastChatMsg(msg);
	next(Code.OK);
};

Remote.prototype.broadcastAssociationChatMsg = function (msg, next) {
	this.app.get("playerService").broadcastAssociationChatMsg(msg);
	next(Code.OK);
};

Remote.prototype.getLocalPlayerDoc = function(msg, next) {
	this.app.get("playerService").getPlayerDocByDb(msg.playerId, msg.collectionNames, function (code, playerDoc, isNewRegister) {
		next(code, playerDoc, isNewRegister);
	});
};

Remote.prototype.getPkMatchPlayerListSimpleDB = function (msg, cb) {
	let result = [];
	let self = this;
	let findDbName = new Array(commonEnum.DB_NAME.player);
	async.eachSeries(msg.uidList, function (date, callback) {
			self.app.get("playerService").getPlayerDocByDb(date.uid, findDbName, function(code, playerDoc, isNew){
			//logger.debug("Remote getOtherPlayer", msg.playerId, msg.myGid);
			// logger.error("获得简要信息：", playerDoc.player.uid, playerDoc.player.name, playerDoc.player.faceUrl, playerDoc.player.level);
			result.push(utils.deepCopy({uid: playerDoc.player.uid, Name: playerDoc.player.name, FaceUrl: playerDoc.player.faceUrl, level: playerDoc.player.level}));
			callback(null);
		});
	}, function (err) {
		cb(result);
	});
};

Remote.prototype.getActualStrengthList = function (msg, cb) {
	let result = [];
	let self = this;
	let findDbName = new Array(commonEnum.DB_NAME.teamFormation);
	async.eachSeries(msg.uidList, function (date, callback) {
		self.app.get("playerService").getPlayerDocByDb(date.uid, findDbName, function(code, playerDoc, isNew){
			//logger.debug("Remote getOtherPlayer", msg.playerId, msg.myGid);
			//logger.error("获得简要信息：", playerDoc.teamFormation.currTeamFormationId);
			let currTeamFormationId = playerDoc.teamFormation.currTeamFormationId;
			let tmp = {};
			for(let i in playerDoc.teamFormation.teamFormations)
			{
				if(playerDoc.teamFormation.teamFormations[i].Uid === currTeamFormationId)
				{
					tmp = playerDoc.teamFormation.teamFormations[i];
					break;
				}
			}
			result.push({uid: playerDoc.teamFormation.uid, ActualStrength: tmp.ActualStrength, ADSun: tmp.Attack + tmp.Defend});
			callback(null);
		});
	}, function (err) {
		cb(result);
	});
};

Remote.prototype.getRelayDoc = function(msg, next) {
	this.app.get("playerService").getPlayerRelayDocByDb(msg.playerId, function (code, playerDoc) {
		next(code, playerDoc);
	});
};

Remote.prototype.getLocalSimplePlayerDoc = function(msg, next) {
	this.app.get("playerService").getSimplePlayerByDb(msg.playerId, function (code, playerDoc) {
		next(code, playerDoc);
	});
};

Remote.prototype.getAllPlayerInfoDoc = function(msg, next) {
	this.app.get("playerService").getAllPlayerInfoByDb(msg, function (code, uidArray) {
		next(code, uidArray);
	});
};
/*
Remote.prototype.getOtherPlayer = function (msg, next) {
	this.app.get("playerService").getOtherPlayer(msg.playerId, msg.collectionNames, function (player) {
		next(Code.OK, player);
	})
};
*/

Remote.prototype.finalupData = function (msg, next) {
	this.app.get("playerLeagueService").finalupData(msg, function (code) {
		next(code);
	})
};

Remote.prototype.cluster2GameDispather = function (msg, next) {
	//logger.debug("cluster2GameDispather msg:", msg);
	if(!msg.funcType) {
		logger.error("cluster2GameDispather param [msg.funcType] error.")
		return next(Code.FAIL);
	}

	switch (msg.funcType) 
	{
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_ENROLL_RESULT_MAIL:
			this.app.get("playerLeagueService").notifyEnrollResult(msg, function (code) {
				next(code);
			});
			break;

		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_PREPARE:
			this.app.get("playerLeagueService").notifyLeaguePrepare(msg, function (code) {
				next(code);
			});
			break;	
				
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_FINAL_REWARD:
			this.app.get("playerLeagueService").sendFinalReward(msg, function (code) {
				next(code);
			});
			break;

		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_WIN_AND_LOSE_MAIL:
			this.app.get("playerLeagueService").sendWinAndLose(msg, function (code) {
				next(code);
			});
			break;

		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_FINAL_WIN_MAIL:
			this.app.get("playerLeagueService").notifyFinalWin(msg, function (code) {
				next(code);
			});
			break;

		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_MATCH_UPDATE_BATTLE_RECORD:
			this.app.get("playerMatchService").updateMatchRecord(msg, function (code) {
					next(code);
				});
			break;
			
		default:
			logger.error("not case hint ", msg.funcType);
			next(Code.FAIL);
			break;
	}
};

Remote.prototype.cluster2AllGameServerDispather = function (msg, next) {
	//logger.debug("_cluster2AllGameServerDispather msg:", msg);
	if (!msg.funcType) {
		logger.error("param [msg.funcType] error.", msg)
		return next(Code.FAIL);
	}

	switch (msg.funcType) {
		default:
			logger.error("not case hint ", msg.funcType, msg);
			next(Code.FAIL);
			break;

		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_START:
			this.app.get("playerLeagueService").notifyLeagueStart(msg, function (code) {
				next(code);
			});
		break;

		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_PUBLIC_NOTIFY_PLAYER:
			this.app.get("playerLeagueService").publicNotifyPlayer(msg, function (code) {
				next(code);
			});
		break;
	}
};

Remote.prototype.updateTrophyResult = function(msg, next) {
	let trophyId = 0;
	for(let key in dataApi.allData.data["TrophyTeam"]) {
		if(dataApi.allData.data["TrophyTeam"][key]["TeamID"] === msg.teamCopyId) {
			trophyId = dataApi.allData.data["TrophyTeam"][key]["CopyType"];
			break;
		}
	}
	this.app.get("playerTrophyService").updateTrophyResult(msg.playerId, trophyId, msg.teamCopyId, msg.selfScore, msg.enemyScore, function(code){
		//logger.debug('updateTrophyResult code: ', code);
		next(null);
	});
};

Remote.prototype.updateWorldCupResult = function(msg, next) {
	this.app.get("playerWorldCupService").updateWorldCupResult(msg.playerId, msg.teamCopyId, msg.selfScore, msg.enemyScore, function(code){
		//logger.debug('updateWorldCupResult code: ', code);
		next(null);
	});
};

Remote.prototype.updateMiddleEastCupResult = function(msg, next) {
	this.app.get("playerMiddleEastCupService").updateMiddleEastCupResult(msg.playerId, msg.teamCopyId, msg.selfScore, msg.enemyScore, function(code){
		//logger.debug('updateWorldCupResult code: ', code);
		next(null);
	});
};

Remote.prototype.updateGulfCupResult = function(msg, next) {
	this.app.get("playerGulfCupService").updateGulfCupResult(msg.playerId, msg.teamCopyId, msg.selfScore, msg.enemyScore, function(code){
		//logger.debug('updateWorldCupResult code: ', code);
		next(null);
	});
};

Remote.prototype.updateMLSResult = function(msg, next) {
	this.app.get("playerMLSService").updateMLSResult(msg.playerId, msg.teamCopyId, msg.selfScore, msg.enemyScore, function(code){
		//logger.debug('updateWorldCupResult code: ', code);
		next(null);
	});
};

Remote.prototype.updateWatchRecord = function(msg, next) 
{
	this.app.get("playerService").updateWatchRecord(msg.playerId, msg, function(code) {
		next(null, code);
	});
};

Remote.prototype.unlockBelief = function (msg, next) {
	this.app.get("playerService").unlockBelief(msg, function (code) {
		next(code);
	});
};

Remote.prototype.onlineProcessFollow = function (msg, next) {
	//logger.debug('onlineProcessFollow msg: ', msg);
	this.app.get("playerFollowService").onlineProcessFollow(msg, function (code) {
		next(null, code);
	})
};

Remote.prototype.onlineGiveFollowEnergy = function (msg, next) {
	//logger.debug('onlineProcessFollow msg: ', msg);
	this.app.get("playerFollowService").giveEnergy(msg, function (code) {
		next(null, code);
	})
};

Remote.prototype.onlineProcessUnFollow = function (msg, next) {
	this.app.get("playerFollowService").onlineProcessUnFollow(msg, function (code) {
		next(null, code);
	})
};

Remote.prototype.dealWithPaymentResult = function (msg, next) {
	this.app.get("playerService").dealWithPaymentResult(msg, function (code) {
		next(code);
	});
};

Remote.prototype.getCpuAndMemoryInfo = function (msg, next) {
	//获取该服务器的CPU,内存使用情况
	let info = {};
	osUtils.cpuUsage(function (v) {
		info.cpuUsePercent = Math.round(v * 100);
		info.freememPercent = Math.round(osUtils.freememPercentage() * 100);
		next(Code.OK, info);
	})
};

Remote.prototype.sendEditMail = function (msg, next) {
	this.app.get("playerService").sendEditMail(msg, function (code) {
		next(code);
	});
};

Remote.prototype.sendEditMailByList = function (msg, next) {
	this.app.get("playerService").sendEditMailByList(msg, function (code) {
		next(code);
	});
};

Remote.prototype.sendUrlEditMailByList = function (msg, next) {
	this.app.get("playerService").sendUrlEditMailByList(msg, function (code) {
		next(code);
	});
};

Remote.prototype.sendPeakMatchMailByList = function (msg, next) {
	this.app.get("playerService").sendPeakMatchMailByList(msg, function (code) {
		next(code);
	});
};

Remote.prototype.notifyAllOnlinePlayer = function (msg, next) {
	this.app.get("playerService").notifyAllOnlinePlayer(msg, function (code) {
		next(code);
	});
};

Remote.prototype.playerIsOnline = function (msg, next) {
	this.app.get("playerService").playerIsOnline(msg, function (code, rate, faceUrl) {
		next(code, rate, faceUrl);
	});
};

Remote.prototype.notifyPlayerLeagueDissolve = function (msg, next) {
	this.app.get("playerService").notifyPlayerLeagueDissolve(msg, function (code) {
		next(code);
	});
};

Remote.prototype.playerIsJoinAssociation = function (msg, next) {
	this.app.get("playerService").playerIsJoinAssociation(msg, function (code) {
		next(code);
	});
};

Remote.prototype.updatePlayerApplyList = function (msg, next) {
	this.app.get("playerService").updatePlayerApplyList(msg, function (code) {
		next(code);
	});
};
Remote.prototype.getWarOfFaithTeamStrength = function (msg, next) {
	this.app.get("playerService").getWarOfFaithTeamStrength(msg, function (code, strength) {
		next(code, strength);
	});
};

Remote.prototype.sendWarOfFaithNotify = function (msg, next) {
	this.app.get("playerService").sendWarOfFaithNotify(msg, function (code) {
		next(code);
	});
};

Remote.prototype.sendOnlinePeakMatchTips = function (msg, next) {
	this.app.get("playerService").sendOnlinePeakMatchTips(msg, function () {
		next();
	});
};

Remote.prototype.dealWithGroundMatchRobOwner = function (msg, next) {
	this.app.get("groundMatchService").dealWithRobOwner(msg.record, msg.ownerInfo, msg.atkInfo, msg.robInfo, msg.beReportNum, function (code) {
		next(code);
	});
};

Remote.prototype.dealWithGroundMatchOccupier = function (msg, next) {
	this.app.get("groundMatchService").dealWithRobOccupier(msg.record, msg.ownerInfo, msg.atkInfo, msg.robInfo, function (code, beReportNum) {
		next(code, beReportNum);
	});
};

Remote.prototype.dealWithLeaveAwayOwner = function (msg, next) {
	this.app.get("groundMatchService").dealWithLeaveAwayOwner(msg.ownerUid, msg.ownerGid, msg.occupyInfo, function (code) {
		next(code);
	});
};

Remote.prototype.dealWithDriveAwayOccupier = function (msg, next) {
	this.app.get("groundMatchService").dealWithDriveAwayOccupier(msg.ownerInfo, msg.occupierUid, msg.record, function (code, reportNum) {
		next(code, reportNum);
	});
};

Remote.prototype.dealWithDriveAwayCheckOccupierProtect = function (msg, next) {
	this.app.get("groundMatchService").dealWithDriveAwayCheckOccupierProtect(msg.ownerInfo, msg.occupierUid, function (code) {
		next(code);
	});
};

Remote.prototype.dealWithReportDef = function (msg, next) {
	this.app.get("groundMatchService").dealWithReportDef(msg.beReportInfo, msg.reporterInfo, msg.ownerInfo, function (code, reportList) {
		next(code, reportList);
	});
};

Remote.prototype.dealWithReportOwner = function (msg, next) {
	this.app.get("groundMatchService").dealWithReportOwner(msg.beReportInfo, msg.reporterInfo, msg.ownerInfo, function (code) {
		next(code);
	});
};

Remote.prototype.getAndCalcPlayerStr = function (msg, next) {
	this.app.get("groundMatchService").getAndCalcPlayerStr(msg.searchUid, msg.searchTeamUid, function (ret) {
		next(ret);
	});
};

//处理到期占领者数据
Remote.prototype.dealWithCheckTimeOverOccupier = function (msg, next) {
	this.app.get("groundMatchService").dealWithCheckTimeOverOccupier(msg.occupyInfo, function (code) {
			next(code);
		});
};

//处理到期被占领-拥有者数据
Remote.prototype.dealWithCheckTimeOverOwner = function (msg, next) {
	this.app.get("groundMatchService").dealWithCheckTimeOverOwner(msg.ownerInfo, function (code) {
			next(code);
		});
};

//处理设置阵容时占领者的数据变更
Remote.prototype.syncOccupyTeamInfo = function (msg, next) {
	this.app.get("groundMatchService").syncOccupyTeamInfo(msg.uid, msg.occupyTeamIndex, msg.occupyTeamUid, msg.occupyUid, function (code) {
		next(code);
	});
};

//处理设置阵容时占领者的数据变更
Remote.prototype.dealWithCheckOwnerInfo = function (msg, next) {
	this.app.get("groundMatchService").dealWithCheckOwnerInfo(msg.ownerUid, msg.teamIndex, msg.robInfo, function (code) {
		next(code);
	});
};

Remote.prototype.updateActConfig = function (msg, next) {
	this.app.get("playerService").updateActConfig(msg, function (err) {
		next(Code.OK);
	});
};

Remote.prototype.setGiftBuyTime = function (msg, next) {
	this.app.get("playerService").setGiftBuyTime(msg, function (code) {
		next(code);
	});
};

Remote.prototype.saveGroundData = function (msg, next) {
	this.app.get("playerService").saveGroundData(msg, function (code) {
		next(code);
	});
};

Remote.prototype.updateHonorWallJoinNum = function (msg, next) {
	this.app.get("honorWallService").updateHonorWallJoinNum(msg, function (code) {
		next(code);
	});
};

Remote.prototype.updateHonorWallData = function (data, type, next) {
	this.app.get("honorWallService").updateHonorWallData(data, type, function (code) {
		next(code);
	});
};
