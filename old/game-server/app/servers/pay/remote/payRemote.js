var Code = require('../../../../../shared/code');
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var qs = require('querystring');


module.exports = function(app) {
    return new Remote(app);
};

var Remote = function(app) {
    this.app = app;
};

Remote.prototype.rechargeCreateOrder = function (msg, cb) {
    this.app.get("payService").rechargeCreateOrder(msg, function (code, orderId) {
        cb(code, orderId);
    });
};

Remote.prototype.addRenameCardOrder = function (msg, cb) {
    this.app.get("payService").addRenameCardOrder(msg, function (code, isInsert) {
       cb(code, isInsert);
    });
};

Remote.prototype.DeleteSendSuccessfulPlayer = function (msg, cb) {
    this.app.get("payService").DeleteSendSuccessfulPlayer(msg.playerId, function () {
        cb();
    });
};

Remote.prototype.doUpdateActiveData = function (msg, cb) {
    this.app.get("payService").doUpdateActiveData(msg, function (err) {
        cb(Code.OK);
    });
};

Remote.prototype.testFunctions = function (msg, cb) {
    this.app.get("payService").testFunctions(msg, function (code, data) {
        cb(code, data);
    });
};