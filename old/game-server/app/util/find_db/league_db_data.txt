[{"round": 0, "isRunning": true, "canAction": true, "battleStatus": 2, "sendWaitStartNtf": true, "sendStartNtf": true}, {"round": 1, "isRunning": true, "canAction": true, "battleStatus": 2, "sendWaitStartNtf": true, "sendStartNtf": true}, {"round": 2, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 3, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 4, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 5, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 6, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 7, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 8, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 9, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 10, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 11, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 12, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 13, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 14, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 15, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 16, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 17, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 18, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 19, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 20, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 21, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 22, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 23, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 24, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 25, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 26, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 27, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 28, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 29, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 30, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 31, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 32, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 33, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 34, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 35, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 36, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 37, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}, {"round": 38, "isRunning": false, "canAction": false, "battleStatus": 0, "sendWaitStartNtf": false, "sendStartNtf": false}]