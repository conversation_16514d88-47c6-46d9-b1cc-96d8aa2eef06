let mongoClient = require("mongodb").MongoClient;
let fs = require('fs');
let readline = require('readline');
let async = require('async');
let logger = require('pomelo-logger').getLogger(__filename);
let clusterConfig = require('../../../config/cluster.json');

let allDeleteMap = new Map();
let allLogIncreaseArr = [];
let allCheckMap = new Map();
let allRepeatMap = new Map();

let basePath = '../../../../statisLog/';
if (!process.argv[2]) {
    process.exit();
}
logger.debug('input check file path: ', process.argv[2]);

let checkFilePath = basePath + process.argv[2];

async.waterfall(
    [
        function (callback) {
            let readObj = readline.createInterface({
                input: fs.createReadStream('./allDeletePlayer.json')
            });
            readObj.on('line', function (line) {
                //arr.push(line);
                let obj = JSON.parse(line);
                allDeleteMap.set(obj.oid, obj.uid);
            });
            readObj.on('close', function () {
                logger.debug('allDeleteMap num: ', allDeleteMap.size);
                callback(null);
            });
        },
        function (callback) {
            let readObj = readline.createInterface({
                input: fs.createReadStream(checkFilePath)
            });
            readObj.on('line', function (line) {
                let obj = JSON.parse(line);
                // logger.debug('check file, obj:', obj.moreinfo.openid);
                if (obj.bt === 23 && obj.value1 === 7) {
                    allLogIncreaseArr.push(obj.uid);
                }
            });
            readObj.on('close', function () {
                logger.debug('allLogIncreaseArr num: ', allLogIncreaseArr.length);
                callback(null);
            });
        },
        function (callback) {
            let dbUser = clusterConfig.clusterDBName + '-admin';
            let clusterUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
            mongoClient.connect(clusterUrl, { useNewUrlParser: true }, function (error, dbclient) {
                if(error){
                    logger.error("connect clusterDBUrl failed! err: " + error);
                    return callback(error);
                }
                let db = dbclient.db(clusterConfig.clusterDBName);
                async.eachSeries(allLogIncreaseArr, function (uid, cb) {
                    //deletePlayerData(db, playerUid, clusterCollections, cb);
                    db.collection("account", function (err, collection) {
                        collection.findOne({
                            uid: uid
                        }, function (err, doc) {
                            if (!doc) {
                                return cb(null);
                            }
                            allCheckMap.set(doc.uid, doc.oid);
                            cb(null);
                        });
                    })
                }, function (err) {
                    logger.debug("allCheckMap num: ", allCheckMap.size);
                    for (let [k,v] of allCheckMap) {
                        if (allDeleteMap.has(v)){
                            allRepeatMap.set(v, k);
                        }
                    }
                    callback(null);
                });
            });
        },
    ],function (err) {
        logger.debug("check over, repeat num: ", allCheckMap.size, allRepeatMap.size, allCheckMap.size-allRepeatMap.size);
        process.exit();
    }
);

