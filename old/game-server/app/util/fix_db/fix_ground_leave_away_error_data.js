let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
let fs = require("fs");

let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');
let dataApi = require('../dataApi');
let TimeUtils = require('../../util/timeUtils');

let isFixed = false;

let dbMap = new Map();
let dbArr = [];
let resultMap = new Map();

let searchDate = "2020-04-01 00:00:00";
let searchTime = (new Date(searchDate)).getTime();

async.waterfall([
    //1. 初始化game服务器连接
    function (callback) {
        async.eachSeries(serversConfig.development.game, function (gameInfo, cb1) {
            let serverId = gameInfo.id;
            let dbUser = serverId + '-admin';
            let gameDBUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
                if (!!error) {
                    logger.error("connect game db fail.", error, serverId);
                    return callback(error);
                }
                dbMap.set(serverId, dbclient);
                cb1();
            })
        }, function (err) {
            callback();
        });
    },
    function (callback) {
        for(let [k,v] of dbMap) {
            dbArr.push({id: k, dbclient: v});
        }
        let _getDbByServerId = function(serverId) {
            return dbMap.get(serverId) ? dbMap.get(serverId).db(serverId) : null;
        };
        async.eachSeries(dbArr, function (gameInfo, cb1) {
            let serverId = gameInfo.id;
            let db = gameInfo.dbclient.db(serverId);
            let num = 0;
            db.collection("player", function (err, col) {
               col.find({leaveTime: {$gt: searchTime}}).toArray(function (err, playerList) {
                   db.collection("footballGround", function (err, col1) {
                      async.eachSeries(playerList, function (player, cb2) {
                          col1.findOne({uid: player.uid}, function (err, groundDoc) {
                                if (err) {
                                    logger.debug("find footballGround err: ", obj);
                                    return cb2();
                                }
                                //logger.debug("groundDoc: ", groundDoc);
                                if (!groundDoc || !groundDoc.groundMatch) {
                                    return cb2();
                                }
                                if(!groundDoc.groundMatch.fieldList) {
                                    logger.debug("groundDoc.groundMatch.fieldList is null. ");
                                    return cb2();
                                }
                                //检查拥有者的被占领用户
                                let fieldList = groundDoc.groundMatch.fieldList;
                                let isFound = false;
                                async.eachSeries(fieldList, function (fieldInfo, cb3) {
                                    if(!fieldInfo.beOccupiedUid || !fieldInfo.beOccupiedGid) {
                                        return cb3();
                                    }
                                    let checkData = {
                                        occupyUid: fieldInfo.beOccupiedUid,
                                        occupyGid: fieldInfo.beOccupiedGid,
                                        occupyTeamUid: fieldInfo.beOccupiedTeamUid,
                                        name: fieldInfo.name
                                    };
                                    let checkDb = _getDbByServerId(checkData.occupyGid);
                                    if(!checkData) {
                                        logger.error("check data gid err: ", checkData.occupyGid);
                                        return cb3();
                                    }
                                    checkDb.collection("footballGround", function (err, col2) {
                                        if(err) {
                                            logger.error("check Db collection footballGround fail. ", checkData, err);
                                            return cb3();
                                        }
                                        col2.findOne({uid: checkData.occupyUid}, function (err, checkDoc) {
                                             if(err || !checkDoc || !checkDoc.groundMatch) {
                                                 logger.error("checkData err: ", err, checkDoc);
                                                 return cb3();
                                             }
                                             let occupyFieldList = checkDoc.groundMatch.occupyFieldList;
                                             let occInfo;
                                             for(let m=0; m<occupyFieldList.length; m++) {
                                                 occInfo = occupyFieldList[m];
                                                 if(occInfo.occupyUid === player.uid && occInfo.teamUid === checkData.occupyTeamUid) {
                                                     isFound = true;
                                                     break;
                                                 }
                                             }
                                             if(!isFound) {
                                                 //logger.debug("find occupy error data: ", player.name, checkData, serverId, occupyFieldList);
                                                 resultMap.set(player.uid, {name: player.name, occName: checkData.name});
                                                 fieldInfo.startTime = TimeUtils.now();   //开始自产时间
                                                 fieldInfo.beOccupiedUid = "";            //占领的玩家uid
                                                 fieldInfo.beOccupiedGid = "";            //占领的玩家gid
                                                 fieldInfo.beOccupiedTeamUid = "";        //占领的阵容uid
                                                 fieldInfo.beOccupiedTeamName = "";       //被占领
                                                 fieldInfo.name = "";                     //占领玩家的名字
                                                 fieldInfo.faceUrl = "";                  //占领玩家的头像
                                                 fieldInfo.formationResId = 0;            //阵型id
                                                 fieldInfo.attack = 0;                    //进攻值
                                                 fieldInfo.defend = 0;                    //防守值
                                                 fieldInfo.atkTactic = 0;                 //占领玩家的进攻战术
                                                 fieldInfo.defTactic = 0;                 //占领玩家的防守战术
                                                 fieldInfo.occupyStartTime = 0;           //占领开始时间
                                             }
                                             cb3();
                                        });
                                    })
                                },function (err) {
                                    if(!isFound && isFixed) {
                                        col1.updateOne({uid: player.uid}, {$set: {groundMatch: groundDoc.groundMatch}}, function (err) {
                                            logger.debug("update finish.", player.name, err);
                                        });
                                    }
                                    cb2();
                                })
                            });
                        }, function () {
                            cb1();
                        });
                    });
                })
            });
        }, function (err) {
            logger.debug('connect game servers mongodb finish.');
            callback(null);
        })
    }], function (err) {
        //关闭数据库连接
        for(let [k,v] of dbMap) {
            v.close();
        }
        logger.debug("resultMap size: ", resultMap.size);
        let fixData = [];
        for(let [k,v] of resultMap) {
            v.uid = k;
            fixData.push(v);
        }
        let filePath = __dirname + "/fix_ground_occupy_error_data_list.json";
        if(isFixed) {
            fs.writeFileSync(filePath, JSON.stringify(fixData));
        }
        process.exit();
});

