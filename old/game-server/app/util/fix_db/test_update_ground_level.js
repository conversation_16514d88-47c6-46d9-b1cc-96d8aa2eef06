let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
var fs = require("fs");

let clusterConfig = require('../../../config/cluster.json');
let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');

let dbMap = new Map();

let isFixed = false;

let searchName = process.argv[2];
let fixLevel = parseInt(process.argv[3]);

let uid = "";
let gid = "";

async.waterfall([
    function (callback) {
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterUrl, { useNewUrlParser: true }, function (error, dbclient) {
            if(error){
                logger.error("connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            dbMap.set("cluster", dbclient);
            let db = dbclient.db(clusterConfig.clusterDBName);
            db.collection("account", function (err, col) {
                col.find({name: searchName}).toArray(function (err, list) {
                    if(!list || list.length !== 1) {
                        logger.debug("get wrong data.", list);
                        return callback("wrong data.");
                    }
                    logger.debug("find account: ", list[0]);
                    uid = list[0].uid;
                    gid = list[0].gid;
                    callback();
                })
            })
        });
    },
    function (callback) {
        let serverId = gid;
        let dbUser = serverId + '-admin';
        let gameDBUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
        mongoClient.connect(gameDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
            if (!!error) {
                logger.error("connect game db fail.", error, serverId);
                return cb1(error);
            }
            dbMap.set(serverId, dbclient);
            let db = dbclient.db(serverId);
            db.collection("footballGround", function (err, col) {
                col.findOne({uid: uid}, function (err, doc) {
                    if(!doc) {
                        logger.debug("find no data. uid: ", uid, gid);
                        return callback("no data.");
                    }
                    logger.debug("find before data: ", doc.mainGround);
                    if(!isFixed) {
                        return callback();
                    }else {
                        doc.mainGround[0].Level = fixLevel;
                        col.updateOne({uid: uid}, {$set: {mainGround: doc.mainGround}}, function (err) {
                           if(!err) {
                               logger.debug("fix success. after data: ", fixLevel);
                           }
                           callback();
                        });
                    }
                });
            });
        });
    }
    ], function (err) {
        //关闭数据库连接
        for(let [k,v] of dbMap) {
            v.close();
        }
        process.exit();
});
