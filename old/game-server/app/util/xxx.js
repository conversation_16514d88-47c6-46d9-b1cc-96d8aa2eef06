var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var async = require("async");
var utils = require("./utils");
var util = require('util');
var EventEmitter = require('events').EventEmitter;
var commonEnum = require("../../../shared/enum");
var Code = require("../../../shared/code");
var http = require('http');
var TimeUtils = require("./timeUtils");

let brecordConfig = require('../../config/brecord.json');
let mongoClient = require("mongodb").MongoClient;

let oneDayTime = 1 * 24 * 60 * 60 * 1000;  //1天
let nowTime = new Date().getTime();

let dbUser = brecordConfig.brecordDBName + '-admin';
let drecordDBUrl = "mongodb://" + dbUser + ':' + brecordConfig.dbPasswd + '@' + brecordConfig.brecordDBUrl + '/' + brecordConfig.brecordDBName;

mongoClient.connect(drecordDBUrl, { useNewUrlParser: true },function(error, dbclient) {
    if (error) {
        logger.error("connect drecordDBUrl failed! err: " + error);
        return callback(error);
    }
    let db = dbclient.db(brecordConfig.brecordDBName);
    db.collection("brecord", function (err1, col) {
        let cursor = col.find({beginTime: {$lte: (nowTime - 2*oneDayTime)}}).limit(6).toArray(
            function (err, doc) {
                logger.debug("arr: ", doc.length)
            }
        );
    });

});