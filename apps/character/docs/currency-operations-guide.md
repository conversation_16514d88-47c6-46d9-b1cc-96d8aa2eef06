# 货币操作优化指南

## 🎯 问题分析

原有的货币操作方法存在以下问题：

1. **参数不匹配**：`addCurrency` 期望 `AddCurrencyPayloadDto`，但内部调用传递简单对象
2. **重复验证**：每个货币操作都重复角色存在性验证
3. **缺乏通用性**：无法作为内部工具方法被其他业务逻辑调用

## 🔧 优化方案

### 新增内部货币操作方法

#### 1. 单个货币操作

```typescript
// ✅ 增加货币 - 内部方法
async addCurrencyInternal(
  characterId: string,
  currencyType: string,
  amount: number,
  reason: string = '增加货币'
): Promise<XResult<CurrencyOperationResult>>

// ✅ 扣除货币 - 内部方法
async subtractCurrencyInternal(
  characterId: string,
  currencyType: string,
  amount: number,
  reason: string = '扣除货币',
  allowNegative: boolean = false
): Promise<XResult<CurrencyOperationResult>>
```

#### 2. 批量货币操作

```typescript
// ✅ 批量操作 - 支持多种货币同时增减
async batchCurrencyOperation(
  characterId: string,
  operations: Array<{
    currencyType: string;
    amount: number; // 正数为增加，负数为扣除
  }>,
  reason: string = '批量货币操作'
): Promise<XResult<BatchCurrencyOperationResult>>
```

## 📋 实际应用案例

### 案例1：简单货币增加

```typescript
// ❌ 优化前 - 参数不匹配
await this.addCurrency(characterId, {
  currencyType: 'gold',
  amount: 1000,
  reason: 'quest_reward'
}); // 类型错误！

// ✅ 优化后 - 使用内部方法
const result = await this.addCurrencyInternal(
  characterId,
  'gold',
  1000,
  'quest_reward'
);

if (XResultUtils.isSuccess(result)) {
  this.logger.log(`金币增加成功，新余额: ${result.data.newBalance}`);
}
```

### 案例2：货币扣除（带余额检查）

```typescript
// ✅ 扣除货币 - 自动检查余额
const result = await this.subtractCurrencyInternal(
  characterId,
  'gold',
  500,
  'purchase_item'
);

if (XResultUtils.isFailure(result)) {
  if (result.code === 'INSUFFICIENT_CURRENCY') {
    return XResultUtils.error('金币不足，无法购买', 'PURCHASE_FAILED');
  }
  return result;
}

// 扣除成功，继续业务逻辑
const newBalance = result.data.newBalance;
```

### 案例3：批量货币操作

```typescript
// ✅ 一次性操作多种货币
const operations = [
  { currencyType: 'gold', amount: -1000 },    // 扣除1000金币
  { currencyType: 'energy', amount: 50 },     // 增加50体力
  { currencyType: 'chip', amount: -10 }       // 扣除10钻石
];

const result = await this.batchCurrencyOperation(
  characterId,
  operations,
  'shop_purchase'
);

if (XResultUtils.isSuccess(result)) {
  this.logger.log('批量货币操作成功', result.data.operations);
}
```

### 案例4：复杂业务逻辑中的应用

```typescript
// ✅ 在升级系统中使用
async levelUpCharacter(dto: LevelUpPayloadDto): Promise<XResult<any>> {
  return this.executeBusinessOperation(async () => {
    // 1. 验证角色存在
    const characterResult = await this.validateAndGetCharacter(
      dto.characterId,
      this.characterRepository,
      '角色升级'
    );
    if (XResultUtils.isFailure(characterResult)) return characterResult;

    const character = characterResult.data;

    // 2. 计算升级消耗
    const goldCost = character.level * 1000;
    const energyCost = 10;

    // 3. 批量扣除货币
    const currencyResult = await this.batchCurrencyOperation(
      dto.characterId,
      [
        { currencyType: 'gold', amount: -goldCost },
        { currencyType: 'energy', amount: -energyCost }
      ],
      'character_levelup'
    );

    if (XResultUtils.isFailure(currencyResult)) {
      return currencyResult; // 自动包含余额不足等错误信息
    }

    // 4. 执行升级逻辑
    const newLevel = character.level + 1;
    const updateResult = await this.characterRepository.update(dto.characterId, {
      level: newLevel,
      experience: 0
    });

    return this.handleRepositoryResult(updateResult, '角色升级失败');
  });
}
```

### 案例5：商店购买系统

```typescript
// ✅ 商店购买中的货币操作
async purchaseItem(dto: PurchaseItemDto): Promise<XResult<any>> {
  return this.executeBusinessOperation(async () => {
    // 1. 获取商品信息
    const item = await this.getItemInfo(dto.itemId);
    if (!item) {
      return XResultUtils.error('商品不存在', 'ITEM_NOT_FOUND');
    }

    // 2. 构建货币操作（支持多种货币支付）
    const operations = [];
    if (item.goldCost > 0) {
      operations.push({ currencyType: 'gold', amount: -item.goldCost });
    }
    if (item.chipCost > 0) {
      operations.push({ currencyType: 'chip', amount: -item.chipCost });
    }

    // 3. 执行货币扣除
    const currencyResult = await this.batchCurrencyOperation(
      dto.characterId,
      operations,
      `purchase_${item.name}`
    );

    if (XResultUtils.isFailure(currencyResult)) {
      return currencyResult;
    }

    // 4. 添加物品到背包
    const addItemResult = await this.addItemToInventory(dto.characterId, dto.itemId, dto.quantity);
    
    return addItemResult;
  });
}
```

## 🚀 性能优化特性

### 1. 统一角色验证

```typescript
// ✅ 使用 validateAndGetCharacter 消除重复验证
const characterResult = await this.validateAndGetCharacter(
  characterId, 
  this.characterRepository, 
  '货币操作'
);
```

### 2. 批量数据库更新

```typescript
// ✅ 一次数据库操作更新多个字段
const updateData = {
  gold: newGoldBalance,
  energy: newEnergyBalance,
  chip: newChipBalance
};
await this.characterRepository.update(characterId, updateData);
```

### 3. 详细的操作日志

```typescript
// ✅ 自动记录详细的货币操作日志
this.logger.log(`货币操作成功: ${characterId}, ${currencyType}: ${amount > 0 ? '+' : ''}${amount}, 新余额: ${newBalance}`);
```

## 🎯 最佳实践

### 1. 方法选择指南

```typescript
// ✅ 单个货币操作
await this.addCurrencyInternal(characterId, 'gold', 1000, 'quest_reward');
await this.subtractCurrencyInternal(characterId, 'gold', 500, 'shop_purchase');

// ✅ 多个货币操作
await this.batchCurrencyOperation(characterId, operations, 'complex_transaction');

// ✅ Controller层接口（保持兼容）
await this.addCurrency(dto); // 用于微服务调用
```

### 2. 错误处理标准

```typescript
// ✅ 统一的错误处理模式
const result = await this.subtractCurrencyInternal(characterId, 'gold', amount, reason);
if (XResultUtils.isFailure(result)) {
  // 自动包含详细的错误信息和错误码
  return result;
}

// 操作成功，继续业务逻辑
const { newBalance } = result.data;
```

### 3. 事务安全

```typescript
// ✅ 在 executeBusinessOperation 中使用，确保事务安全
return this.executeBusinessOperation(async () => {
  const currencyResult = await this.batchCurrencyOperation(characterId, operations, reason);
  if (XResultUtils.isFailure(currencyResult)) return currencyResult;
  
  // 其他业务逻辑...
  return businessResult;
});
```

## 🏆 核心优势

1. **通用性强**：内部方法可被任何业务逻辑调用
2. **类型安全**：参数类型明确，避免DTO包装问题
3. **性能优化**：统一验证，批量操作，减少数据库访问
4. **错误处理**：统一的错误码和错误信息
5. **日志完整**：详细的操作日志，便于调试和监控
6. **向后兼容**：保留原有Controller层接口

这个优化方案彻底解决了货币操作的通用性问题，大大提高了代码复用性和开发效率！
