import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Formation相关组件
import { FormationController } from './formation.controller';
import { FormationService } from './formation.service';
// Formation模块不需要直接导入配置表Schema，通过GameConfigFacade访问
import { TeamFormations, TeamFormationsSchema } from '@character/common/schemas/formation.schema';
import { FormationRepository } from '@character/common/repositories/formation.repository';

// 导入Character模块以便直接调用CharacterService（使用forwardRef解决循环依赖）
import { CharacterModule } from '../character/character.module';

/**
 * 阵容模块
 */
@Module({
  imports: [
    // 注册Formation相关Schema
    MongooseModule.forFeature([
      { name: TeamFormations.name, schema: TeamFormationsSchema },
    ]),
    // 使用forwardRef解决循环依赖问题
    forwardRef(() => CharacterModule),
  ],

  controllers: [FormationController],

  providers: [
    FormationService,
    FormationRepository,
  ],

  exports: [
    FormationService,
    FormationRepository,
  ],
})
export class FormationModule {}
