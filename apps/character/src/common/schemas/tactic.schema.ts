/**
 * Tactic Schema - 严格基于old项目战术系统重新设计
 * 确保与old项目的数据结构和功能100%一致
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {SwapHerosDto} from "@character/common/dto/formation.dto";

// 战术类型枚举 - 基于old项目commonEnum.TEAM_TACTICS_LIST
export enum TacticType {
  ATTACK = 1,       // 进攻战术
  DEFENSE = 2,      // 防守战术
  BALANCED = 3,     // 平衡战术
  COUNTER = 4,      // 反击战术
}

// 战术效果类型枚举
export enum TacticEffectType {
  ATTRIBUTE_BONUS = 1,    // 属性加成
  SKILL_BONUS = 2,        // 技能加成
  FORMATION_BONUS = 3,    // 阵型加成
  SPECIAL_EFFECT = 4,     // 特殊效果
}

// 战术解锁条件
@Schema({ _id: false })
export class TacticUnlockCondition {
  @Prop({ required: true })
  type: string;             // 条件类型：level, item, achievement等

  @Prop({ required: true })
  value: number;            // 条件值

  @Prop({ default: '' })
  description: string;      // 条件描述
}

// 战术效果配置
@Schema({ _id: false })
export class TacticEffect {
  @Prop({ required: true })
  type: TacticEffectType;   // 效果类型

  @Prop({ required: true })
  target: string;           // 作用目标：hero, team, formation等

  @Prop({ required: true })
  attribute: string;        // 影响的属性

  @Prop({ required: true })
  value: number;            // 效果值

  @Prop({ default: 1 })
  levelMultiplier: number;  // 等级倍数

  @Prop({ default: '' })
  formula: string;          // 计算公式

  @Prop({ default: 0 })
  duration: number;         // 持续时间（0表示永久）

  @Prop({ default: '' })
  description: string;      // 效果描述
}

// 战术组合效果
@Schema({ _id: false })
export class TacticCombination {
  @Prop({ type: [String], required: true })
  tacticKeys: string[];     // 组合的战术Key列表

  @Prop({ required: true })
  name: string;             // 组合名称

  @Prop({ type: [TacticEffect], default: [] })
  bonusEffects: TacticEffect[]; // 额外效果

  @Prop({ default: '' })
  description: string;      // 组合描述
}

// 单个战术实例 - 基于old项目战术数据结构
@Schema({ _id: false })
export class CharacterTactic {
  @Prop({ required: true })
  tacticId: string;         // 战术ID - 对应配置表ID

  @Prop({ required: true })
  tacticKey: string;        // 战术Key - 对应配置表Key

  @Prop({ required: true })
  name: string;             // 战术名称

  @Prop({ default: 1 })
  level: number;            // 战术等级

  @Prop({ default: 0 })
  experience: number;       // 战术经验

  @Prop({ default: false })
  isActive: boolean;        // 是否激活

  @Prop({ default: false })
  isUnlocked: boolean;      // 是否解锁

  @Prop({ default: TacticType.ATTACK })
  type: TacticType;         // 战术类型

  @Prop({ type: [TacticEffect], default: [] })
  currentEffects: TacticEffect[]; // 当前等级的效果

  @Prop({ type: [TacticUnlockCondition], default: [] })
  unlockConditions: TacticUnlockCondition[]; // 解锁条件

  @Prop({ default: Date.now })
  unlockTime: number;       // 解锁时间

  @Prop({ default: Date.now })
  lastUsedTime: number;     // 最后使用时间

  @Prop({ default: 0 })
  usageCount: number;       // 使用次数

  @Prop({ default: 0 })
  winCount: number;         // 胜利次数

  @Prop({ default: 0 })
  totalMatches: number;     // 总场次

  // 虚拟字段：胜率
  get winRate(): number {
    return this.totalMatches > 0 ? (this.winCount / this.totalMatches) * 100 : 0;
  }

  // 虚拟字段：是否可以升级
  get canUpgrade(): boolean {
    return this.level < 10 && this.isUnlocked; // 假设最大等级为10
  }

  // 虚拟字段：升级所需经验
  get requiredExperience(): number {
    return this.level * 100; // 简化的经验计算
  }
}

// 阵容战术配置 - 基于old项目Formation中的战术字段
@Schema({ _id: false })
export class FormationTacticConfig {
  @Prop({ required: true })
  formationId: string;      // 阵容ID

  @Prop({ required: true })
  attackTacticId: string;   // 进攻战术ID - 对应old项目UseTactics

  @Prop({ required: true })
  defenseTacticId: string;  // 防守战术ID - 对应old项目UseDefTactics

  @Prop({ type: [String], default: [] })
  combinationTactics: string[]; // 组合战术

  @Prop({ default: Date.now })
  updateTime: number;       // 更新时间
}

// 球员战术加成缓存 - 基于old项目calcHeroTacticsAttr
@Schema({ _id: false })
export class HeroTacticBonus {
  @Prop({ required: true })
  heroId: string;         // 球员ID

  @Prop({ required: true })
  formationId: string;      // 阵容ID

  @Prop({ type: Object, default: {} })
  attributeBonus: Record<string, number>; // 属性加成

  @Prop({ type: Object, default: {} })
  skillBonus: Record<string, number>; // 技能加成

  @Prop({ default: Date.now })
  cacheTime: number;        // 缓存时间

  @Prop({ default: false })
  isValid: boolean;         // 缓存是否有效
}

// 主Tactic Schema - 严格基于old项目战术系统
@Schema({ 
  collection: 'tactics', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Tactic {
  @Prop({ required: true, index: true })
  uid: string;              // 玩家UID - 对应old项目的uid

  @Prop({ required: true, index: true })
  characterId: string;      // 角色ID (新增字段，用于微服务架构)

  @Prop({ required: true, index: true })
  serverId: string;         // 服务器ID (新增字段，用于微服务架构)

  // 所有战术 - 对应old项目的allTactics (Map转为Array)
  @Prop({ type: [CharacterTactic], default: [] })
  tactics: CharacterTactic[];

  // 阵容战术配置 - 对应old项目Formation中的战术设置
  @Prop({ type: [FormationTacticConfig], default: [] })
  formationTactics: FormationTacticConfig[];

  // 战术组合配置
  @Prop({ type: [TacticCombination], default: [] })
  combinations: TacticCombination[];

  // 球员战术加成缓存
  @Prop({ type: [HeroTacticBonus], default: [] })
  heroBonusCache: HeroTacticBonus[];

  @Prop({ default: Date.now })
  createTime: number;       // 创建时间

  @Prop({ default: Date.now })
  updateTime: number;       // 更新时间
}

export const TacticSchema = SchemaFactory.createForClass(Tactic);

// 定义Document类型和方法接口
export interface TacticMethods {
  // 战术管理方法 - 基于old项目
  getTactic(tacticKey: string): CharacterTactic | null;
  addTactic(tactic: CharacterTactic): void;
  removeTactic(tacticKey: string): boolean;
  updateTactic(tacticKey: string, updates: Partial<CharacterTactic>): boolean;

  // 战术激活和升级 - 基于old项目
  activateTactic(tacticKey: string): boolean;
  deactivateTactic(tacticKey: string): boolean;
  upgradeTactic(tacticKey: string): boolean;
  unlockTactic(tacticKey: string): boolean;

  // 阵容战术设置 - 基于old项目Formation集成
  setFormationTactics(formationId: string, attackTacticId: string, defenseTacticId: string): void;
  getFormationTactics(formationId: string): FormationTacticConfig | null;
  removeFormationTactics(formationId: string): boolean;

  // 战术效果计算 - 基于old项目
  calculateTacticEffects(tacticKey: string, level: number): TacticEffect[];
  calculateHeroBonus(heroId: string, formationId: string): HeroTacticBonus;
  calculateCombinationEffects(tacticKeys: string[]): TacticEffect[];

  // 战术统计 - 基于old项目
  updateTacticUsage(tacticKey: string, isWin: boolean): void;
  getTacticStats(tacticKey: string): { winRate: number; usageCount: number };

  // 缓存管理
  invalidateHeroBonusCache(heroId?: string, formationId?: string): void;
  cleanExpiredCache(): void;

  // 数据转换 - 基于old项目
  makeClientTacticList(): any[];
  toJSONforDB(): any;

  // ========== 新增实用业务方法 - 基于TacticService真实逻辑 ==========

  // 战术验证方法
  validateTacticUnlockConditions(tacticKey: string): boolean;
  checkTacticCanUpgrade(tacticKey: string): boolean;
  getTacticUpgradeCost(tacticKey: string): number;

  // 战术组合管理 - 基于service中的组合效果计算
  addTacticCombination(combination: TacticCombination): boolean;
  removeTacticCombination(combinationId: string): boolean;
  getActiveCombinations(): TacticCombination[];

  // 战术经验管理 - 基于service中的使用统计
  addTacticExperience(tacticKey: string, experience: number): boolean;
  getTacticProgress(tacticKey: string): { currentExp: number; requiredExp: number; progress: number };

  // 战术效果查询 - 基于service中的效果计算
  getAllActiveTacticEffects(formationId: string): TacticEffect[];
  getTacticEffectsByType(effectType: TacticEffectType): TacticEffect[];

  // 数据统计和分析 - 基于service中的统计逻辑
  getTotalTacticCount(): number;
  getUnlockedTacticCount(): number;
  getActiveTacticCount(): number;
  getTacticsByType(type: TacticType): CharacterTactic[];
  getMostUsedTactics(limit: number): CharacterTactic[];
  getBestPerformingTactics(limit: number): CharacterTactic[];
}

export type TacticDocument = Tactic & Document & TacticMethods;

// 创建索引
TacticSchema.index({ uid: 1 }, { unique: true });
TacticSchema.index({ characterId: 1 });
TacticSchema.index({ serverId: 1 });

// 添加实例方法 - 基于old项目的核心方法

/**
 * 获取指定战术
 * 基于old项目: 战术查询逻辑
 */
TacticSchema.methods.getTactic = function(tacticKey: string): CharacterTactic | null {
  return this.tactics.find((tactic: CharacterTactic) => tactic.tacticKey === tacticKey) || null;
};

/**
 * 激活战术
 * 基于old项目: activateTactic逻辑
 */
TacticSchema.methods.activateTactic = function(tacticKey: string): boolean {
  const tactic = this.getTactic(tacticKey);
  if (!tactic || !tactic.isUnlocked) {
    return false;
  }

  // 先取消其他同类型战术的激活状态
  this.tactics.forEach((t: CharacterTactic) => {
    if (t.type === tactic.type && t.tacticKey !== tacticKey) {
      t.isActive = false;
    }
  });

  tactic.isActive = true;
  tactic.lastUsedTime = Date.now();
  this.updateTime = Date.now();

  return true;
};

/**
 * 升级战术
 * 基于old项目: upgradeTactic逻辑
 */
TacticSchema.methods.upgradeTactic = function(tacticKey: string): boolean {
  const tactic = this.getTactic(tacticKey);
  if (!tactic || !tactic.canUpgrade) {
    return false;
  }

  if (tactic.experience >= tactic.requiredExperience) {
    tactic.level += 1;
    tactic.experience -= tactic.requiredExperience;
    this.updateTime = Date.now();

    // 重新计算当前等级的效果
    // TODO: 从配置表获取新等级的效果

    return true;
  }

  return false;
};

/**
 * 设置阵容战术
 * 基于old项目: Formation模块的战术设置
 */
TacticSchema.methods.setFormationTactics = function(formationId: string, attackTacticId: string, defenseTacticId: string): void {
  // 查找现有配置
  let config = this.formationTactics.find((config: FormationTacticConfig) => config.formationId === formationId);

  if (config) {
    config.attackTacticId = attackTacticId;
    config.defenseTacticId = defenseTacticId;
    config.updateTime = Date.now();
  } else {
    this.formationTactics.push({
      formationId,
      attackTacticId,
      defenseTacticId,
      combinationTactics: [],
      updateTime: Date.now()
    });
  }

  this.updateTime = Date.now();

  // 清除相关的球员加成缓存
  this.invalidateHeroBonusCache(undefined, formationId);
};

/**
 * 获取阵容战术配置
 * 基于old项目: Formation模块的战术查询
 */
TacticSchema.methods.getFormationTactics = function(formationId: string): FormationTacticConfig | null {
  return this.formationTactics.find((config: FormationTacticConfig) => config.formationId === formationId) || null;
};

/**
 * 计算球员战术加成
 * 基于old项目: calcHeroTacticsAttr方法
 */
TacticSchema.methods.calculateHeroBonus = function(heroId: string, formationId: string): HeroTacticBonus {
  // 检查缓存
  const cached = this.heroBonusCache.find((cache: HeroTacticBonus) =>
    cache.heroId === heroId && cache.formationId === formationId && cache.isValid
  );

  if (cached && (Date.now() - cached.cacheTime) < 300000) { // 5分钟缓存
    return cached;
  }

  // 获取阵容战术配置
  const formationTactics = this.getFormationTactics(formationId);
  if (!formationTactics) {
    return {
      heroId,
      formationId,
      attributeBonus: {},
      skillBonus: {},
      cacheTime: Date.now(),
      isValid: false
    };
  }

  // 计算进攻战术加成
  const attackTactic = this.getTactic(formationTactics.attackTacticId);
  const defenseTactic = this.getTactic(formationTactics.defenseTacticId);

  const attributeBonus: Record<string, number> = {};
  const skillBonus: Record<string, number> = {};

  // 应用进攻战术效果
  if (attackTactic && attackTactic.isActive) {
    attackTactic.currentEffects.forEach((effect: TacticEffect) => {
      if (effect.type === TacticEffectType.ATTRIBUTE_BONUS) {
        attributeBonus[effect.attribute] = (attributeBonus[effect.attribute] || 0) + effect.value;
      } else if (effect.type === TacticEffectType.SKILL_BONUS) {
        skillBonus[effect.attribute] = (skillBonus[effect.attribute] || 0) + effect.value;
      }
    });
  }

  // 应用防守战术效果
  if (defenseTactic && defenseTactic.isActive) {
    defenseTactic.currentEffects.forEach((effect: TacticEffect) => {
      if (effect.type === TacticEffectType.ATTRIBUTE_BONUS) {
        attributeBonus[effect.attribute] = (attributeBonus[effect.attribute] || 0) + effect.value;
      } else if (effect.type === TacticEffectType.SKILL_BONUS) {
        skillBonus[effect.attribute] = (skillBonus[effect.attribute] || 0) + effect.value;
      }
    });
  }

  // 创建新的缓存项
  const newBonus: HeroTacticBonus = {
    heroId,
    formationId,
    attributeBonus,
    skillBonus,
    cacheTime: Date.now(),
    isValid: true
  };

  // 更新缓存
  if (cached) {
    Object.assign(cached, newBonus);
  } else {
    this.heroBonusCache.push(newBonus);
  }

  return newBonus;
};

/**
 * 更新战术使用统计
 * 基于old项目: 战术统计逻辑
 */
TacticSchema.methods.updateTacticUsage = function(tacticKey: string, isWin: boolean): void {
  const tactic = this.getTactic(tacticKey);
  if (!tactic) {
    return;
  }

  tactic.usageCount += 1;
  tactic.totalMatches += 1;
  if (isWin) {
    tactic.winCount += 1;
  }
  tactic.lastUsedTime = Date.now();
  this.updateTime = Date.now();
};

/**
 * 清除球员加成缓存
 * 基于缓存管理需求
 */
TacticSchema.methods.invalidateHeroBonusCache = function(heroId?: string, formationId?: string): void {
  this.heroBonusCache.forEach((cache: HeroTacticBonus) => {
    if ((!heroId || cache.heroId === heroId) && (!formationId || cache.formationId === formationId)) {
      cache.isValid = false;
    }
  });
};

/**
 * 生成客户端战术列表
 * 基于old项目: makeClientTacticList方法
 */
TacticSchema.methods.makeClientTacticList = function(): any[] {
  return this.tactics.map((tactic: CharacterTactic) => ({
    tacticId: tactic.tacticId,
    tacticKey: tactic.tacticKey,
    name: tactic.name,
    level: tactic.level,
    experience: tactic.experience,
    isActive: tactic.isActive,
    isUnlocked: tactic.isUnlocked,
    type: tactic.type,
    winRate: tactic.winRate,
    canUpgrade: tactic.canUpgrade,
    requiredExperience: tactic.requiredExperience,
    usageCount: tactic.usageCount,
    effects: tactic.currentEffects
  }));
};
