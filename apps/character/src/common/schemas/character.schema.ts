/**
 * 角色实体Schema
 * 基于旧项目player.js迁移而来，适配新的微服务架构
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

// 游戏进度枚举
export enum CreateRoleStep {
  WATCH_RECORD = 1,
  CREATE_TEAM = 2,
  COMPLETE = 3,
}

// 角色基础信息子文档
@Schema({ _id: false })
export class GameProgress {
  @Prop({ default: CreateRoleStep.WATCH_RECORD })
  createRoleStep: number;

  @Prop({ default: true })
  isNewer: boolean;

  @Prop({ default: false })
  isRobot: boolean;

  @Prop({ default: 0 })
  activeDay: number;

  @Prop({ default: 0 })
  recentActiveDay: number;
}

// 登录信息子文档
@Schema({ _id: false })
export class LoginInfo {
  @Prop({ default: 0 })
  loginTime: number;

  @Prop({ default: 0 })
  leaveTime: number;

  @Prop({ default: 0 })
  createTime: number;

  @Prop({ default: '' })
  ip: string;

  @Prop()
  frontendId?: string;

  @Prop()
  sessionId?: string;
}

// 体力信息子文档
@Schema({ _id: false })
export class EnergyInfo {
  @Prop({ default: 1 })
  buyEnergyCount: number;

  @Prop({ default: 1 })
  buyTime: number;

  @Prop({ default: 1 })
  freeTime: number;

  @Prop({ default: 0 })
  firstFree: number;

  @Prop({ default: 0 })
  secondFree: number;

  @Prop({ default: 0 })
  isEnergyFull: number;

  @Prop({ default: 0 })
  energyRecoverTime: number;
}

// VIP信息子文档
@Schema({ _id: false })
export class VipInfo {
  @Prop({ default: 0 })
  vip: number;

  @Prop({ default: 0 })
  vipExp: number;

  @Prop({ default: [] })
  isFirstRecharge: boolean[];

  @Prop({ default: 0 })
  firstRechargeRefreshTime: number;

  @Prop({ default: 0 })
  totalRecharge: number;
}

// 信仰信息子文档
@Schema({ _id: false })
export class BeliefInfo {
  @Prop({ default: 0 })
  beliefId: number;

  @Prop({ default: 0 })
  honor: number;

  @Prop({ default: 0 })
  beliefNum: number;

  @Prop({ default: 0 })
  beliefLiveness: number;

  @Prop({ default: false })
  lockBelief: boolean;

  @Prop({ default: 0 })
  reChangeBeliefTime: number;

  @Prop({ default: [] })
  beliefChangeRecord: any[];
}

// 主角色Schema
@Schema({ 
  collection: 'characters', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Character {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  characterId: string;

  // 账号信息
  @Prop({ required: true, index: true })
  userId: string;

  // 区服信息
  @Prop({ required: true, index: true })
  serverId: string;

  // 平台外部ID
  @Prop({ required: true })
  openId: string;

  // 角色名称
  @Prop({ required: true, maxlength: 20 })
  name: string;

  // 外观信息
  @Prop({ default: '' })
  avatar: string;

  // 头像信息
  @Prop({ default: 10600011 })
  faceIcon: number;

  @Prop({ default: '' })
  faceUrl: string;

  @Prop({ type: [Number], default: [] })
  clubFaceIcon: number[];

  @Prop({ type: [Number], default: [] })
  countryFaceIcon: number[];

  // 等级和经验
  @Prop({ default: 1, min: 1, max: 100 })
  level: number;

  // 现金
  @Prop({ default: 10000, min: 0 })
  cash: number;

  // 金币
  @Prop({ default: 0, min: 0 })
  gold: number;

  // 体力
  @Prop({ default: 100, min: 0, max: 10000 })
  energy: number;

  // 声望和奖杯
  @Prop({ default: 0, min: 0 })
  fame: number;

  @Prop({ default: 0, min: 0 })
  allFame: number;

  // 奖杯
  @Prop({ default: 0, min: 0 })
  trophy: number;

  // 其他货币
  @Prop({ default: 0, min: 0 })
  worldCoin: number;

  // 合约碎片
  @Prop({ default: 0, min: 0 })
  chip: number;

  // 转播积分
  @Prop({ default: 0, min: 0 })
  integral: number;

  // 球场信息
  @Prop({ default: 0, min: 0 })
  fieldLevel: number;

  @Prop({ default: 0 })
  qualified: number;

  // 联赛信息
  @Prop({ default: 0 })
  league: number;

  // 嵌套文档
  @Prop({ type: GameProgress, default: () => ({}) })
  gameProgress: GameProgress;

  @Prop({ type: LoginInfo, default: () => ({}) })
  loginInfo: LoginInfo;

  @Prop({ type: EnergyInfo, default: () => ({}) })
  energyInfo: EnergyInfo;

  @Prop({ type: VipInfo, default: () => ({}) })
  vipInfo: VipInfo;

  @Prop({ type: BeliefInfo, default: () => ({}) })
  beliefInfo: BeliefInfo;

  // 信仰系统
  @Prop({ default: 0 })
  beliefId: number;     // 信仰ID

  // 兑换码系统
  @Prop({ type: Object, default: {} })
  deemCodeList: Record<string, string>; // 兑换码列表 Group => CodeId

  // 持续buff系统
  @Prop({ default: 0 })
  continuedBuffStartTime: number; // 持续buff开始时间

  @Prop({ default: 0 })
  continuedBuffEndTime: number;   // 持续buff结束时间

  @Prop({ default: 0 })
  lastBuffUpdateTime: number;     // 上一次更新buff的时间

  @Prop({ default: 0 })
  alreadyAddEnergyCount: number;  // 已添加体力的次数

  // 购买记录
  @Prop({ default: false })
  isBuyedCoach: boolean;

  @Prop({ default: 0 })
  buyBestFootballerNum: number;

  @Prop({ default: 0 })
  bestFootballerTime: number;

  // 战术显示
  @Prop({ default: 0 })
  isShowTactics: number;

  // 申请列表
  @Prop({ type: [String], default: [] })
  leagueList: string[];

  @Prop({ type: [Number], default: [] })
  exchangeNumList: number[];

  @Prop({ type: [Object], default: [] })
  associationSponsorReward: any[];

  // 注册时间
  @Prop({ default: 0 })
  regTime: number;

  // ==================== old项目缺失的关键字段 ====================

  // 首次奖励标识
  @Prop({ default: 0 })
  isFristGetReward: number; // 是否第一次领取1亿欧元奖励

  // 阵容激活状态
  @Prop({ default: 0 })
  formationAct: number; // 阵容激活状态

  // 球探系统（基于old项目Scout实体）
  @Prop({
    type: {
      scoutRp: { type: Number, default: 0 }, // 球探RP值
      scoutEnergy: { type: Number, default: 100 }, // 球探体力
      scoutGroup: [{
        type: { type: Number, required: true }, // 球探类型 (1=初级, 2=高级, 3=顶级)
        count: { type: Number, default: 0 }, // 可用次数
        getTime: { type: Number, default: 1 }, // 获取时间
      }],
      scoutPack: [{ type: MongooseSchema.Types.Mixed }], // 球探包中的球员数据
      isFrist: { type: Number, default: 0 }, // 是否第一次抽 (0=是, 1=不是)
      reTime: { type: Number, default: Date.now }, // 刷新时间
      lastEnergyRegenTime: { type: Date, default: Date.now }, // 最后体力恢复时间
      lastSearchTime: { type: Number, default: 0 }, // 最后搜索时间
      maxScoutPackSize: { type: Number, default: 10 }, // 球探包最大容量
    },
    default: () => ({
      scoutRp: 0,
      scoutEnergy: 100,
      scoutGroup: [
        { type: 1, count: 5, getTime: 1 }, // 初级球探组
        { type: 2, count: 1, getTime: 1 }, // 高级球探组
        { type: 3, count: 1, getTime: 1 }, // 顶级球探组
      ],
      scoutPack: [],
      isFrist: 0,
      reTime: Date.now(),
      lastEnergyRegenTime: new Date(),
      lastSearchTime: 0,
      maxScoutPackSize: 10,
    })
  })
  scoutData: {
    scoutRp: number;
    scoutEnergy: number;
    scoutGroup: Array<{
      type: number;
      count: number;
      getTime: number;
    }>;
    scoutPack: any[];
    isFrist: number;
    reTime: number;
    lastEnergyRegenTime: Date;
    lastSearchTime: number;
    maxScoutPackSize: number;
  };

  // 虚拟字段：是否在线
  get isOnline(): boolean {
    return !!(this.loginInfo?.frontendId && this.loginInfo?.sessionId);
  }

  // 虚拟字段：当前阵容ID（通过微服务获取）
  currentFormationId?: string;

  // 虚拟字段：球员数量（通过微服务获取）
  heroCount?: number;
}

export const CharacterSchema = SchemaFactory.createForClass(Character);

// 定义Document类型
export type CharacterDocument = Character & Document;

// 创建索引
CharacterSchema.index({ characterId: 1 }, { unique: true });
CharacterSchema.index({ userId: 1 });
CharacterSchema.index({ serverId: 1 });
CharacterSchema.index({ openId: 1 });
CharacterSchema.index({ level: 1 });
CharacterSchema.index({ 'vipInfo.vip': 1 });
CharacterSchema.index({ 'loginInfo.loginTime': 1 });
CharacterSchema.index({ 'loginInfo.createTime': 1 });

// 添加虚拟字段
CharacterSchema.virtual('isOnline').get(function() {
  return !!(this.loginInfo?.frontendId && this.loginInfo?.sessionId);
});

// 添加实例方法
CharacterSchema.methods.toCharacterInfo = function() {
  return {
    characterId: this.characterId,
    name: this.name,
    level: this.level,
    fame: this.fame,
    cash: this.cash,
    gold: this.gold,
    energy: this.energy,
    trophy: this.trophy,
    faceIcon: this.faceIcon,
    faceUrl: this.faceUrl,
    vip: this.vipInfo.vip,
    isOnline: this.isOnline,
  };
};

CharacterSchema.methods.getBattleData = function() {
  return {
    characterId: this.characterId,
    name: this.name,
    level: this.level,
    fieldLevel: this.fieldLevel,
    faceIcon: this.faceIcon,
    faceUrl: this.faceUrl,
    // 关联数据通过微服务获取
    // heroes: 通过HeroService获取
    // formations: 通过FormationService获取
    // items: 通过InventoryService获取
  };
};

// 定义方法接口 - 基于CharacterService的真实业务逻辑
export interface CharacterMethods {
  // 基础信息方法
  toCharacterInfo(): any;
  getBattleData(): any;

  // 货币管理方法 - 基于CharacterService的货币操作逻辑
  addCurrency(currencyType: string, amount: number): boolean;
  subtractCurrency(currencyType: string, amount: number): boolean;
  getCurrencyBalance(currencyType: string): number;
  hasSufficientCurrency(currencyType: string, amount: number): boolean;

  // 等级和经验管理 - 基于CharacterService的升级逻辑
  canLevelUp(): boolean;
  getNextLevelRequirement(): number;
  addExperience(experience: number): boolean;

  // VIP系统管理 - 基于CharacterService的VIP逻辑
  isVip(): boolean;
  getVipLevel(): number;
  getVipExpireTime(): number;
  isVipExpired(): boolean;

  // 体力系统管理 - 基于CharacterService的体力逻辑
  addEnergy(amount: number): boolean;
  subtractEnergy(amount: number): boolean;
  isEnergyFull(): boolean;
  getEnergyRegenTime(): number;

  // 球探系统管理 - 基于CharacterService的球探逻辑
  getScoutEnergy(): number;
  addScoutEnergy(amount: number): boolean;
  subtractScoutEnergy(amount: number): boolean;
  canUseScout(scoutType: number): boolean;
  updateScoutEnergyRegen(): void;

  // 登录和活跃度管理 - 基于CharacterService的登录逻辑
  updateLoginTime(): void;
  isOnlineNow(): boolean;
  getDaysActive(): number;
  updateActiveDay(): void;

  // 游戏进度管理 - 基于CharacterService的进度逻辑
  isNewPlayer(): boolean;
  completeNewPlayerGuide(): void;
  updateCreateRoleStep(step: number): void;

  // 数据统计和分析
  getTotalWealth(): number;
  getAccountAge(): number;
  getLastLoginDays(): number;
}

// 重新定义Document类型
export type CharacterDocument = Character & Document & CharacterMethods;
