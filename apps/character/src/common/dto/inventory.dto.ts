/**
 * 统一的角色物品背包DTO定义
 * 合并ItemDto和InventoryDto的功能
 * 提供完整的API请求和响应数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsArray, IsBoolean, IsEnum, ValidateNested, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { InventoryTabType, ItemUseType } from '../schemas/inventory.schema';

// ==================== 基础DTO ====================

/**
 * 物品实例DTO
 */
export class ItemInstanceDto {
  @ApiProperty({ description: '物品实例UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '物品配置ID' })
  @IsNumber()
  resId: number;

  @ApiProperty({ description: '物品数量' })
  @IsNumber()
  @Min(1)
  num: number;

  @ApiPropertyOptional({ description: '绑定状态：0-未绑定，1-已绑定', default: 0 })
  @IsNumber()
  @IsOptional()
  bind?: number;

  @ApiProperty({ description: '物品类型' })
  @IsNumber()
  type: number;

  @ApiPropertyOptional({ description: '时间限制：0xFFFF 无限制时间', default: 0 })
  @IsNumber()
  @IsOptional()
  timeLimit?: number;

  @ApiPropertyOptional({ description: '是否失效：0-未失效，1-已失效', default: 0 })
  @IsNumber()
  @IsOptional()
  invalid?: number;

  @ApiPropertyOptional({ description: '在页签中的位置，-1表示未放入背包', default: -1 })
  @IsNumber()
  @IsOptional()
  slot?: number;

  @ApiPropertyOptional({ description: '所属页签ID，0表示未分配', default: 0 })
  @IsNumber()
  @IsOptional()
  tabId?: number;

  @ApiPropertyOptional({ description: '获得时间' })
  @IsNumber()
  @IsOptional()
  acquiredTime?: number;

  @ApiPropertyOptional({ description: '过期时间（0表示永不过期）', default: 0 })
  @IsNumber()
  @IsOptional()
  expireTime?: number;
}

/**
 * 页签实例DTO
 */
export class InventoryTabDto {
  @ApiProperty({ description: '页签ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: '页签名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '页签类型', enum: InventoryTabType })
  @IsEnum(InventoryTabType)
  type: InventoryTabType;

  @ApiPropertyOptional({ description: '当前容量', default: 50 })
  @IsNumber()
  @Min(1)
  @Max(200)
  @IsOptional()
  capacity?: number;

  @ApiPropertyOptional({ description: '扩展次数', default: 0 })
  @IsNumber()
  @Min(0)
  @IsOptional()
  expandCount?: number;

  @ApiPropertyOptional({ description: '下次扩展费用', default: 1000 })
  @IsNumber()
  @Min(0)
  @IsOptional()
  nextExpandCost?: number;

  @ApiPropertyOptional({ description: '创建时间' })
  @IsNumber()
  @IsOptional()
  createTime?: number;

  @ApiPropertyOptional({ description: '更新时间' })
  @IsNumber()
  @IsOptional()
  updateTime?: number;
}

// ==================== 请求DTO ====================

/**
 * 创建角色物品数据DTO
 */
export class CreateCharacterItemsDto {
  @ApiProperty({ description: '玩家UID' })
  @IsString()
  uid: string;

  @ApiPropertyOptional({ description: '物品实例列表', type: [ItemInstanceDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ItemInstanceDto)
  @IsOptional()
  items?: ItemInstanceDto[];

  @ApiPropertyOptional({ description: '页签列表', type: [InventoryTabDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InventoryTabDto)
  @IsOptional()
  tabs?: InventoryTabDto[];
}

/**
 * 添加物品请求DTO
 */
export class AddItemDto {
  @ApiProperty({ description: '角色UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '物品配置ID' })
  @IsNumber()
  @Min(1)
  resId: number;

  @ApiProperty({ description: '物品数量' })
  @IsNumber()
  @Min(1)
  quantity: number;
}

/**
 * 移除物品请求DTO
 */
export class RemoveItemDto {
  @ApiProperty({ description: '角色UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '物品实例UID' })
  @IsString()
  itemUid: string;

  @ApiPropertyOptional({ description: '移除数量，不指定则移除全部' })
  @IsNumber()
  @Min(1)
  @IsOptional()
  quantity?: number;
}

/**
 * 移动物品请求DTO
 */
export class MoveItemDto {
  @ApiProperty({ description: '角色UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '物品实例UID' })
  @IsString()
  itemUid: string;

  @ApiProperty({ description: '目标页签ID' })
  @IsNumber()
  @Min(1)
  tabId: number;

  @ApiPropertyOptional({ description: '目标位置，不指定则自动分配' })
  @IsNumber()
  @Min(1)
  @IsOptional()
  slot?: number;
}

/**
 * 批量添加物品项DTO
 */
export class BatchAddItemDto {
  @ApiProperty({ description: '物品配置ID' })
  @IsNumber()
  @Min(1)
  resId: number;

  @ApiProperty({ description: '物品数量' })
  @IsNumber()
  @Min(1)
  quantity: number;
}

/**
 * 批量添加物品请求DTO
 */
export class BatchAddItemsDto {
  @ApiProperty({ description: '角色UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '要添加的物品列表', type: [BatchAddItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchAddItemDto)
  items: BatchAddItemDto[];
}

/**
 * 使用物品请求DTO
 */
export class UseItemDto {
  @ApiProperty({ description: '角色UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '物品实例UID' })
  @IsString()
  itemUid: string;

  @ApiPropertyOptional({ description: '使用数量', default: 1 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  useCount?: number;
}

/**
 * 批量使用物品项DTO
 */
export class BatchUseItemDto {
  @ApiProperty({ description: '物品实例UID' })
  @IsString()
  itemUid: string;

  @ApiProperty({ description: '使用数量' })
  @IsNumber()
  @Min(1)
  useCount: number;
}

/**
 * 批量使用物品请求DTO
 */
export class BatchUseItemsDto {
  @ApiProperty({ description: '角色UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '要使用的物品列表', type: [BatchUseItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchUseItemDto)
  items: BatchUseItemDto[];
}

/**
 * 扩展背包请求DTO
 */
export class ExpandBagDto {
  @ApiProperty({ description: '角色UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '页签ID' })
  @IsNumber()
  @Min(1)
  tabId: number;

  @ApiPropertyOptional({ description: '扩展次数', default: 1 })
  @IsNumber()
  @Min(1)
  @Max(10)
  @IsOptional()
  expandCount?: number;
}

/**
 * 背包排序请求DTO
 */
export class SortBagDto {
  @ApiProperty({ description: '角色UID' })
  @IsString()
  uid: string;

  @ApiProperty({ description: '页签ID' })
  @IsNumber()
  @Min(1)
  tabId: number;

  @ApiPropertyOptional({ 
    description: '排序类型：slot-位置，resId-配置ID，type-类型，num-数量，acquiredTime-获得时间', 
    default: 'slot' 
  })
  @IsString()
  @IsOptional()
  sortType?: string;
}

// ==================== 响应DTO ====================

/**
 * 添加物品响应DTO
 */
export class AddItemResponseDto {
  @ApiProperty({ description: '是否成功' })
  @IsBoolean()
  success: boolean;

  @ApiProperty({ description: '新增的物品UID列表', type: [String] })
  @IsArray()
  @IsString({ each: true })
  addedUids: string[];

  @ApiProperty({ description: '使用的槽位数量' })
  @IsNumber()
  @Min(0)
  slotsUsed: number;
}

/**
 * 移除物品响应DTO
 */
export class RemoveItemResponseDto {
  @ApiProperty({ description: '是否成功' })
  @IsBoolean()
  success: boolean;

  @ApiProperty({ description: '实际移除的数量' })
  @IsNumber()
  @Min(0)
  removedQuantity: number;
}

/**
 * 移动物品响应DTO
 */
export class MoveItemResponseDto {
  @ApiProperty({ description: '是否成功' })
  @IsBoolean()
  success: boolean;

  @ApiProperty({ description: '新的槽位位置' })
  @IsNumber()
  newSlot: number;
}

/**
 * 扩展背包响应DTO
 */
export class ExpandBagResponseDto {
  @ApiProperty({ description: '是否成功' })
  @IsBoolean()
  success: boolean;

  @ApiProperty({ description: '扩展费用' })
  @IsNumber()
  @Min(0)
  cost: number;

  @ApiProperty({ description: '新的容量' })
  @IsNumber()
  @Min(1)
  newCapacity: number;
}

/**
 * 背包统计响应DTO
 */
export class BagStatisticsResponseDto {
  @ApiProperty({ description: '总物品数量' })
  @IsNumber()
  @Min(0)
  totalItems: number;

  @ApiProperty({ description: '总容量' })
  @IsNumber()
  @Min(0)
  totalCapacity: number;

  @ApiProperty({ description: '已使用容量' })
  @IsNumber()
  @Min(0)
  usedCapacity: number;

  @ApiProperty({ description: '使用率（百分比）' })
  @IsNumber()
  @Min(0)
  @Max(100)
  usageRate: number;

  @ApiProperty({ description: '页签统计信息' })
  @IsArray()
  tabStats: Array<{
    id: number;
    name: string;
    itemCount: number;
    capacity: number;
    usageRate: number;
  }>;
}

/**
 * 验证背包响应DTO
 */
export class ValidateBagResponseDto {
  @ApiProperty({ description: '是否有效' })
  @IsBoolean()
  isValid: boolean;

  @ApiProperty({ description: '发现的问题列表', type: [String] })
  @IsArray()
  @IsString({ each: true })
  issues: string[];
}

/**
 * 修复背包响应DTO
 */
export class RepairBagResponseDto {
  @ApiProperty({ description: '是否进行了修复' })
  @IsBoolean()
  repaired: boolean;

  @ApiProperty({ description: '修复的问题数量' })
  @IsNumber()
  @Min(0)
  fixedIssues: number;
}

/**
 * 清理过期物品响应DTO
 */
export class CleanExpiredItemsResponseDto {
  @ApiProperty({ description: '清理的物品数量' })
  @IsNumber()
  @Min(0)
  cleanedCount: number;
}
