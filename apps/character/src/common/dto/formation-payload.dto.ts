/**
 * Formation模块的Payload DTO定义
 * 
 * 为formation.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min, Max, Length, IsArray } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 阵容基础操作相关 ====================

/**
 * 获取角色阵容Payload DTO
 * @MessagePattern('formation.getFormations')
 */
export class GetFormationsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 创建阵容Payload DTO
 * @MessagePattern('formation.createFormation')
 */
export class CreateFormationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵型配置ID', example: 4231 })
  @Expose()
  @IsNumber({}, { message: '阵型配置ID必须是数字' })
  @Min(1, { message: '阵型配置ID不能小于1' })
  @Max(99999, { message: '阵型配置ID不能大于99999' })
  resId: number;

  @ApiPropertyOptional({ description: '阵容类型（默认为1）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '阵容类型必须是数字' })
  @Min(1, { message: '阵容类型不能小于1' })
  @Max(10, { message: '阵容类型不能大于10' })
  type?: number;
}

// ==================== 2. 球员位置操作相关 ====================

/**
 * 添加球员到位置Payload DTO
 * @MessagePattern('formation.addHeroToPosition')
 */
export class AddHeroToPositionPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;

  @ApiProperty({ description: '位置', example: 'GK', enum: ['GK', 'DC', 'DL', 'DR', 'MC', 'ML', 'MR', 'ST'] })
  @Expose()
  @IsString({ message: '位置必须是字符串' })
  @Length(1, 10, { message: '位置长度必须在1-10个字符之间' })
  position: string;

  @ApiProperty({ description: '位置索引', example: 0 })
  @Expose()
  @IsNumber({}, { message: '位置索引必须是数字' })
  @Min(0, { message: '位置索引不能小于0' })
  @Max(10, { message: '位置索引不能大于10' })
  index: number;

  @ApiProperty({ description: '球员ID', example: 'hero_11111' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 从位置移除球员Payload DTO
 * @MessagePattern('formation.removeHeroFromPosition')
 */
export class RemoveHeroFromPositionPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;

  @ApiProperty({ description: '位置', example: 'GK', enum: ['GK', 'DC', 'DL', 'DR', 'MC', 'ML', 'MR', 'ST'] })
  @Expose()
  @IsString({ message: '位置必须是字符串' })
  @Length(1, 10, { message: '位置长度必须在1-10个字符之间' })
  position: string;

  @ApiProperty({ description: '球员ID', example: 'hero_11111' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

// ==================== 3. 阵容设置相关 ====================

/**
 * 设置激活阵容Payload DTO
 * @MessagePattern('formation.setActiveFormation')
 */
export class SetActiveFormationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;
}

/**
 * 设置联赛阵容Payload DTO
 * @MessagePattern('formation.setLeagueFormation')
 */
export class SetLeagueFormationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;
}

/**
 * 设置信仰之战阵容Payload DTO
 * @MessagePattern('formation.setWarOfFaithFormation')
 */
export class SetWarOfFaithFormationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;
}

// ==================== 4. 高级操作相关 ====================

/**
 * 自动布阵Payload DTO
 * @MessagePattern('formation.autoFormation')
 */
export class AutoFormationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;

  @ApiPropertyOptional({ description: '指定球员ID列表（可选）', type: [String] })
  @Expose()
  @IsOptional()
  @IsArray({ message: '球员ID列表必须是数组' })
  @IsString({ each: true, message: '球员ID必须是字符串' })
  heroIds?: string[];
}

/**
 * 复制阵容Payload DTO
 * @MessagePattern('formation.copyFormation')
 */
export class CopyFormationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '源阵容ID', example: 'formation_source' })
  @Expose()
  @IsString({ message: '源阵容ID必须是字符串' })
  @Length(1, 50, { message: '源阵容ID长度必须在1-50个字符之间' })
  sourceFormationId: string;
}

/**
 * 设置阵容战术Payload DTO
 * @MessagePattern('formation.setFormationTactics')
 */
export class SetFormationTacticsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容唯一标识', example: 'formation_uid_123' })
  @Expose()
  @IsString({ message: '阵容唯一标识必须是字符串' })
  @Length(1, 50, { message: '阵容唯一标识长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '战术配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '战术配置ID必须是数字' })
  @Min(1, { message: '战术配置ID不能小于1' })
  @Max(99999, { message: '战术配置ID不能大于99999' })
  resId: number;

  @ApiProperty({ description: '战术类型', example: 'attack', enum: ['attack', 'defense', 'balanced'] })
  @Expose()
  @IsString({ message: '战术类型必须是字符串' })
  @Length(1, 20, { message: '战术类型长度必须在1-20个字符之间' })
  tacticsType: string;
}
