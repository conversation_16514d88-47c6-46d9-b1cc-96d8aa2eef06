import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScoutController } from './scout.controller';
import { ScoutService } from './scout.service';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { Hero, HeroSchema } from '@hero/common/schemas/hero.schema';

/**
 * 球探系统模块
 * 处理球探包、球员签约等功能
 *
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - MongooseModule.forFeature()用于注册特定Schema，需要在使用的模块中注册
 */
@Module({
  imports: [
    // 注册Hero Schema（HeroRepository需要）
    MongooseModule.forFeature([
      { name: Hero.name, schema: HeroSchema },
    ]),
  ],
  controllers: [ScoutController],
  providers: [
    ScoutService,
    HeroRepository, // 共享的Hero数据访问层
  ],
  exports: [ScoutService],
})
export class ScoutModule {}
