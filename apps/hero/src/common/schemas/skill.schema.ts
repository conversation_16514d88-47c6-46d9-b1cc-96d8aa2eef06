/**
 * 球员技能实例Schema
 * 管理球员拥有的技能实例，包括等级、激活状态等
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 技能激活状态枚举
export enum SkillActiveStatus {
  INACTIVE = 0,   // 未激活
  ACTIVE = 1,     // 已激活
  LOCKED = 2,     // 已锁定
}

// 主球员技能实例Schema
@Schema({ 
  collection: 'skills', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Skill {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  skillId: string;      // 球员技能实例ID

  @Prop({ required: true, index: true })
  heroId: string;       // 球员ID

  @Prop({ required: true, index: true })
  characterId: string;  // 角色ID

  @Prop({ required: true, index: true })
  serverId: string;     // 区服ID

  @Prop({ required: true, index: true })
  configId: number;     // 技能配置ID

  // 技能状态
  @Prop({ default: 1, min: 1 })
  level: number;        // 当前等级

  @Prop({ default: 0 })
  experience: number;   // 当前经验值

  @Prop({ default: SkillActiveStatus.INACTIVE, enum: SkillActiveStatus })
  activeStatus: SkillActiveStatus; // 激活状态

  @Prop({ default: 0 })
  slotPosition: number; // 技能槽位置（0表示未装备）

  // 使用统计
  @Prop({ default: 0 })
  usageCount: number;   // 使用次数

  @Prop({ default: 0 })
  lastUsedTime: number; // 最后使用时间

  @Prop({ default: 0 })
  totalDamage: number;  // 总伤害

  @Prop({ default: 0 })
  totalHealing: number; // 总治疗

  // 升级历史
  @Prop({ default: 0 })
  upgradeCount: number; // 升级次数

  @Prop({ default: 0 })
  totalUpgradeCost: number; // 总升级费用

  @Prop({ default: 0 })
  lastUpgradeTime: number; // 最后升级时间

  // 获得信息
  @Prop({ default: 0 })
  obtainTime: number;   // 获得时间

  @Prop({ default: '' })
  obtainSource: string; // 获得来源（抽卡、任务、商店等）

  @Prop({ default: 0 })
  obtainCost: number;   // 获得费用

  // 锁定信息
  @Prop({ default: false })
  isLocked: boolean;    // 是否锁定

  @Prop({ default: 0 })
  lockTime: number;     // 锁定时间

  @Prop({ default: '' })
  lockReason: string;   // 锁定原因

  // 虚拟字段：是否已激活
  get isActive(): boolean {
    return this.activeStatus === SkillActiveStatus.ACTIVE;
  }

  // 虚拟字段：是否已装备
  get isEquipped(): boolean {
    return this.slotPosition > 0;
  }

  // 虚拟字段：是否可升级
  get canUpgrade(): boolean {
    return !this.isLocked && this.activeStatus !== SkillActiveStatus.LOCKED;
  }

  // 虚拟字段：下一级所需经验
  get nextLevelExp(): number {
    return Math.pow(this.level, 2) * 100;
  }

  // 虚拟字段：升级进度百分比
  get upgradeProgress(): number {
    const nextExp = this.nextLevelExp;
    return nextExp > 0 ? Math.min((this.experience / nextExp) * 100, 100) : 100;
  }
}

export const SkillSchema = SchemaFactory.createForClass(Skill);

// 定义方法接口 - 基于SkillService的真实业务逻辑
export interface SkillMethods {
  // 基础方法（已存在）
  addExperience(exp: number): number;
  activate(slotPosition?: number): this;
  deactivate(): this;
  lock(reason?: string): this;
  unlock(): this;
  use(damage?: number, healing?: number): this;
  getEffectiveLevel(): number;

  // 新增实用业务方法 - 基于SkillService真实逻辑

  // 升级管理 - 基于SkillService
  canUpgradeToLevel(targetLevel: number): boolean;
  getUpgradeCost(targetLevel: number): number;
  upgradeToLevel(targetLevel: number): boolean;
  getUpgradeProgress(): { current: number; required: number; percentage: number };

  // 装备管理 - 基于SkillService
  equipToSlot(slotPosition: number): boolean;
  unequipFromSlot(): boolean;
  canEquipToSlot(slotPosition: number): boolean;

  // 状态管理 - 基于SkillService
  updateUsageStats(damage: number, healing: number): void;
  resetUsageStats(): void;
  updateUpgradeStats(cost: number): void;

  // 数据验证 - 基于SkillService
  validateSkillData(): { isValid: boolean; errors: string[] };
  isMaxLevel(): boolean;
  hasEnoughExperience(requiredExp: number): boolean;

  // 数据转换 - 基于SkillService
  toSkillInfoDto(): any;
  toClientSkillData(): any;
  getSkillSummary(): any;
}

// 定义Document类型
export type SkillDocument = Skill & Document & SkillMethods;

// 创建索引
SkillSchema.index({ skillId: 1 }, { unique: true });
SkillSchema.index({ heroId: 1 });
SkillSchema.index({ characterId: 1 });
SkillSchema.index({ serverId: 1 });
SkillSchema.index({ configId: 1 });
SkillSchema.index({ heroId: 1, configId: 1 }, { unique: true }); // 球员不能拥有重复技能
SkillSchema.index({ activeStatus: 1 });
SkillSchema.index({ slotPosition: 1 });
SkillSchema.index({ level: 1 });
SkillSchema.index({ obtainTime: 1 });

// 添加虚拟字段
SkillSchema.virtual('isActive').get(function() {
  return this.activeStatus === SkillActiveStatus.ACTIVE;
});

SkillSchema.virtual('isEquipped').get(function() {
  return this.slotPosition > 0;
});

SkillSchema.virtual('canUpgrade').get(function() {
  return !this.isLocked && this.activeStatus !== SkillActiveStatus.LOCKED;
});

SkillSchema.virtual('nextLevelExp').get(function() {
  return Math.pow(this.level, 2) * 100;
});

SkillSchema.virtual('upgradeProgress').get(function() {
  const nextExp = this.nextLevelExp;
  return nextExp > 0 ? Math.min((this.experience / nextExp) * 100, 100) : 100;
});

// 添加实例方法
SkillSchema.methods.addExperience = function(exp: number) {
  this.experience += exp;
  
  // 检查是否可以升级
  while (this.experience >= this.nextLevelExp && this.level < 10) { // 假设最大等级为10
    this.experience -= this.nextLevelExp;
    this.level += 1;
    this.upgradeCount += 1;
    this.lastUpgradeTime = Date.now();
  }
  
  return this.level;
};

SkillSchema.methods.activate = function(slotPosition?: number) {
  this.activeStatus = SkillActiveStatus.ACTIVE;
  if (slotPosition) {
    this.slotPosition = slotPosition;
  }
  return this;
};

SkillSchema.methods.deactivate = function() {
  this.activeStatus = SkillActiveStatus.INACTIVE;
  this.slotPosition = 0;
  return this;
};

SkillSchema.methods.lock = function(reason: string = '') {
  this.isLocked = true;
  this.lockTime = Date.now();
  this.lockReason = reason;
  this.activeStatus = SkillActiveStatus.LOCKED;
  return this;
};

SkillSchema.methods.unlock = function() {
  this.isLocked = false;
  this.lockTime = 0;
  this.lockReason = '';
  if (this.activeStatus === SkillActiveStatus.LOCKED) {
    this.activeStatus = SkillActiveStatus.INACTIVE;
  }
  return this;
};

SkillSchema.methods.use = function(damage: number = 0, healing: number = 0) {
  this.usageCount += 1;
  this.lastUsedTime = Date.now();
  this.totalDamage += damage;
  this.totalHealing += healing;
  return this;
};

SkillSchema.methods.getEffectiveLevel = function() {
  // 考虑装备、buff等因素的有效等级
  return this.level;
};

// ==================== 新增实用业务方法实现 ====================

/**
 * 检查是否可以升级到指定等级
 * 基于SkillService: upgradeSkill方法中的升级条件检查逻辑
 */
SkillSchema.methods.canUpgradeToLevel = function(targetLevel: number): boolean {
  // 检查目标等级是否有效
  if (targetLevel <= this.level || targetLevel > 10) return false;

  // 检查是否被锁定
  if (this.isLocked || this.activeStatus === SkillActiveStatus.LOCKED) return false;

  // 检查经验是否足够
  const requiredExp = this.getUpgradeCost(targetLevel);
  return this.experience >= requiredExp;
};

/**
 * 获取升级到指定等级的费用
 * 基于SkillService: calculateUpgradeCost方法逻辑
 */
SkillSchema.methods.getUpgradeCost = function(targetLevel: number): number {
  let totalCost = 0;

  for (let level = this.level + 1; level <= targetLevel; level++) {
    // 基于service中的升级费用公式
    totalCost += level * 1000;
  }

  return totalCost;
};

/**
 * 升级到指定等级
 * 基于SkillService: upgradeSkill方法逻辑
 */
SkillSchema.methods.upgradeToLevel = function(targetLevel: number): boolean {
  if (!this.canUpgradeToLevel(targetLevel)) return false;

  const upgradeCost = this.getUpgradeCost(targetLevel);

  // 扣除经验（这里简化处理，实际可能需要扣除金币等资源）
  this.experience -= upgradeCost;
  this.level = targetLevel;
  this.upgradeCount += 1;
  this.lastUpgradeTime = Date.now();
  this.totalUpgradeCost += upgradeCost;

  return true;
};

/**
 * 获取升级进度
 * 基于SkillService: 升级进度计算逻辑
 */
SkillSchema.methods.getUpgradeProgress = function(): { current: number; required: number; percentage: number } {
  if (this.level >= 10) {
    return { current: this.experience, required: 0, percentage: 100 };
  }

  const requiredExp = this.nextLevelExp;
  const percentage = requiredExp > 0 ? Math.min((this.experience / requiredExp) * 100, 100) : 100;

  return {
    current: this.experience,
    required: requiredExp,
    percentage: Math.round(percentage)
  };
};

/**
 * 装备到技能槽
 * 基于SkillService: activateSkill方法逻辑
 */
SkillSchema.methods.equipToSlot = function(slotPosition: number): boolean {
  if (!this.canEquipToSlot(slotPosition)) return false;

  this.activeStatus = SkillActiveStatus.ACTIVE;
  this.slotPosition = slotPosition;

  return true;
};

/**
 * 从技能槽卸下
 * 基于SkillService: deactivateSkill方法逻辑
 */
SkillSchema.methods.unequipFromSlot = function(): boolean {
  if (!this.isEquipped) return false;

  this.activeStatus = SkillActiveStatus.INACTIVE;
  this.slotPosition = 0;

  return true;
};

/**
 * 检查是否可以装备到指定槽位
 * 基于SkillService: activateSkill方法中的装备条件检查逻辑
 */
SkillSchema.methods.canEquipToSlot = function(slotPosition: number): boolean {
  // 检查槽位是否有效（基于DTO中的限制：1-3）
  if (slotPosition < 1 || slotPosition > 3) return false;

  // 检查技能是否被锁定
  if (this.isLocked || this.activeStatus === SkillActiveStatus.LOCKED) return false;

  return true;
};

/**
 * 更新使用统计
 * 基于SkillService: 技能使用统计逻辑
 */
SkillSchema.methods.updateUsageStats = function(damage: number, healing: number): void {
  this.usageCount += 1;
  this.lastUsedTime = Date.now();
  this.totalDamage += damage;
  this.totalHealing += healing;
};

/**
 * 重置使用统计
 * 基于SkillService: 统计重置逻辑
 */
SkillSchema.methods.resetUsageStats = function(): void {
  this.usageCount = 0;
  this.lastUsedTime = 0;
  this.totalDamage = 0;
  this.totalHealing = 0;
};

/**
 * 更新升级统计
 * 基于SkillService: upgradeSkill方法中的统计更新逻辑
 */
SkillSchema.methods.updateUpgradeStats = function(cost: number): void {
  this.upgradeCount += 1;
  this.totalUpgradeCost += cost;
  this.lastUpgradeTime = Date.now();
};

/**
 * 验证技能数据
 * 基于SkillService: 数据验证逻辑
 */
SkillSchema.methods.validateSkillData = function(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 检查基础数据
  if (!this.skillId || this.skillId.trim() === '') {
    errors.push('技能ID不能为空');
  }

  if (!this.heroId || this.heroId.trim() === '') {
    errors.push('球员ID不能为空');
  }

  if (!this.configId || this.configId <= 0) {
    errors.push('技能配置ID无效');
  }

  // 检查等级范围
  if (this.level < 1 || this.level > 10) {
    errors.push('技能等级必须在1-10之间');
  }

  // 检查经验值
  if (this.experience < 0) {
    errors.push('经验值不能为负数');
  }

  // 检查槽位
  if (this.slotPosition < 0 || this.slotPosition > 3) {
    errors.push('技能槽位必须在0-3之间');
  }

  // 检查激活状态与槽位的一致性
  if (this.activeStatus === SkillActiveStatus.ACTIVE && this.slotPosition === 0) {
    errors.push('已激活的技能必须有有效的槽位');
  }

  if (this.activeStatus !== SkillActiveStatus.ACTIVE && this.slotPosition > 0) {
    errors.push('未激活的技能不应该占用槽位');
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * 检查是否达到最大等级
 * 基于SkillService: 等级限制逻辑
 */
SkillSchema.methods.isMaxLevel = function(): boolean {
  return this.level >= 10;
};

/**
 * 检查经验是否足够
 * 基于SkillService: 经验检查逻辑
 */
SkillSchema.methods.hasEnoughExperience = function(requiredExp: number): boolean {
  return this.experience >= requiredExp;
};

/**
 * 转换为SkillInfoDto
 * 基于SkillService: toSkillInfoDto方法逻辑
 */
SkillSchema.methods.toSkillInfoDto = function(): any {
  return {
    skillId: this.skillId,
    heroId: this.heroId,
    configId: this.configId,
    level: this.level,
    experience: this.experience,
    activeStatus: this.activeStatus,
    slotPosition: this.slotPosition,
    usageCount: this.usageCount,
    lastUsedTime: this.lastUsedTime,
    upgradeCount: this.upgradeCount,
    totalUpgradeCost: this.totalUpgradeCost,
    obtainTime: this.obtainTime,
    obtainSource: this.obtainSource,
    isLocked: this.isLocked,
    isActive: this.isActive,
    isEquipped: this.isEquipped,
    canUpgrade: this.canUpgrade,
    nextLevelExp: this.nextLevelExp,
    upgradeProgress: this.upgradeProgress
  };
};

/**
 * 转换为客户端技能数据
 * 基于SkillService: 客户端数据转换需求
 */
SkillSchema.methods.toClientSkillData = function(): any {
  const progress = this.getUpgradeProgress();

  return {
    skillId: this.skillId,
    configId: this.configId,
    level: this.level,
    experience: this.experience,
    isActive: this.isActive,
    isEquipped: this.isEquipped,
    slotPosition: this.slotPosition,
    canUpgrade: this.canUpgrade && !this.isMaxLevel(),
    upgradeProgress: progress.percentage,
    usageCount: this.usageCount,
    totalDamage: this.totalDamage,
    totalHealing: this.totalHealing,
    obtainTime: this.obtainTime,
    obtainSource: this.obtainSource
  };
};

/**
 * 获取技能摘要
 * 基于SkillService: 技能摘要信息获取需求
 */
SkillSchema.methods.getSkillSummary = function(): any {
  const validation = this.validateSkillData();
  const progress = this.getUpgradeProgress();

  return {
    skillId: this.skillId,
    heroId: this.heroId,
    configId: this.configId,
    level: this.level,
    maxLevel: 10,
    isMaxLevel: this.isMaxLevel(),
    experience: this.experience,
    upgradeProgress: progress.percentage,
    activeStatus: this.activeStatus,
    isActive: this.isActive,
    isEquipped: this.isEquipped,
    slotPosition: this.slotPosition,
    canUpgrade: this.canUpgrade && !this.isMaxLevel(),
    isLocked: this.isLocked,
    usageStats: {
      usageCount: this.usageCount,
      totalDamage: this.totalDamage,
      totalHealing: this.totalHealing,
      lastUsedTime: this.lastUsedTime
    },
    upgradeStats: {
      upgradeCount: this.upgradeCount,
      totalUpgradeCost: this.totalUpgradeCost,
      lastUpgradeTime: this.lastUpgradeTime
    },
    validation: validation,
    obtainInfo: {
      obtainTime: this.obtainTime,
      obtainSource: this.obtainSource,
      obtainCost: this.obtainCost
    }
  };
};

SkillSchema.methods.calculateUpgradeCost = function(targetLevel: number) {
  let totalCost = 0;
  for (let level = this.level + 1; level <= targetLevel; level++) {
    // 基础升级费用公式
    totalCost += level * 1000;
  }
  return totalCost;
};
