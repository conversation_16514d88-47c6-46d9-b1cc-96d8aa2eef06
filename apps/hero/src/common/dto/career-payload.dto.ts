/**
 * Career模块的Payload DTO定义
 * 
 * 为career.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min, Max, Length, IsArray } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 合约管理相关 ====================

/**
 * 增加球员合约天数Payload DTO
 * @MessagePattern('career.addContractDays')
 */
export class AddContractDaysPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '增加的天数', example: 30 })
  @Expose()
  @IsNumber({}, { message: '天数必须是数字' })
  @Min(1, { message: '天数不能小于1' })
  @Max(3650, { message: '天数不能大于3650（10年）' })
  days: number;
}

/**
 * 续约球员Payload DTO
 * @MessagePattern('career.renewContract')
 */
export class RenewContractPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID列表', type: [String], example: ['hero_12345', 'hero_67890'] })
  @Expose()
  @IsArray({ message: '球员ID列表必须是数组' })
  @IsString({ each: true, message: '球员ID必须是字符串' })
  @Length(1, 50, { each: true, message: '球员ID长度必须在1-50个字符之间' })
  heroIds: string[];
}

// ==================== 2. 球员状态管理相关 ====================

/**
 * 提升球员状态Payload DTO
 * @MessagePattern('career.promoteStatus')
 */
export class PromoteStatusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '道具ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '道具ID必须是数字' })
  @Min(1, { message: '道具ID不能小于1' })
  @Max(999999, { message: '道具ID不能大于999999' })
  itemId: number;
}

/**
 * 加入退役名单Payload DTO
 * @MessagePattern('career.addToRetirement')
 */
export class AddToRetirementPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '角色ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 处理球员退役Payload DTO
 * @MessagePattern('career.processRetirement')
 */
export class ProcessRetirementPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

// ==================== 3. 生涯统计和查询相关 ====================

/**
 * 获取球员生涯统计Payload DTO
 * @MessagePattern('career.getStats')
 */
export class GetStatsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 获取即将到期的球员列表Payload DTO
 * @MessagePattern('career.getExpiringContracts')
 */
export class GetExpiringContractsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '查询天数范围', example: 7 })
  @Expose()
  @IsNumber({}, { message: '天数必须是数字' })
  @Min(1, { message: '天数不能小于1' })
  @Max(365, { message: '天数不能大于365' })
  days: number;
}
