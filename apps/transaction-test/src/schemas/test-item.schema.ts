import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TestItemDocument = TestItem & Document;

/**
 * 测试道具Schema
 * 用于验证事务中的道具操作
 */
@Schema({ timestamps: true })
export class TestItem {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  itemId: string;

  @Prop({ required: true })
  itemName: string;

  @Prop({ required: true, default: 1 })
  quantity: number;

  @Prop({ required: true })
  purchasePrice: number;

  @Prop({ default: Date.now })
  purchaseTime: Date;

  @Prop({ default: 0 })
  version: number;
}

export const TestItemSchema = SchemaFactory.createForClass(TestItem);

// 创建复合索引
TestItemSchema.index({ userId: 1, itemId: 1 }, { unique: true });
