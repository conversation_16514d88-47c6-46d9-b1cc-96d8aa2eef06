/**
 * 复杂Payload测试DTO
 * 用于测试复杂微服务验证管道
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEmail, IsBoolean, Min, Max, Length, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { Type, Expose } from 'class-transformer';

// 模拟UpdateCharacterDto
export class UpdateCharacterDto {
  @ApiProperty({ description: '角色名称', example: 'TestHero', required: false })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  name?: string;

  @ApiProperty({ description: '角色等级', example: 25, required: false })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '角色等级必须是数字' })
  @Min(1, { message: '角色等级不能小于1' })
  @Max(100, { message: '角色等级不能大于100' })
  level?: number;

  @ApiProperty({ description: '角色经验值', example: 1500, required: false })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '经验值必须是数字' })
  @Min(0, { message: '经验值不能为负数' })
  experience?: number;

  @ApiProperty({ description: '角色描述', example: '这是一个测试角色', required: false })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色描述必须是字符串' })
  @Length(0, 200, { message: '角色描述不能超过200个字符' })
  description?: string;
}

// 模拟InjectedContext
export interface InjectedContext {
  userId?: string;
  requestId?: string;
  timestamp?: number;
  clientInfo?: {
    ip?: string;
    userAgent?: string;
  };
}

// 复杂Payload类型定义
export interface ComplexPayloadType {
  characterId: string;
  updateDto: UpdateCharacterDto;
  serverId?: string;
  injectedContext?: InjectedContext;
}

// 其他测试用的复杂Payload类型

// 创建角色Payload
export class CreateCharacterDto {
  @ApiProperty({ description: '角色名称', example: 'NewHero' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  name: string;

  @ApiProperty({ description: '角色职业', example: 'warrior' })
  @Expose()
  @IsString({ message: '角色职业必须是字符串' })
  @Length(2, 20, { message: '角色职业长度必须在2-20个字符之间' })
  class: string;

  @ApiProperty({ description: '初始属性点', example: 10 })
  @Expose()
  @IsNumber({}, { message: '属性点必须是数字' })
  @Min(1, { message: '属性点不能小于1' })
  @Max(50, { message: '属性点不能大于50' })
  attributePoints: number;
}

export interface CreateCharacterPayloadType {
  userId: string;
  createDto: CreateCharacterDto;
  serverId?: string;
  injectedContext?: InjectedContext;
}

// 装备升级Payload
export class UpgradeEquipmentDto {
  @ApiProperty({ description: '目标等级', example: 5 })
  @Expose()
  @IsNumber({}, { message: '目标等级必须是数字' })
  @Min(1, { message: '目标等级不能小于1' })
  @Max(20, { message: '目标等级不能大于20' })
  targetLevel: number;

  @ApiProperty({ description: '是否使用保护石', example: true, required: false })
  @Expose()
  @IsOptional()
  @IsBoolean({ message: '保护石选项必须是布尔值' })
  useProtectionStone?: boolean;

  @ApiProperty({ description: '升级材料列表', example: ['iron_ore', 'magic_crystal'], required: false })
  @Expose()
  @IsOptional()
  @IsArray({ message: '升级材料必须是数组' })
  @IsString({ each: true, message: '每个材料ID都必须是字符串' })
  materials?: string[];
}

export interface UpgradeEquipmentPayloadType {
  characterId: string;
  equipmentId: string;
  upgradeDto: UpgradeEquipmentDto;
  serverId?: string;
  injectedContext?: InjectedContext;
}

// 批量操作Payload
export class BatchOperationDto {
  @ApiProperty({ description: '操作类型', example: 'delete' })
  @Expose()
  @IsString({ message: '操作类型必须是字符串' })
  @IsEnum(['create', 'update', 'delete'], { message: '操作类型必须是create、update或delete' })
  operation: 'create' | 'update' | 'delete';

  @ApiProperty({ description: '目标ID列表', example: ['id1', 'id2', 'id3'] })
  @Expose()
  @IsArray({ message: '目标ID列表必须是数组' })
  @IsString({ each: true, message: '每个ID都必须是字符串' })
  @Length(1, 50, { each: true, message: '每个ID长度必须在1-50个字符之间' })
  targetIds: string[];

  @ApiProperty({ description: '批量操作选项', required: false })
  @Expose()
  @IsOptional()
  options?: {
    skipErrors?: boolean;
    maxConcurrency?: number;
    timeout?: number;
  };
}

export interface BatchOperationPayloadType {
  userId: string;
  batchDto: BatchOperationDto;
  serverId?: string;
  injectedContext?: InjectedContext;
}
