/**
 * Sign模块的Payload DTO定义
 * 
 * 为sign.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 每日签到相关 ====================

/**
 * 每日签到Payload DTO
 * @MessagePattern('sign.daily')
 * 基于真实接口结构：{ characterId: string; openServerTime?: number; injectedContext?: InjectedContext }
 */
export class DailySignPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ 
    description: '开服时间（可选，默认为当前时间）', 
    example: 1640995200000,
    type: 'number'
  })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '开服时间必须是数字' })
  @Min(0, { message: '开服时间不能小于0' })
  openServerTime?: number;
}

// ==================== 2. 签到状态相关 ====================

/**
 * 获取签到状态Payload DTO
 * @MessagePattern('sign.getStatus')
 * 基于真实接口结构：{ characterId: string; openServerTime?: number; injectedContext?: InjectedContext }
 */
export class GetSignStatusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ 
    description: '开服时间（可选，默认为当前时间）', 
    example: 1640995200000,
    type: 'number'
  })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '开服时间必须是数字' })
  @Min(0, { message: '开服时间不能小于0' })
  openServerTime?: number;
}

// ==================== 3. 七日签到奖励相关 ====================

/**
 * 七日签到奖励Payload DTO
 * @MessagePattern('sign.sevenDayReward')
 * 基于真实接口结构：{ characterId: string; day: number; injectedContext?: InjectedContext }
 */
export class SevenDayRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ 
    description: '签到天数', 
    example: 3,
    minimum: 1,
    maximum: 7
  })
  @Expose()
  @IsNumber({}, { message: '签到天数必须是数字' })
  @Min(1, { message: '签到天数不能小于1' })
  @Max(7, { message: '签到天数不能大于7' })
  day: number;
}

// ==================== 4. 补签相关 ====================

/**
 * 补签Payload DTO
 * @MessagePattern('sign.makeUp')
 * 基于真实接口结构：{ characterId: string; targetDay: number; injectedContext?: InjectedContext }
 */
export class MakeUpSignPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ 
    description: '目标补签日期', 
    example: 15,
    minimum: 1,
    maximum: 31
  })
  @Expose()
  @IsNumber({}, { message: '目标补签日期必须是数字' })
  @Min(1, { message: '目标补签日期不能小于1' })
  @Max(31, { message: '目标补签日期不能大于31' })
  targetDay: number;
}
