/**
 * 活动数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 * 基于Repository模式实现数据库访问
 */

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, ClientSession } from 'mongoose';
import { Activity, ActivityDocument, ActivityType, FirstChargeStatus } from '../schemas/event.schema';
import { CreateActivityDto, UpdateActivityDto } from '../dto/event.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 活动数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 活动记录CRUD操作
 * - 活动状态管理
 * - 活动奖励处理
 * - 活动统计分析
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class EventRepository extends BaseRepository<ActivityDocument> {
  constructor(
    @InjectModel(Activity.name) activityModel: Model<ActivityDocument>
  ) {
    super(activityModel, 'EventRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 创建活动记录
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   * Schema已提供默认值，TypeScript类型现在完全兼容
   */
  async create(createData: CreateActivityDto): Promise<XResult<ActivityDocument>> {
    const result = await this.createOne(createData);
    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`活动记录创建成功: ${result.data.uid}`);
    }
    return result;
  }

  /**
   * 根据玩家ID查找活动记录
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByCharacterId(uid: string): Promise<XResult<ActivityDocument | null>> {
    return this.findOne({ uid });
  }

  /**
   * 根据玩家ID查找活动记录（Lean查询优化版本）
   */
  async findByCharacterIdLean(uid: string): Promise<XResult<any | null>> {
    return this.findOneLean({ uid });
  }

  /**
   * 根据活动记录ID查找
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByActivityRecordId(activityRecordId: string): Promise<XResult<ActivityDocument | null>> {
    return this.findOne({ activityRecordId });
  }

  /**
   * 根据活动记录ID查找（Lean查询优化版本）
   */
  async findByActivityRecordIdLean(activityRecordId: string): Promise<XResult<any | null>> {
    return this.findOneLean({ activityRecordId });
  }

  /**
   * 更新活动记录
   * 使用BaseRepository的updateOne方法优化性能
   */
  async update(
    uid: string,
    updateData: UpdateQuery<ActivityDocument>,
    session?: ClientSession
  ): Promise<XResult<ActivityDocument | null>> {
    const result = await this.updateOne(
      { uid },
      { ...updateData, lastUpdateTime: Date.now() },
      session
    );

    if (XResultUtils.isSuccess(result) && result.data) {
      this.logger.log(`活动记录更新成功: ${uid}`);
    }

    return result;
  }

  /**
   * 更新活动进度
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async updateActivityProgress(
    uid: string,
    actId: number,
    progress: number,
    data?: any
  ): Promise<XResult<ActivityDocument | null>> {
    return this.withTransaction(async (session) => {
      const activityResult = await this.findByCharacterId(uid);
      if (XResultUtils.isFailure(activityResult) || !activityResult.data) {
        return XResultUtils.ok(null);
      }

      const activity = activityResult.data;
      const success = activity.updateActivityProgress(actId, progress, data);
      if (success) {
        await activity.save({ session });
        this.logger.log(`活动进度更新成功: ${uid}, 活动: ${actId}, 进度: ${progress}`);
        return XResultUtils.ok(activity);
      }

      return XResultUtils.ok(null);
    });
  }

  /**
   * 领取活动奖励
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async claimActivityReward(
    uid: string,
    actId: number,
    rewardId: number
  ): Promise<XResult<ActivityDocument | null>> {
    return this.withTransaction(async (session) => {
      const activityResult = await this.findByCharacterId(uid);
      if (XResultUtils.isFailure(activityResult) || !activityResult.data) {
        return XResultUtils.ok(null);
      }

      const activity = activityResult.data;
      const success = activity.claimActivityReward(actId, rewardId);
      if (success) {
        await activity.save({ session });
        this.logger.log(`活动奖励领取成功: ${uid}, 活动: ${actId}, 奖励: ${rewardId}`);
        return XResultUtils.ok(activity);
      }

      return XResultUtils.ok(null);
    });
  }

  /**
   * 激活首充
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async activateFirstCharge(uid: string): Promise<XResult<ActivityDocument | null>> {
    return this.withTransaction(async (session) => {
      const activityResult = await this.findByCharacterId(uid);
      if (XResultUtils.isFailure(activityResult) || !activityResult.data) {
        return XResultUtils.ok(null);
      }

      const activity = activityResult.data;
      const success = activity.activateFirstCharge();
      if (success) {
        await activity.save({ session });
        this.logger.log(`首充激活成功: ${uid}`);
        return XResultUtils.ok(activity);
      }

      return XResultUtils.ok(null);
    });
  }

  /**
   * 激活开服礼包
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async activateOpenCharge(uid: string): Promise<XResult<ActivityDocument | null>> {
    return this.withTransaction(async (session) => {
      const activityResult = await this.findByCharacterId(uid);
      if (XResultUtils.isFailure(activityResult) || !activityResult.data) {
        return XResultUtils.ok(null);
      }

      const activity = activityResult.data;
      const success = activity.activateOpenCharge();
      if (success) {
        await activity.save({ session });
        this.logger.log(`开服礼包激活成功: ${uid}`);
        return XResultUtils.ok(activity);
      }

      return XResultUtils.ok(null);
    });
  }

  /**
   * 清理过期活动
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async cleanExpiredActivities(uid: string): Promise<XResult<number>> {
    return this.withTransaction(async (session) => {
      const activityResult = await this.findByCharacterId(uid);
      if (XResultUtils.isFailure(activityResult) || !activityResult.data) {
        return XResultUtils.ok(0);
      }

      const activity = activityResult.data;
      const removedCount = activity.cleanExpiredActivities();
      if (removedCount > 0) {
        await activity.save({ session });
        this.logger.log(`清理过期活动: ${uid}, 清理数量: ${removedCount}`);
      }

      return XResultUtils.ok(removedCount);
    });
  }

  /**
   * 查找需要刷新的活动记录
   * 使用BaseRepository的findMany方法优化性能
   */
  async findNeedingRefresh(): Promise<XResult<ActivityDocument[]>> {
    const oneHourMs = 60 * 60 * 1000;
    const cutoffTime = Date.now() - oneHourMs;

    return this.findMany({
      lastRefreshTime: { $lt: cutoffTime }
    });
  }

  /**
   * 查找需要刷新的活动记录（Lean查询优化版本）
   */
  async findNeedingRefreshLean(): Promise<XResult<any[]>> {
    const oneHourMs = 60 * 60 * 1000;
    const cutoffTime = Date.now() - oneHourMs;

    return this.findManyLean({
      lastRefreshTime: { $lt: cutoffTime }
    }, {
      select: ['uid', 'serverId', 'activityRecordId', 'lastRefreshTime']
    });
  }

  /**
   * 根据区服查找活动记录
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByServerId(serverId: string): Promise<XResult<ActivityDocument[]>> {
    return this.findMany({ serverId });
  }

  /**
   * 根据区服查找活动记录（Lean查询优化版本）
   */
  async findByServerIdLean(serverId: string): Promise<XResult<any[]>> {
    return this.findManyLean({ serverId }, {
      select: ['uid', 'serverId', 'activityRecordId', 'firstChargeStatus', 'openChargeStatus']
    });
  }

  /**
   * 统计活动数据
   * 使用BaseRepository的count和aggregate方法优化性能
   */
  async getActivityStatistics(serverId?: string): Promise<XResult<any>> {
    const filter: FilterQuery<ActivityDocument> = {};
    if (serverId) {
      filter.serverId = serverId;
    }

    // 并行执行统计查询
    const [
      totalResult,
      firstChargeResult,
      openChargeResult,
      avgActivitiesResult
    ] = await Promise.all([
      this.count(filter),
      this.count({
        ...filter,
        firstChargeStatus: FirstChargeStatus.ACTIVE
      }),
      this.count({
        ...filter,
        openChargeStatus: FirstChargeStatus.ACTIVE
      }),
      this.aggregate([
        { $match: filter },
        { $project: { activityCount: { $size: '$globalActMgrInfo' } } },
        { $group: { _id: null, avgActivities: { $avg: '$activityCount' } } }
      ])
    ]);

    // 检查查询结果
    if (XResultUtils.isFailure(totalResult) ||
        XResultUtils.isFailure(firstChargeResult) ||
        XResultUtils.isFailure(openChargeResult) ||
        XResultUtils.isFailure(avgActivitiesResult)) {
      return XResultUtils.error('获取活动统计失败', 'STATISTICS_QUERY_FAILED');
    }

    const totalCharacters = totalResult.data;
    const firstChargeActivations = firstChargeResult.data;
    const openChargeActivations = openChargeResult.data;
    const averageParticipatedActivities = avgActivitiesResult.data[0]?.avgActivities || 0;

    return XResultUtils.ok({
      totalCharacters,
      firstChargeActivations,
      openChargeActivations,
      averageParticipatedActivities: Math.round(averageParticipatedActivities * 100) / 100,
      firstChargeRate: totalCharacters > 0 ? (firstChargeActivations / totalCharacters) * 100 : 0,
      openChargeRate: totalCharacters > 0 ? (openChargeActivations / totalCharacters) * 100 : 0
    });
  }

  /**
   * 查找活动参与排行榜
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getActivityParticipationLeaderboard(limit: number = 50): Promise<XResult<any[]>> {
    const pipeline = [
      {
        $project: {
          uid: 1,
          activityCount: { $size: '$globalActMgrInfo' },
          rewardCount: {
            $sum: {
              $map: {
                input: '$globalActMgrInfo',
                as: 'record',
                in: { $size: '$$record.rewards' }
              }
            }
          }
        }
      },
      { $sort: { activityCount: -1, rewardCount: -1 } },
      { $limit: limit }
    ];

    const result = await this.aggregate(pipeline);
    if (XResultUtils.isSuccess(result)) {
      return XResultUtils.ok(result.data);
    }

    return XResultUtils.ok([]);
  }

  /**
   * 批量更新活动状态
   * 使用BaseRepository的updateMany方法优化性能
   */
  async batchUpdateActivityStatus(
    filter: FilterQuery<ActivityDocument>,
    updateData: UpdateQuery<ActivityDocument>
  ): Promise<XResult<number>> {
    const result = await this.updateMany(
      filter,
      { ...updateData, lastUpdateTime: Date.now() }
    );

    if (XResultUtils.isSuccess(result)) {
      const modifiedCount = result.data.modifiedCount || 0;
      this.logger.log(`批量更新活动状态: ${modifiedCount} 条记录`);
      return XResultUtils.ok(modifiedCount);
    }

    return XResultUtils.ok(0);
  }

  /**
   * 删除活动记录
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async delete(uid: string): Promise<XResult<boolean>> {
    const result = await this.deleteOne({ uid });
    if (XResultUtils.isSuccess(result) && result.data) {
      this.logger.log(`活动记录删除成功: ${uid}`);
      return XResultUtils.ok(true);
    }
    return XResultUtils.ok(false);
  }

  /**
   * 查找特定活动类型的参与者
   * 使用BaseRepository的findMany方法优化性能
   */
  async findParticipantsByActivityType(activityType: ActivityType): Promise<XResult<ActivityDocument[]>> {
    return this.findMany({
      'globalActMgrInfo.actType': activityType
    });
  }

  /**
   * 查找特定活动类型的参与者（Lean查询优化版本）
   */
  async findParticipantsByActivityTypeLean(activityType: ActivityType): Promise<XResult<any[]>> {
    return this.findManyLean({
      'globalActMgrInfo.actType': activityType
    }, {
      select: ['uid', 'serverId', 'globalActMgrInfo']
    });
  }

  /**
   * 查找已激活首充的玩家
   * 使用BaseRepository的findMany方法优化性能
   */
  async findFirstChargeActivatedCharacters(): Promise<XResult<ActivityDocument[]>> {
    return this.findMany({
      firstChargeStatus: FirstChargeStatus.ACTIVE
    });
  }

  /**
   * 查找已激活首充的玩家（Lean查询优化版本）
   */
  async findFirstChargeActivatedCharactersLean(): Promise<XResult<any[]>> {
    return this.findManyLean({
      firstChargeStatus: FirstChargeStatus.ACTIVE
    }, {
      select: ['uid', 'serverId', 'firstChargeStatus', 'firstChargeTime']
    });
  }

  /**
   * 批量清理过期活动
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async batchCleanExpiredActivities(): Promise<XResult<number>> {
    return this.withTransaction(async (session) => {
      let totalCleaned = 0;
      const activitiesResult = await this.findMany({});

      if (XResultUtils.isFailure(activitiesResult)) {
        return XResultUtils.ok(0);
      }

      const activities = activitiesResult.data;
      for (const activity of activities) {
        const cleaned = activity.cleanExpiredActivities();
        if (cleaned > 0) {
          await activity.save({ session });
          totalCleaned += cleaned;
        }
      }

      this.logger.log(`批量清理过期活动完成: 总计清理 ${totalCleaned} 个活动`);
      return XResultUtils.ok(totalCleaned);
    });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加活动特定的验证规则
   */
  protected validateData(data: Partial<Activity>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.uid) {
        return XResultUtils.error('玩家UID不能为空', 'UID_REQUIRED');
      }

      if (!data.activityRecordId) {
        return XResultUtils.error('活动记录ID不能为空', 'ACTIVITY_RECORD_ID_REQUIRED');
      }

      if (!data.serverId) {
        return XResultUtils.error('服务器ID不能为空', 'SERVER_ID_REQUIRED');
      }
    }

    if (data.firstChargeStatus !== undefined &&
        !Object.values(FirstChargeStatus).includes(data.firstChargeStatus)) {
      return XResultUtils.error('首充状态值无效', 'INVALID_FIRST_CHARGE_STATUS');
    }

    if (data.openChargeStatus !== undefined &&
        !Object.values(FirstChargeStatus).includes(data.openChargeStatus)) {
      return XResultUtils.error('开服礼包状态值无效', 'INVALID_OPEN_CHARGE_STATUS');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对活动数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByCharacterId': 300,          // 活动记录缓存5分钟
      'findByCharacterIdLean': 180,      // 活动简介缓存3分钟
      'getActivityStatistics': 600,      // 统计信息缓存10分钟
      'getActivityParticipationLeaderboard': 300, // 排行榜缓存5分钟
      'findByServerId': 300,             // 区服活动缓存5分钟
      'findNeedingRefresh': 60,          // 刷新查询缓存1分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
