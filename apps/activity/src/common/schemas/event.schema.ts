/**
 * 活动系统Schema
 * 基于old项目act.js实体迁移
 */

import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 活动时间类型枚举（基于old项目）
export enum ActivityTimeType {
  PERSISTENT = 1,       // 持久性活动
  CYCLE = 2,           // 周期性活动
}

// 活动刷新类型枚举
export enum ActivityRefreshType {
  NONE = 0,            // 无刷新
  CROSS_DAY = 1,       // 跨天刷新
  CROSS_WEEK = 2,      // 跨周刷新
  CROSS_MONTH = 3,     // 跨月刷新
}

// 活动类型枚举（基于old项目ACT_COMMON_TYPE）
export enum ActivityType {
  FIRST_CHARGE = 1,    // 首充活动
  OPEN_GIFT_BAG = 2,   // 开服礼包
  GOLD_COACH = 3,      // 金牌教练
  DAILY_TASK = 4,      // 每日任务
  WEEKLY_TASK = 5,     // 每周任务
  ACHIEVEMENT = 6,     // 成就活动
  LIMITED_TIME = 7,    // 限时活动
}

// 首充状态枚举
export enum FirstChargeStatus {
  NONE = 0,            // 未充值
  ACTIVE = 1,          // 已激活
}

// 活动记录子文档（基于old项目actRecord结构）
@Schema({ _id: false })
export class ActivityRecord {
  @Prop({ required: true })
  actId: number;       // 活动ID

  @Prop({ required: true, enum: ActivityType })
  actType: ActivityType; // 活动类型

  @Prop({ default: 1 })
  periods?: number;     // 期数

  @Prop({ default: 0 })
  progress?: number;    // 进度

  @Prop({ default: 0 })
  status?: number;      // 状态

  @Prop({ type: Array, default: [] })
  rewards?: number[];   // 已领取奖励ID列表

  @Prop({ type: Object, default: {} })
  data?: any;           // 活动特定数据

  @Prop({ default: Date.now })
  createTime?: number;  // 创建时间

  @Prop({ default: Date.now })
  updateTime?: number;  // 更新时间
}

// 活动信息子文档（基于old项目globalCurrActList结构）
@Schema({ _id: false })
export class ActivityInfo {
  @Prop({ required: true })
  actId: number;       // 活动ID

  @Prop({ required: true, enum: ActivityType })
  actType: ActivityType; // 活动类型

  @Prop({ required: true })
  actName: string;     // 活动名称

  @Prop({ required: true })
  startTime: number;   // 开始时间

  @Prop({ required: true })
  endTime: number;     // 结束时间

  @Prop({ required: true, enum: ActivityTimeType })
  timeType: ActivityTimeType; // 时间类型

  @Prop({ default: ActivityRefreshType.NONE, enum: ActivityRefreshType })
  refreshCycle?: ActivityRefreshType; // 刷新周期，有默认值

  @Prop({ default: 1 })
  periods?: number;     // 期数，有默认值

  @Prop({ default: true })
  isActive?: boolean;   // 是否激活，有默认值

  @Prop({ type: Object, default: {} })
  config?: any;         // 配置数据
}

// 历史活动数据子文档
@Schema({ _id: false })
export class HistoryActivityData {
  @Prop({ required: true })
  actId: number;       // 活动ID

  @Prop({ required: true })
  periods: number;     // 期数

  @Prop({ type: Object, default: {} })
  data: any;           // 历史数据

  @Prop({ default: Date.now })
  archiveTime: number; // 归档时间
}

// 主活动Schema（基于old项目Act结构）
@Schema({ 
  collection: 'activities', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Activity {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  activityRecordId: string; // 活动记录ID

  @Prop({ required: true, index: true })
  uid: string;         // 玩家UID

  @Prop({ required: true, index: true })
  serverId: string;    // 区服ID

  // 当前活动列表（基于old项目globalCurrActList Map结构）
  @Prop({ type: [ActivityInfo], default: [] })
  globalCurrActList: ActivityInfo[]; // 当前活动列表

  // 活动管理信息（基于old项目globalActMgrInfo Map结构）
  @Prop({ type: [ActivityRecord], default: [] })
  globalActMgrInfo: ActivityRecord[]; // 活动管理信息

  // 历史活动数据（基于old项目historyActList Map结构）
  @Prop({ type: [HistoryActivityData], default: [] })
  historyActList: HistoryActivityData[]; // 历史活动数据

  // 特殊状态
  @Prop({ default: FirstChargeStatus.NONE, enum: FirstChargeStatus })
  firstChargeStatus: FirstChargeStatus; // 首充状态

  @Prop({ default: FirstChargeStatus.NONE, enum: FirstChargeStatus })
  openChargeStatus: FirstChargeStatus; // 开服礼包状态

  @Prop({ default: Date.now })
  lastUpdateTime: number;   // 最后更新时间

  @Prop({ default: Date.now })
  lastRefreshTime: number;  // 最后刷新时间

  // 虚拟字段：活跃活动数量
  get activeActivityCount(): number {
    const now = Date.now();
    return this.globalCurrActList.filter(act => 
      act.isActive && now >= act.startTime && now <= act.endTime
    ).length;
  }

  // 虚拟字段：待领取奖励数量
  get pendingRewards(): number {
    return this.globalActMgrInfo.reduce((count, record) => {
      // TODO: 根据具体业务逻辑计算待领取奖励
      return count;
    }, 0);
  }

  // 虚拟字段：总参与活动数
  get totalParticipatedActivities(): number {
    return this.globalActMgrInfo.length;
  }

  // 虚拟字段：是否需要刷新活动控制
  get needsActivityRefresh(): boolean {
    const now = Date.now();
    const oneHourMs = 60 * 60 * 1000;
    return (now - this.lastRefreshTime) >= oneHourMs;
  }
}

export const ActivitySchema = SchemaFactory.createForClass(Activity);

// 定义方法接口
export interface ActivityMethods {
  getActivityInfo(actId: number): ActivityInfo | null;
  getActivityRecord(actId: number): ActivityRecord | null;
  updateActivityProgress(actId: number, progress: number, data?: any): boolean;
  claimActivityReward(actId: number, rewardId: number): boolean;
  addActivityInfo(actInfo: ActivityInfo): void;
  removeActivityInfo(actId: number): boolean;
  addActivityRecord(actRecord: ActivityRecord): void;
  updateActivityRecord(actId: number, updates: Partial<ActivityRecord>): boolean;
  archiveActivity(actId: number): void;
  activateFirstCharge(): boolean;
  activateOpenCharge(): boolean;
  cleanExpiredActivities(): number;
}

// 定义Document类型
export type ActivityDocument = Activity & Document & ActivityMethods;

// 创建索引
ActivitySchema.index({ activityRecordId: 1 }, { unique: true });
ActivitySchema.index({ uid: 1 });
ActivitySchema.index({ serverId: 1 });
ActivitySchema.index({ 'globalCurrActList.actId': 1 });
ActivitySchema.index({ 'globalActMgrInfo.actId': 1 });
ActivitySchema.index({ firstChargeStatus: 1 });
ActivitySchema.index({ openChargeStatus: 1 });
ActivitySchema.index({ lastUpdateTime: 1 });

// 添加虚拟字段
ActivitySchema.virtual('activeActivityCount').get(function() {
  const now = Date.now();
  return this.globalCurrActList.filter(act => 
    act.isActive && now >= act.startTime && now <= act.endTime
  ).length;
});

ActivitySchema.virtual('pendingRewards').get(function() {
  return this.globalActMgrInfo.reduce((count, record) => {
    // TODO: 根据具体业务逻辑计算待领取奖励
    return count;
  }, 0);
});

ActivitySchema.virtual('totalParticipatedActivities').get(function() {
  return this.globalActMgrInfo.length;
});

ActivitySchema.virtual('needsActivityRefresh').get(function() {
  const now = Date.now();
  const oneHourMs = 60 * 60 * 1000;
  return (now - this.lastRefreshTime) >= oneHourMs;
});

// 添加实例方法
ActivitySchema.methods.getActivityInfo = function(actId: number) {
  return this.globalCurrActList.find(act => act.actId === actId) || null;
};

ActivitySchema.methods.getActivityRecord = function(actId: number) {
  return this.globalActMgrInfo.find(record => record.actId === actId) || null;
};

ActivitySchema.methods.updateActivityProgress = function(actId: number, progress: number, data?: any) {
  let record = this.getActivityRecord(actId);
  if (!record) {
    // 创建新记录
    const actInfo = this.getActivityInfo(actId);
    if (!actInfo) return false;
    
    record = {
      actId,
      actType: actInfo.actType,
      periods: actInfo.periods,
      progress: 0,
      status: 0,
      rewards: [],
      data: {},
      createTime: Date.now(),
      updateTime: Date.now(),
    };
    this.globalActMgrInfo.push(record);
  }
  
  record.progress = Math.max(record.progress, progress);
  record.updateTime = Date.now();
  
  if (data) {
    record.data = { ...record.data, ...data };
  }
  
  this.lastUpdateTime = Date.now();
  return true;
};

ActivitySchema.methods.claimActivityReward = function(actId: number, rewardId: number) {
  const record = this.getActivityRecord(actId);
  if (!record) return false;
  
  if (record.rewards.includes(rewardId)) return false;
  
  record.rewards.push(rewardId);
  record.updateTime = Date.now();
  this.lastUpdateTime = Date.now();
  
  return true;
};

ActivitySchema.methods.addActivityInfo = function(actInfo: ActivityInfo) {
  const existingIndex = this.globalCurrActList.findIndex(act => act.actId === actInfo.actId);
  if (existingIndex !== -1) {
    this.globalCurrActList[existingIndex] = actInfo;
  } else {
    this.globalCurrActList.push(actInfo);
  }
  this.lastUpdateTime = Date.now();
};

ActivitySchema.methods.removeActivityInfo = function(actId: number) {
  const index = this.globalCurrActList.findIndex(act => act.actId === actId);
  if (index === -1) return false;
  
  this.globalCurrActList.splice(index, 1);
  this.lastUpdateTime = Date.now();
  return true;
};

ActivitySchema.methods.activateFirstCharge = function() {
  if (this.firstChargeStatus === FirstChargeStatus.ACTIVE) return false;
  
  this.firstChargeStatus = FirstChargeStatus.ACTIVE;
  this.lastUpdateTime = Date.now();
  return true;
};

ActivitySchema.methods.activateOpenCharge = function() {
  if (this.openChargeStatus === FirstChargeStatus.ACTIVE) return false;
  
  this.openChargeStatus = FirstChargeStatus.ACTIVE;
  this.lastUpdateTime = Date.now();
  return true;
};

ActivitySchema.methods.cleanExpiredActivities = function() {
  const now = Date.now();
  const initialCount = this.globalCurrActList.length;
  
  this.globalCurrActList = this.globalCurrActList.filter(act => {
    if (act.timeType === ActivityTimeType.PERSISTENT) return true;
    return now <= act.endTime;
  });
  
  const removedCount = initialCount - this.globalCurrActList.length;
  if (removedCount > 0) {
    this.lastUpdateTime = Date.now();
  }
  
  return removedCount;
};

// 添加中间件
ActivitySchema.pre('save', function(next) {
  this.lastUpdateTime = Date.now();
  next();
});

// 添加静态方法
ActivitySchema.statics.findByCharacter = function(uid: string) {
  return this.findOne({ uid });
};

ActivitySchema.statics.findNeedingRefresh = function() {
  const oneHourMs = 60 * 60 * 1000;
  const cutoffTime = Date.now() - oneHourMs;
  
  return this.find({
    lastRefreshTime: { $lt: cutoffTime }
  });
};
