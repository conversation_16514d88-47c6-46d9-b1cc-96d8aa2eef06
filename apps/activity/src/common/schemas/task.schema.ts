/**
 * 任务系统Schema
 * 基于old项目tasks.js和newerTask.js实体迁移
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 任务状态枚举
export enum TaskStatus {
  NOT_STARTED = 0,    // 未开始
  IN_PROGRESS = 1,    // 进行中
  COMPLETED = 2,      // 已完成
  CLAIMED = 3,        // 已领取奖励
  EXPIRED = 4,        // 已过期
}

// 任务类型枚举
export enum TaskType {
  DAILY = 1,          // 每日任务
  WEEKLY = 2,         // 每周任务
  ACHIEVEMENT = 3,    // 成就任务
  NEWBIE = 4,         // 新手任务
  EVENT = 5,          // 活动任务
  MAIN = 6,           // 主线任务
}

// 任务奖励子文档
@Schema({ _id: false })
export class TaskReward {
  @Prop({ required: true })
  type: string;         // 奖励类型（gold、cash、item等）

  @Prop({ required: true })
  itemId: number;       // 物品ID

  @Prop({ required: true })
  quantity: number;     // 数量

  @Prop({ default: '' })
  description: string;  // 奖励描述
}

// 任务进度子文档
@Schema({ _id: false })
export class TaskProgress {
  @Prop({ required: true })
  taskId: number;       // 任务ID

  @Prop({ default: 0 })
  currentValue: number; // 当前进度值

  @Prop({ default: 0 })
  targetValue: number;  // 目标值

  @Prop({ default: TaskStatus.NOT_STARTED, enum: TaskStatus })
  status: TaskStatus;   // 任务状态

  @Prop({ default: Date.now })
  startTime: number;    // 开始时间

  @Prop({ default: 0 })
  completeTime: number; // 完成时间

  @Prop({ default: 0 })
  claimTime: number;    // 领取时间

  @Prop({ type: [TaskReward], default: [] })
  rewards: TaskReward[]; // 任务奖励

  @Prop({ default: '' })
  description: string;  // 任务描述

  @Prop({ default: 0 })
  expireTime: number;   // 过期时间

  // 活动任务相关属性
  @Prop({ default: '' })
  activityId: string;   // 活动ID

  @Prop({ default: '' })
  taskCategory: string; // 任务分类

  @Prop({ default: '' })
  taskCondition: string; // 任务条件

  @Prop({ default: 0 })
  requiredQuality: number; // 需要的品质

  // 兼容性属性（与old项目保持一致）
  @Prop({ default: 0 })
  currentProgress: number; // 当前进度（兼容性）

  @Prop({ default: 0 })
  targetProgress: number;  // 目标进度（兼容性）

  // 虚拟字段：是否可以领取奖励
  get canClaim(): boolean {
    return this.status === TaskStatus.COMPLETED;
  }

  // 虚拟字段：进度百分比
  get progressPercent(): number {
    if (this.targetValue === 0) return 0;
    return Math.min(100, Math.floor((this.currentValue / this.targetValue) * 100));
  }

  // 虚拟字段：是否已过期
  get isExpired(): boolean {
    return this.expireTime > 0 && Date.now() > this.expireTime;
  }
}

// 主任务系统Schema
@Schema({ 
  collection: 'tasks', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Task {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  taskRecordId: string; // 任务记录ID

  @Prop({ required: true, index: true })
  characterId: string;     // 玩家ID

  @Prop({ required: true, index: true })
  serverId: string;     // 区服ID

  // 每日任务（基于old项目tasks Map结构）
  @Prop({ type: [TaskProgress], default: [] })
  dailyTasks: TaskProgress[];   // 每日任务列表

  // 每周任务
  @Prop({ type: [TaskProgress], default: [] })
  weeklyTasks: TaskProgress[];  // 每周任务列表

  // 成就任务
  @Prop({ type: [TaskProgress], default: [] })
  achievementTasks: TaskProgress[]; // 成就任务列表

  // 新手任务（基于old项目newerTask Map结构）
  @Prop({ type: [TaskProgress], default: [] })
  newbieTasks: TaskProgress[];  // 新手任务列表

  // 活动任务
  @Prop({ type: [TaskProgress], default: [] })
  eventTasks: TaskProgress[];   // 活动任务列表

  // 主线任务
  @Prop({ type: [TaskProgress], default: [] })
  mainTasks: TaskProgress[];    // 主线任务列表

  // 任务刷新时间（基于old项目）
  @Prop({ default: Date.now })
  dailyRefreshTime: number;     // 每日任务刷新时间

  @Prop({ default: Date.now })
  weeklyRefreshTime: number;    // 每周任务刷新时间

  // 统计信息
  @Prop({ default: 0 })
  totalCompletedTasks: number;  // 总完成任务数

  @Prop({ default: 0 })
  totalClaimedRewards: number;  // 总领取奖励数

  @Prop({ default: 0 })
  dailyCompletedCount: number;  // 今日完成任务数

  @Prop({ default: 0 })
  weeklyCompletedCount: number; // 本周完成任务数

  @Prop({ default: Date.now })
  lastUpdateTime: number;       // 最后更新时间

  // 虚拟字段：是否需要每日刷新
  get needsDailyRefresh(): boolean {
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    return (now - this.dailyRefreshTime) >= oneDayMs;
  }

  // 虚拟字段：是否需要每周刷新
  get needsWeeklyRefresh(): boolean {
    const now = Date.now();
    const oneWeekMs = 7 * 24 * 60 * 60 * 1000;
    return (now - this.weeklyRefreshTime) >= oneWeekMs;
  }

  // 虚拟字段：待领取奖励数量
  get pendingRewards(): number {
    const allTasks = [
      ...this.dailyTasks,
      ...this.weeklyTasks,
      ...this.achievementTasks,
      ...this.newbieTasks,
      ...this.eventTasks,
      ...this.mainTasks,
    ];
    return allTasks.filter(task => task.status === TaskStatus.COMPLETED).length;
  }

  // 虚拟字段：今日任务完成度
  get dailyProgress(): { completed: number; total: number; percent: number } {
    const completed = this.dailyTasks.filter(task => 
      task.status === TaskStatus.COMPLETED || task.status === TaskStatus.CLAIMED
    ).length;
    const total = this.dailyTasks.length;
    const percent = total > 0 ? Math.floor((completed / total) * 100) : 0;
    return { completed, total, percent };
  }
}

export const TaskSchema = SchemaFactory.createForClass(Task);

// 定义方法接口
export interface TaskMethods {
  updateTaskProgress(taskId: number, value: number): boolean;
  completeTask(taskId: number): boolean;
  claimTaskReward(taskId: number): TaskReward[] | null;
  refreshDailyTasks(): void;
  refreshWeeklyTasks(): void;
  addNewTask(taskType: TaskType, taskData: Partial<TaskProgress>): boolean;
  getTasksByType(taskType: TaskType): TaskProgress[];
  getTaskById(taskId: number): TaskProgress | null;
}

// 定义Document类型
export type TaskDocument = Task & Document & TaskMethods;

// 创建索引
TaskSchema.index({ taskRecordId: 1 }, { unique: true });
TaskSchema.index({ characterId: 1 });
TaskSchema.index({ serverId: 1 });
TaskSchema.index({ 'dailyTasks.taskId': 1 });
TaskSchema.index({ 'weeklyTasks.taskId': 1 });
TaskSchema.index({ 'achievementTasks.taskId': 1 });

// 添加虚拟字段
TaskSchema.virtual('needsDailyRefresh').get(function() {
  const now = Date.now();
  const oneDayMs = 24 * 60 * 60 * 1000;
  return (now - this.dailyRefreshTime) >= oneDayMs;
});

TaskSchema.virtual('needsWeeklyRefresh').get(function() {
  const now = Date.now();
  const oneWeekMs = 7 * 24 * 60 * 60 * 1000;
  return (now - this.weeklyRefreshTime) >= oneWeekMs;
});

TaskSchema.virtual('pendingRewards').get(function() {
  const allTasks = [
    ...this.dailyTasks,
    ...this.weeklyTasks,
    ...this.achievementTasks,
    ...this.newbieTasks,
    ...this.eventTasks,
    ...this.mainTasks,
  ];
  return allTasks.filter(task => task.status === TaskStatus.COMPLETED).length;
});

TaskSchema.virtual('dailyProgress').get(function() {
  const completed = this.dailyTasks.filter(task => 
    task.status === TaskStatus.COMPLETED || task.status === TaskStatus.CLAIMED
  ).length;
  const total = this.dailyTasks.length;
  const percent = total > 0 ? Math.floor((completed / total) * 100) : 0;
  return { completed, total, percent };
});

// 添加实例方法
TaskSchema.methods.updateTaskProgress = function(taskId: number, value: number) {
  const allTasks = [
    ...this.dailyTasks,
    ...this.weeklyTasks,
    ...this.achievementTasks,
    ...this.newbieTasks,
    ...this.eventTasks,
    ...this.mainTasks,
  ];
  
  const task = allTasks.find(t => t.taskId === taskId);
  if (!task || task.status === TaskStatus.CLAIMED) return false;
  
  task.currentValue = Math.max(task.currentValue, value);
  
  // 检查是否完成
  if (task.currentValue >= task.targetValue && task.status === TaskStatus.IN_PROGRESS) {
    task.status = TaskStatus.COMPLETED;
    task.completeTime = Date.now();
  }
  
  this.lastUpdateTime = Date.now();
  return true;
};

TaskSchema.methods.completeTask = function(taskId: number) {
  const task = this.getTaskById(taskId);
  if (!task || task.status !== TaskStatus.IN_PROGRESS) return false;
  
  task.status = TaskStatus.COMPLETED;
  task.completeTime = Date.now();
  task.currentValue = task.targetValue;
  
  this.totalCompletedTasks += 1;
  this.lastUpdateTime = Date.now();
  
  return true;
};

TaskSchema.methods.claimTaskReward = function(taskId: number) {
  const task = this.getTaskById(taskId);
  if (!task || task.status !== TaskStatus.COMPLETED) return null;
  
  task.status = TaskStatus.CLAIMED;
  task.claimTime = Date.now();
  
  this.totalClaimedRewards += 1;
  this.lastUpdateTime = Date.now();
  
  return task.rewards;
};

TaskSchema.methods.refreshDailyTasks = function() {
  this.dailyTasks = [];
  this.dailyRefreshTime = Date.now();
  this.dailyCompletedCount = 0;
  this.lastUpdateTime = Date.now();
  
  // TODO: 从配置中加载每日任务
};

TaskSchema.methods.refreshWeeklyTasks = function() {
  this.weeklyTasks = [];
  this.weeklyRefreshTime = Date.now();
  this.weeklyCompletedCount = 0;
  this.lastUpdateTime = Date.now();
  
  // TODO: 从配置中加载每周任务
};

TaskSchema.methods.addNewTask = function(taskType: TaskType, taskData: Partial<TaskProgress>) {
  const newTask: TaskProgress = {
    taskId: taskData.taskId || 0,
    currentValue: 0,
    targetValue: taskData.targetValue || 1,
    status: TaskStatus.IN_PROGRESS,
    startTime: Date.now(),
    completeTime: 0,
    claimTime: 0,
    rewards: taskData.rewards || [],
    description: taskData.description || '',
    expireTime: taskData.expireTime || 0,
    activityId: taskData.activityId || '',
    taskCategory: taskData.taskCategory || '',
    taskCondition: taskData.taskCondition || '',
    requiredQuality: taskData.requiredQuality || 0,
    currentProgress: 0,
    targetProgress: taskData.targetValue || 1,
    canClaim: false,
    progressPercent: 0,
    isExpired: false,
  };
  
  switch (taskType) {
    case TaskType.DAILY:
      this.dailyTasks.push(newTask);
      break;
    case TaskType.WEEKLY:
      this.weeklyTasks.push(newTask);
      break;
    case TaskType.ACHIEVEMENT:
      this.achievementTasks.push(newTask);
      break;
    case TaskType.NEWBIE:
      this.newbieTasks.push(newTask);
      break;
    case TaskType.EVENT:
      this.eventTasks.push(newTask);
      break;
    case TaskType.MAIN:
      this.mainTasks.push(newTask);
      break;
    default:
      return false;
  }
  
  this.lastUpdateTime = Date.now();
  return true;
};

TaskSchema.methods.getTasksByType = function(taskType: TaskType) {
  switch (taskType) {
    case TaskType.DAILY:
      return this.dailyTasks;
    case TaskType.WEEKLY:
      return this.weeklyTasks;
    case TaskType.ACHIEVEMENT:
      return this.achievementTasks;
    case TaskType.NEWBIE:
      return this.newbieTasks;
    case TaskType.EVENT:
      return this.eventTasks;
    case TaskType.MAIN:
      return this.mainTasks;
    default:
      return [];
  }
};

TaskSchema.methods.getTaskById = function(taskId: number) {
  const allTasks = [
    ...this.dailyTasks,
    ...this.weeklyTasks,
    ...this.achievementTasks,
    ...this.newbieTasks,
    ...this.eventTasks,
    ...this.mainTasks,
  ];
  
  return allTasks.find(task => task.taskId === taskId) || null;
};
