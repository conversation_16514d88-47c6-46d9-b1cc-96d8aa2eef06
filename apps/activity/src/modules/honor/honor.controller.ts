/**
 * 荣誉墙控制器
 * 基于old项目honorWall.js接口迁移
 */

import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { Cacheable, CacheEvict } from '@libs/redis';
import { HonorService } from './honor.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  AddTaskPayloadDto,
  ClaimLevelRewardPayloadDto,
  ClaimTaskRewardPayloadDto,
  GetHonorInfoPayloadDto,
  GetHonorRankingPayloadDto,
  TriggerHonorTaskPayloadDto,
  UpdateProgressPayloadDto
} from "@activity/common/dto/honor-payload.dto";

@Controller()
export class HonorController {
  private readonly logger = new Logger(HonorController.name);

  constructor(private readonly honorService: HonorService) {}

  /**
   * 获取荣誉墙信息
   */
  @MessagePattern('honor.getInfo')
  @Cacheable({
    key: 'honor:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getHonorInfo(@Payload() payload: GetHonorInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取荣誉墙信息: ${payload.uid}`);
    const honorInfo = await this.honorService.getHonorInfo(payload.uid, payload.serverId);
    return {
      code: 0,
      message: '获取成功',
      data: honorInfo,
    };
  }

  /**
   * 领取荣誉任务奖励
   * 对应old项目中的getHonorTaskReward方法 - 这是缺失的核心功能
   */
  @MessagePattern('honor.claimTaskReward')
  @CacheEvict({
    key: 'honor:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getHonorTaskReward(@Payload() payload: ClaimTaskRewardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取荣誉任务奖励: ${payload.uid}, 任务: ${payload.taskId}`);
    const result = await this.honorService.getHonorTaskReward(
      payload.uid, 
      payload.serverId, 
      payload.taskId
    );
    return {
      code: 0,
      message: '奖励领取成功',
      data: result,
    };
  }

  /**
   * 更新荣誉任务进度
   */
  @MessagePattern('honor.updateProgress')
  @CacheEvict({
    key: 'honor:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateHonorTaskProgress(@Payload() payload: UpdateProgressPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新荣誉任务进度: ${payload.uid}, 任务: ${payload.taskId}, 进度: ${payload.progress}`);
    const result = await this.honorService.updateHonorTaskProgress(
      payload.uid, 
      payload.serverId, 
      payload.taskId,
      payload.progress
    );
    return {
      code: 0,
      message: '进度更新成功',
      data: result,
    };
  }

  /**
   * 添加荣誉任务
   */
  @MessagePattern('honor.addTask')
  @CacheEvict({
    key: 'honor:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addHonorTask(@Payload() payload: AddTaskPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`添加荣誉任务: ${payload.uid}, 任务: ${payload.taskConfig.taskId}`);
    const result = await this.honorService.addHonorTask(
      payload.uid, 
      payload.serverId, 
      payload.taskConfig
    );
    return {
      code: 0,
      message: '任务添加成功',
      data: result,
    };
  }

  /**
   * 获取荣誉等级奖励
   */
  @MessagePattern('honor.claimLevelReward')
  @CacheEvict({
    key: 'honor:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getHonorLevelReward(@Payload() payload: ClaimLevelRewardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取荣誉等级奖励: ${payload.uid}, 等级: ${payload.level}`);
    const result = await this.honorService.getHonorLevelReward(
      payload.uid, 
      payload.serverId, 
      payload.level
    );
    return {
      code: 0,
      message: '等级奖励领取成功',
      data: result,
    };
  }

  /**
   * 触发荣誉任务检查
   */
  @MessagePattern('honor.trigger')
  @CacheEvict({
    key: 'honor:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async triggerHonorTask(@Payload() payload: TriggerHonorTaskPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`触发荣誉任务: ${payload.uid}, 类型: ${payload.triggerType}`);
    const result = await this.honorService.triggerHonorTask(
      payload.uid, 
      payload.serverId, 
      payload.triggerType,
      payload.param
    );
    return {
      code: 0,
      message: '荣誉任务触发成功',
      data: result,
    };
  }

  /**
   * 获取荣誉排行榜
   */
  @MessagePattern('honor.getRanking')
  @Cacheable({
    key: 'honor:ranking',
    dataType: 'global',
    ttl: 600
  })
  async getHonorRanking(@Payload() payload: GetHonorRankingPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取荣誉排行榜: 限制 ${payload.limit || 100}`);
    // TODO: 实现荣誉排行榜逻辑
    return {
      code: 0,
      message: '获取成功',
      data: {
        rankings: [],
        limit: payload.limit || 100,
      },
    };
  }
}
