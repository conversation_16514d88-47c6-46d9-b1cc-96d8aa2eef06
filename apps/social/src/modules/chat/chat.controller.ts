import { <PERSON>, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ChatService } from './chat.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  GetHistoryPayloadDto,
  GetPrivateHistoryPayloadDto,
  JoinChannelPayloadDto,
  LeaveChannelPayloadDto,
  SendMessagePayloadDto
} from "@social/common/dto/chat-payload.dto";

@Controller()
export class ChatController {
  private readonly logger = new Logger(ChatController.name);

  constructor(private readonly chatService: ChatService) {}

  /**
   * 发送聊天消息
   */
  @MessagePattern('chat.sendMessage')
  async sendMessage(@Payload() payload: SendMessagePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`发送聊天消息: ${payload.characterId}`);

    // 设置发送者信息
    const messageData = {
      ...payload,
      senderId: payload.characterId,
      serverId: payload.serverId,
    };

    const result = await this.chatService.sendMessage(messageData);
    return {
      code: 0,
      message: '消息发送成功',
      data: result,
    };
  }

  /**
   * 获取聊天历史
   */
  @MessagePattern('chat.getHistory')
  async getChatHistory(@Payload() payload: GetHistoryPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取聊天历史: ${payload.channelId}`);
    const result = await this.chatService.getChatHistory(payload);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 获取私聊历史
   */
  @MessagePattern('chat.getPrivateHistory')
  async getPrivateHistory(@Payload() payload: GetPrivateHistoryPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取私聊历史: ${payload.senderId} <-> ${payload.receiverId}`);
    const result = await this.chatService.getPrivateChatHistory(payload);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 加入聊天频道
   */
  @MessagePattern('chat.joinChannel')
  async joinChannel(@Payload() payload: JoinChannelPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`加入聊天频道: ${payload.characterId} -> ${payload.channelId}`);
    const result = await this.chatService.joinChannel(
      payload.characterId,
      payload.channelId,
      payload.serverId
    );
    return {
      code: 0,
      message: '加入频道成功',
      data: result,
    };
  }

  /**
   * 离开聊天频道
   */
  @MessagePattern('chat.leaveChannel')
  async leaveChannel(@Payload() payload: LeaveChannelPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`离开聊天频道: ${payload.characterId} -> ${payload.channelId}`);
    const result = await this.chatService.leaveChannel(payload.characterId, payload.channelId);
    return {
      code: 0,
      message: '离开频道成功',
      data: result,
    };
  }
}
