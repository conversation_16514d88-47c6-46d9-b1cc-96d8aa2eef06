/**
 * 聊天模块
 * 处理游戏内聊天功能
 */

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatController } from './chat.controller';
import { ChatService } from './chat.service';
import { ChatRepositoryBak } from '../../common/repositories/chat.repository.bak';
import {
  ChatMessage,
  ChatMessageSchema,
  ChatChannel,
  ChatChannelSchema
} from '../../common/schemas/chat.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ChatMessage.name, schema: ChatMessageSchema },
      { name: ChatChannel.name, schema: ChatChannelSchema },
    ]),
  ],
  controllers: [ChatController],
  providers: [ChatService, ChatRepositoryBak],
  exports: [ChatService, ChatRepositoryBak],
})
export class ChatModule {}
