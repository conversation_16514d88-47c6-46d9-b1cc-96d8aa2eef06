/**
 * 聊天数据访问层
 * 基于Repository模式，提供聊天消息和频道的CRUD操作
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { 
  ChatMessage, 
  ChatMessageDocument, 
  ChatChannel, 
  ChatChannelDocument,
  ChatMessageType 
} from '../schemas/chat.schema';

@Injectable()
export class ChatRepositoryBak {
  private readonly logger = new Logger(ChatRepositoryBak.name);

  constructor(
    @InjectModel(ChatMessage.name) private chatMessageModel: Model<ChatMessageDocument>,
    @InjectModel(ChatChannel.name) private chatChannelModel: Model<ChatChannelDocument>,
  ) {}

  /**
   * 创建聊天消息
   */
  async createMessage(messageData: Partial<ChatMessage>): Promise<ChatMessageDocument> {
    try {
      const message = new this.chatMessageModel(messageData);
      const savedMessage = await message.save();
      this.logger.log(`聊天消息创建成功: ${savedMessage.messageId}`);
      return savedMessage;
    } catch (error) {
      this.logger.error('创建聊天消息失败', error);
      throw error;
    }
  }

  /**
   * 根据频道ID获取聊天历史
   */
  async getMessagesByChannel(
    channelId: string, 
    limit: number = 50, 
    before?: number
  ): Promise<ChatMessageDocument[]> {
    try {
      const query: any = { channelId };
      if (before) {
        query.sendTime = { $lt: before };
      }

      return await this.chatMessageModel
        .find(query)
        .sort({ sendTime: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error(`获取频道聊天历史失败: ${channelId}`, error);
      throw error;
    }
  }

  /**
   * 获取私聊消息历史
   */
  async getPrivateMessages(
    senderId: string, 
    receiverId: string, 
    limit: number = 50,
    before?: number
  ): Promise<ChatMessageDocument[]> {
    try {
      const query: any = {
        $or: [
          { senderId, receiverId },
          { senderId: receiverId, receiverId: senderId }
        ],
        messageType: ChatMessageType.PRIVATE
      };

      if (before) {
        query.sendTime = { $lt: before };
      }

      return await this.chatMessageModel
        .find(query)
        .sort({ sendTime: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error(`获取私聊消息失败: ${senderId} <-> ${receiverId}`, error);
      throw error;
    }
  }

  /**
   * 创建或获取聊天频道
   */
  async getOrCreateChannel(channelData: Partial<ChatChannel>): Promise<ChatChannelDocument> {
    try {
      let channel = await this.chatChannelModel.findOne({ 
        channelId: channelData.channelId 
      }).exec();

      if (!channel) {
        channel = new this.chatChannelModel(channelData);
        await channel.save();
        this.logger.log(`聊天频道创建成功: ${channel.channelId}`);
      }

      return channel;
    } catch (error) {
      this.logger.error('创建或获取聊天频道失败', error);
      throw error;
    }
  }

  /**
   * 加入频道
   */
  async joinChannel(channelId: string, characterId: string): Promise<ChatChannelDocument | null> {
    try {
      const channel = await this.chatChannelModel.findOneAndUpdate(
        { 
          channelId,
          members: { $ne: characterId } // 确保不重复添加
        },
        { 
          $addToSet: { members: characterId },
          $inc: { memberCount: 1 }
        },
        { new: true }
      ).exec();

      if (channel) {
        this.logger.log(`角色 ${characterId} 加入频道 ${channelId}`);
      }

      return channel;
    } catch (error) {
      this.logger.error(`加入频道失败: ${characterId} -> ${channelId}`, error);
      throw error;
    }
  }

  /**
   * 离开频道
   */
  async leaveChannel(channelId: string, characterId: string): Promise<ChatChannelDocument | null> {
    try {
      const channel = await this.chatChannelModel.findOneAndUpdate(
        { 
          channelId,
          members: characterId // 确保用户在频道中
        },
        { 
          $pull: { members: characterId },
          $inc: { memberCount: -1 }
        },
        { new: true }
      ).exec();

      if (channel) {
        this.logger.log(`角色 ${characterId} 离开频道 ${channelId}`);
      }

      return channel;
    } catch (error) {
      this.logger.error(`离开频道失败: ${characterId} -> ${channelId}`, error);
      throw error;
    }
  }

  /**
   * 更新频道最后消息信息
   */
  async updateChannelLastMessage(
    channelId: string, 
    messageContent: string, 
    messageTime: number
  ): Promise<void> {
    try {
      await this.chatChannelModel.updateOne(
        { channelId },
        {
          $set: {
            lastMessageContent: messageContent,
            lastMessageTime: messageTime
          },
          $inc: { messageCount: 1 }
        }
      ).exec();
    } catch (error) {
      this.logger.error(`更新频道最后消息失败: ${channelId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID获取其参与的频道
   */
  async getChannelsByCharacter(characterId: string): Promise<ChatChannelDocument[]> {
    try {
      return await this.chatChannelModel
        .find({ members: characterId })
        .sort({ lastMessageTime: -1 })
        .exec();
    } catch (error) {
      this.logger.error(`获取角色频道失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 根据频道ID查找频道
   */
  async findChannelById(channelId: string): Promise<ChatChannelDocument | null> {
    try {
      return await this.chatChannelModel.findOne({ channelId }).exec();
    } catch (error) {
      this.logger.error(`查找频道失败: ${channelId}`, error);
      throw error;
    }
  }

  /**
   * 标记消息为已读
   */
  async markMessageAsRead(messageId: string, readTime: number): Promise<void> {
    try {
      await this.chatMessageModel.updateOne(
        { messageId },
        { 
          $set: { 
            status: 'read',
            readTime 
          }
        }
      ).exec();
    } catch (error) {
      this.logger.error(`标记消息已读失败: ${messageId}`, error);
      throw error;
    }
  }

  /**
   * 删除过期消息（清理任务）
   */
  async deleteExpiredMessages(beforeTime: number): Promise<number> {
    try {
      const result = await this.chatMessageModel.deleteMany({
        sendTime: { $lt: beforeTime }
      }).exec();

      this.logger.log(`删除过期消息: ${result.deletedCount} 条`);
      return result.deletedCount;
    } catch (error) {
      this.logger.error('删除过期消息失败', error);
      throw error;
    }
  }
}
