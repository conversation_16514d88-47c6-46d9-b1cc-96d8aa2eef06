/**
 * 邮件数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 * 基于Repository模式实现数据库访问
 */

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, ClientSession } from 'mongoose';
import { Mail, MailDocument, MailStatus } from '../schemas/mail.schema';
import { CreateMailDto } from '../dto/mail.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 邮件数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 邮件CRUD操作
 * - 邮件状态管理
 * - 邮件附件处理
 * - 邮件搜索和过滤
 * - 批量邮件操作
 * - 过期邮件清理
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class MailRepository extends BaseRepository<MailDocument> {
  constructor(
    @InjectModel(Mail.name) mailModel: Model<MailDocument>
  ) {
    super(mailModel, 'MailRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 创建邮件
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(createData: CreateMailDto & { uid: string }): Promise<XResult<MailDocument>> {
    const result = await this.createOne(createData);
    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`邮件创建成功: ${result.data.uid}`);
    }
    return result;
  }

  /**
   * 根据UID查找邮件
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByUid(uid: string): Promise<XResult<MailDocument | null>> {
    return this.findOne({ uid });
  }

  /**
   * 根据UID查找邮件（Lean查询优化版本）
   */
  async findByUidLean(uid: string): Promise<XResult<any | null>> {
    return this.findOneLean({ uid });
  }

  /**
   * 根据接收者ID查找邮件列表
   * 使用BaseRepository的findWithPagination方法优化性能
   */
  async findByReceiver(
    receiverUid: string,
    page: number = 1,
    limit: number = 20
  ): Promise<XResult<{ mails: MailDocument[]; total: number }>> {
    const result = await this.findWithPagination({
      page,
      limit,
      filter: { receiverUid },
      sort: { sendTime: -1 }
    });

    if (XResultUtils.isSuccess(result)) {
      return XResultUtils.ok({
        mails: result.data.data,
        total: result.data.total
      });
    }

    return XResultUtils.ok({ mails: [], total: 0 });
  }

  /**
   * 查找未读邮件
   * 使用BaseRepository的findMany方法优化性能
   */
  async findUnreadByReceiver(receiverUid: string): Promise<XResult<MailDocument[]>> {
    return this.findMany({
      receiverUid,
      status: MailStatus.UNREAD
    }, {
      sort: { sendTime: -1 }
    });
  }

  /**
   * 查找未读邮件（Lean查询优化版本）
   */
  async findUnreadByReceiverLean(receiverUid: string): Promise<XResult<any[]>> {
    return this.findManyLean({
      receiverUid,
      status: MailStatus.UNREAD
    }, {
      sort: { sendTime: -1 },
      select: ['uid', 'title', 'sendTime', 'senderName', 'hasAttachment']
    });
  }

  /**
   * 统计未读邮件数量
   * 使用BaseRepository的count方法优化性能
   */
  async countUnreadByReceiver(receiverUid: string): Promise<XResult<number>> {
    return this.count({
      receiverUid,
      status: MailStatus.UNREAD
    });
  }

  /**
   * 更新邮件
   * 使用BaseRepository的updateOne方法优化性能
   */
  async update(
    uid: string,
    updateData: UpdateQuery<MailDocument>,
    session?: ClientSession
  ): Promise<XResult<MailDocument | null>> {
    const result = await this.updateOne(
      { uid },
      updateData,
      session
    );

    if (XResultUtils.isSuccess(result) && result.data) {
      this.logger.log(`邮件更新成功: ${uid}`);
    }

    return result;
  }

  /**
   * 标记邮件为已读
   * 使用BaseRepository的update方法优化性能
   */
  async markAsRead(uid: string): Promise<XResult<MailDocument | null>> {
    return this.update(uid, {
      status: MailStatus.READ,
      readTime: Date.now()
    });
  }

  /**
   * 标记邮件附件为已领取
   * 使用BaseRepository的update方法优化性能
   */
  async markAttachmentClaimed(uid: string): Promise<XResult<MailDocument | null>> {
    return this.update(uid, {
      status: MailStatus.CLAIMED,
      claimTime: Date.now()
    });
  }

  /**
   * 删除邮件
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async delete(uid: string): Promise<XResult<boolean>> {
    const result = await this.deleteOne({ uid });
    if (XResultUtils.isSuccess(result) && result.data) {
      this.logger.log(`邮件删除成功: ${uid}`);
      return XResultUtils.ok(true);
    }
    return XResultUtils.ok(false);
  }

  /**
   * 批量删除过期邮件
   * 使用BaseRepository的deleteMany方法优化性能
   */
  async deleteExpiredMails(): Promise<XResult<number>> {
    const now = Date.now();
    const result = await this.deleteMany({
      expireTime: { $gt: 0, $lt: now }
    });

    if (XResultUtils.isSuccess(result)) {
      const deletedCount = result.data.deletedCount || 0;
      this.logger.log(`删除过期邮件: ${deletedCount} 封`);
      return XResultUtils.ok(deletedCount);
    }

    return XResultUtils.ok(0);
  }

  /**
   * 查找玩家最老的邮件
   * 使用BaseRepository的findOne方法优化性能
   */
  async findOldestByReceiver(receiverUid: string): Promise<XResult<MailDocument | null>> {
    return this.findOne({ receiverUid }, {
      sort: { sendTime: 1 }
    });
  }

  /**
   * 统计玩家邮件数量
   * 使用BaseRepository的count方法优化性能
   */
  async countByReceiver(receiverUid: string): Promise<XResult<number>> {
    return this.count({ receiverUid });
  }

  /**
   * 查找有附件的邮件
   * 使用BaseRepository的findMany方法优化性能
   */
  async findWithAttachmentsByReceiver(receiverUid: string): Promise<XResult<MailDocument[]>> {
    return this.findMany({
      receiverUid,
      'attachList.0': { $exists: true },
      status: { $ne: MailStatus.CLAIMED }
    }, {
      sort: { sendTime: -1 }
    });
  }

  /**
   * 查找有附件的邮件（Lean查询优化版本）
   */
  async findWithAttachmentsByReceiverLean(receiverUid: string): Promise<XResult<any[]>> {
    return this.findManyLean({
      receiverUid,
      'attachList.0': { $exists: true },
      status: { $ne: MailStatus.CLAIMED }
    }, {
      sort: { sendTime: -1 },
      select: ['uid', 'title', 'sendTime', 'attachList', 'status']
    });
  }

  /**
   * 根据发送者查找邮件
   * 使用BaseRepository的findMany方法优化性能
   */
  async findBySender(senderUid: string): Promise<XResult<MailDocument[]>> {
    return this.findMany({ senderUid }, {
      sort: { sendTime: -1 }
    });
  }

  /**
   * 根据邮件类型查找邮件
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByType(receiverUid: string, mailType: number): Promise<XResult<MailDocument[]>> {
    return this.findMany({ receiverUid, mailType }, {
      sort: { sendTime: -1 }
    });
  }

  /**
   * 批量更新邮件状态
   * 使用BaseRepository的updateMany方法优化性能
   */
  async batchUpdateStatus(
    filter: FilterQuery<MailDocument>,
    status: MailStatus
  ): Promise<XResult<number>> {
    const result = await this.updateMany(
      filter,
      { status, readTime: Date.now() }
    );

    if (XResultUtils.isSuccess(result)) {
      const modifiedCount = result.data.modifiedCount || 0;
      this.logger.log(`批量更新邮件状态: ${modifiedCount} 封`);
      return XResultUtils.ok(modifiedCount);
    }

    return XResultUtils.ok(0);
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加邮件特定的验证规则
   */
  protected validateData(data: Partial<Mail>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.uid) {
        return XResultUtils.error('邮件UID不能为空', 'MAIL_UID_REQUIRED');
      }

      if (!data.receiverUid) {
        return XResultUtils.error('接收者UID不能为空', 'RECEIVER_UID_REQUIRED');
      }

      if (!data.title || data.title.trim().length === 0) {
        return XResultUtils.error('邮件标题不能为空', 'TITLE_REQUIRED');
      }

      if (data.title.length > 100) {
        return XResultUtils.error('邮件标题不能超过100个字符', 'TITLE_TOO_LONG');
      }
    }

    if (data.content && data.content.length > 2000) {
      return XResultUtils.error('邮件内容不能超过2000个字符', 'CONTENT_TOO_LONG');
    }

    if (data.sendTime !== undefined && data.sendTime <= 0) {
      return XResultUtils.error('发送时间必须大于0', 'INVALID_SEND_TIME');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对邮件数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByUid': 300,                  // 邮件详情缓存5分钟
      'findByUidLean': 180,              // 邮件简介缓存3分钟
      'findByReceiver': 120,             // 邮件列表缓存2分钟
      'findUnreadByReceiver': 60,        // 未读邮件缓存1分钟
      'countUnreadByReceiver': 60,       // 未读数量缓存1分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
