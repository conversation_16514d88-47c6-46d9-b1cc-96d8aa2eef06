/**
 * 公会系统Schema
 * 基于old项目association.js实体迁移
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 公会职位枚举（基于old项目）
export enum GuildPosition {
  PRESIDENT = 1,        // 会长
  VICE_PRESIDENT = 2,   // 副会长
  MEMBER = 3,           // 普通成员
}

// 公会成员信息子文档
@Schema({ _id: false })
export class GuildMember {
  @Prop({ required: true })
  characterId: string;     // 玩家UID

  @Prop({ required: true })
  characterName: string;   // 玩家名字

  @Prop({ default: 0 })
  isOnLine: number;     // 是否在线 1在线 0离线

  @Prop({ default: Date.now })
  leaveTime: number;    // 离线时间

  @Prop({ default: 0 })
  exp: number;          // 玩家活跃度

  @Prop({ default: 0 })
  contribute: number;   // 玩家贡献

  @Prop({ default: '' })
  faceUrl: string;      // 玩家头像

  @Prop({ default: 0 })
  strength: number;     // 玩家实力

  @Prop({ required: true, enum: GuildPosition })
  pos: GuildPosition;   // 玩家职位

  @Prop({ required: true })
  gid: string;          // 玩家gid

  @Prop()
  frontendId?: string;  // 前端ID

  @Prop()
  sessionId?: string;   // 会话ID

  @Prop({ default: Date.now })
  joinTime: number;     // 加入时间
}

// 公会申请信息子文档
@Schema({ _id: false })
export class GuildApplication {
  @Prop({ required: true })
  characterId: string;     // 申请者UID

  @Prop({ required: true })
  characterName: string;   // 申请者名字

  @Prop({ required: true })
  askTime: number;      // 申请时间

  @Prop({ required: true })
  gid: string;          // 玩家gid

  @Prop({ default: '' })
  faceUrl: string;      // 玩家头像

  @Prop({ default: 0 })
  strength: number;     // 玩家实力

  @Prop()
  frontendId?: string;  // 前端ID

  @Prop()
  sessionId?: string;   // 会话ID
}

// 公会日志子文档
@Schema({ _id: false })
export class GuildNotify {
  @Prop({ required: true })
  time: number;         // 时间

  @Prop({ required: true })
  text: string;         // 日志内容
}

// 主公会Schema（基于old项目Association结构）
@Schema({ 
  collection: 'guilds', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Guild {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  guildId: string;          // 公会ID

  @Prop({ required: true })
  creator: string;          // 创建者（会长名字）

  @Prop({ required: true })
  createTime: number;       // 创建时间

  @Prop({ default: "欢迎大家加入公会" })
  guildNotice: string;      // 公会公告

  @Prop({ default: 1 })
  faceId: number;           // 公会头像

  @Prop({ required: true, unique: true })
  guildName: string;        // 公会名字

  @Prop({ default: 1 })
  guildLevel: number;       // 公会等级

  @Prop({ default: 0 })
  exp: number;              // 公会经验

  @Prop({ default: 0 })
  contribute: number;       // 公会贡献

  // 成员信息
  @Prop({ type: [GuildMember], default: [] })
  allCharacter: GuildMember[]; // 公会所有玩家

  @Prop({ type: [GuildApplication], default: [] })
  approvalList: GuildApplication[]; // 申请列表

  @Prop({ default: 0 })
  vicePresident: number;    // 副会长数量

  @Prop({ type: [GuildNotify], default: [] })
  notifyList: GuildNotify[]; // 公会日志

  // 虚拟字段：当前成员数
  get memberCount(): number {
    return this.allCharacter.length;
  }

  // 虚拟字段：在线成员数
  get onlineMemberCount(): number {
    return this.allCharacter.filter(member => member.isOnLine === 1).length;
  }

  // 虚拟字段：是否已满员
  get isFull(): boolean {
    return this.allCharacter.length >= 10; // 最大成员数
  }

  // 虚拟字段：会长信息
  get president(): GuildMember | null {
    return this.allCharacter.find(member => member.pos === GuildPosition.PRESIDENT) || null;
  }

  // 虚拟字段：副会长列表
  get vicePresidents(): GuildMember[] {
    return this.allCharacter.filter(member => member.pos === GuildPosition.VICE_PRESIDENT);
  }

  // 虚拟字段：普通成员列表
  get members(): GuildMember[] {
    return this.allCharacter.filter(member => member.pos === GuildPosition.MEMBER);
  }

  // 虚拟字段：待处理申请数
  get pendingApplications(): number {
    return this.approvalList.length;
  }
}

export const GuildSchema = SchemaFactory.createForClass(Guild);

// 定义方法接口 - 基于GuildService的真实业务逻辑
export interface GuildMethods {
  // 现有方法
  addMember(memberInfo: Partial<GuildMember>): boolean;
  removeMember(characterId: string): boolean;
  addApplication(applicationInfo: Partial<GuildApplication>): boolean;
  removeApplication(characterId: string): boolean;
  promoteToVicePresident(characterId: string): boolean;
  demoteToMember(characterId: string): boolean;
  transferPresidency(characterId: string): boolean;
  addNotify(text: string): void;
  updateMemberOnlineStatus(characterId: string, isOnline: boolean): void;
  getMemberByCharacterId(characterId: string): GuildMember | null;
  hasPermission(characterId: string): boolean;

  // 新增实用业务方法 - 基于GuildService真实逻辑

  // 成员管理 - 基于GuildService
  canAddMember(): boolean;
  getApplicationByCharacterId(characterId: string): GuildApplication | null;
  hasApplication(characterId: string): boolean;
  isMember(characterId: string): boolean;
  getMemberPosition(characterId: string): GuildPosition | null;

  // 职位管理 - 基于GuildService
  canPromoteToVicePresident(): boolean;
  canTransferPresidency(characterId: string): boolean;
  updateMemberPosition(characterId: string, newPosition: GuildPosition): boolean;
  getVicePresidentCount(): number;

  // 公会升级 - 基于GuildService
  canLevelUp(): boolean;
  addExperience(exp: number): boolean;
  addContribution(contribution: number): boolean;
  getNextLevelRequirement(): number;

  // 申请管理 - 基于GuildService
  cleanExpiredApplications(expireHours: number): number;
  acceptApplication(characterId: string): boolean;
  rejectApplication(characterId: string): boolean;

  // 公告管理 - 基于GuildService
  updateNotice(notice: string): void;
  addSystemNotify(text: string): void;
  cleanOldNotifies(maxCount: number): void;

  // 统计分析 - 基于GuildService
  getMemberStats(): any;
  getActivityStats(): any;
  updateGuildStats(): void;

  // 数据验证 - 基于GuildService
  validateGuildData(): { isValid: boolean; errors: string[] };
  checkJurisdiction(characterId: string): boolean;

  // 数据转换 - 基于GuildService
  toGuildInfoDto(): any;
  toGuildListItem(): any;
  getGuildSummary(): any;
}

// 定义Document类型
export type GuildDocument = Guild & Document & GuildMethods;

// 创建索引
// 使用稀疏索引，忽略null值，避免多个null值的唯一索引冲突
GuildSchema.index({ guildId: 1 }, { unique: true, sparse: true });
GuildSchema.index({ guildName: 1 }, { unique: true, sparse: true });
GuildSchema.index({ creator: 1 });
GuildSchema.index({ level: 1 });
GuildSchema.index({ 'allCharacter.characterId': 1 });
GuildSchema.index({ 'approvalList.characterId': 1 });
GuildSchema.index({ createTime: 1 });

// 添加虚拟字段
GuildSchema.virtual('memberCount').get(function() {
  return this.allCharacter.length;
});

GuildSchema.virtual('onlineMemberCount').get(function() {
  return this.allCharacter.filter(member => member.isOnLine === 1).length;
});

GuildSchema.virtual('isFull').get(function() {
  return this.allCharacter.length >= 10;
});

GuildSchema.virtual('president').get(function() {
  return this.allCharacter.find(member => member.pos === GuildPosition.PRESIDENT) || null;
});

GuildSchema.virtual('vicePresidents').get(function() {
  return this.allCharacter.filter(member => member.pos === GuildPosition.VICE_PRESIDENT);
});

GuildSchema.virtual('members').get(function() {
  return this.allCharacter.filter(member => member.pos === GuildPosition.MEMBER);
});

GuildSchema.virtual('pendingApplications').get(function() {
  return this.approvalList.length;
});

// 添加实例方法
GuildSchema.methods.addMember = function(memberInfo: Partial<GuildMember>) {
  if (this.isFull) return false;
  
  const existingMember = this.allCharacter.find(m => m.characterId === memberInfo.characterId);
  if (existingMember) return false;
  
  const newMember: GuildMember = {
    characterId: memberInfo.characterId!,
    characterName: memberInfo.characterName!,
    isOnLine: memberInfo.isOnLine || 1,
    leaveTime: Date.now(),
    exp: memberInfo.exp || 0,
    contribute: memberInfo.contribute || 0,
    faceUrl: memberInfo.faceUrl || '',
    strength: memberInfo.strength || 0,
    pos: memberInfo.pos || GuildPosition.MEMBER,
    gid: memberInfo.gid!,
    frontendId: memberInfo.frontendId,
    sessionId: memberInfo.sessionId,
    joinTime: Date.now(),
  };
  
  this.allCharacter.push(newMember);
  return true;
};

GuildSchema.methods.removeMember = function(characterId: string) {
  const index = this.allCharacter.findIndex(m => m.characterId === characterId);
  if (index === -1) return false;
  
  const member = this.allCharacter[index];
  if (member.pos === GuildPosition.VICE_PRESIDENT) {
    this.vicePresident--;
  }
  
  this.allCharacter.splice(index, 1);
  return true;
};

GuildSchema.methods.addApplication = function(applicationInfo: Partial<GuildApplication>) {
  const existingApp = this.approvalList.find(a => a.characterId === applicationInfo.characterId);
  if (existingApp) return false;
  
  const newApplication: GuildApplication = {
    characterId: applicationInfo.characterId!,
    characterName: applicationInfo.characterName!,
    askTime: Date.now(),
    gid: applicationInfo.gid!,
    faceUrl: applicationInfo.faceUrl || '',
    strength: applicationInfo.strength || 0,
    frontendId: applicationInfo.frontendId,
    sessionId: applicationInfo.sessionId,
  };
  
  this.approvalList.push(newApplication);
  return true;
};

GuildSchema.methods.removeApplication = function(characterId: string) {
  const index = this.approvalList.findIndex(a => a.characterId === characterId);
  if (index === -1) return false;
  
  this.approvalList.splice(index, 1);
  return true;
};

GuildSchema.methods.addNotify = function(text: string) {
  const notify: GuildNotify = {
    time: Date.now(),
    text,
  };
  
  this.notifyList.push(notify);
  
  // 保持日志数量限制
  if (this.notifyList.length > 50) {
    this.notifyList.shift();
  }
};

GuildSchema.methods.getMemberByCharacterId = function(characterId: string) {
  return this.allCharacter.find(m => m.characterId === characterId) || null;
};

GuildSchema.methods.hasPermission = function(characterId: string) {
  const member = this.getMemberByCharacterId(characterId);
  if (!member) return false;

  return member.pos === GuildPosition.PRESIDENT || member.pos === GuildPosition.VICE_PRESIDENT;
};

// ==================== 新增实用业务方法实现 ====================

/**
 * 检查是否可以添加成员
 * 基于GuildService: applyJoinGuild方法中的成员限制检查逻辑
 */
GuildSchema.methods.canAddMember = function(): boolean {
  return this.allCharacter.length < 10; // 基于service中的最大成员数限制
};

/**
 * 根据玩家ID获取申请信息
 * 基于GuildService: processApplication方法中的申请查询逻辑
 */
GuildSchema.methods.getApplicationByCharacterId = function(characterId: string): GuildApplication | null {
  return this.approvalList.find(app => app.characterId === characterId) || null;
};

/**
 * 检查是否有申请
 * 基于GuildService: applyJoinGuild方法中的申请检查逻辑
 */
GuildSchema.methods.hasApplication = function(characterId: string): boolean {
  return this.approvalList.some(app => app.characterId === characterId);
};

/**
 * 检查是否为成员
 * 基于GuildService: 成员检查逻辑
 */
GuildSchema.methods.isMember = function(characterId: string): boolean {
  return this.allCharacter.some(member => member.characterId === characterId);
};

/**
 * 获取成员职位
 * 基于GuildService: changePosition方法中的职位查询逻辑
 */
GuildSchema.methods.getMemberPosition = function(characterId: string): GuildPosition | null {
  const member = this.getMemberByCharacterId(characterId);
  return member ? member.pos : null;
};

/**
 * 检查是否可以提升为副会长
 * 基于GuildService: changePosition方法中的副会长限制逻辑
 */
GuildSchema.methods.canPromoteToVicePresident = function(): boolean {
  return this.vicePresidents.length < 2; // 基于service中的副会长数量限制
};

/**
 * 检查是否可以转让会长
 * 基于GuildService: transferPresidency方法中的转让条件逻辑
 */
GuildSchema.methods.canTransferPresidency = function(characterId: string): boolean {
  const member = this.getMemberByCharacterId(characterId);
  if (!member) return false;

  // 只能转让给副会长或普通成员
  return member.pos === GuildPosition.VICE_PRESIDENT || member.pos === GuildPosition.MEMBER;
};

/**
 * 更新成员职位
 * 基于GuildService: changePosition方法逻辑
 */
GuildSchema.methods.updateMemberPosition = function(characterId: string, newPosition: GuildPosition): boolean {
  const member = this.getMemberByCharacterId(characterId);
  if (!member) return false;

  const oldPosition = member.pos;

  // 检查职位变更的合法性
  if (newPosition === GuildPosition.VICE_PRESIDENT && !this.canPromoteToVicePresident()) {
    return false;
  }

  // 更新职位
  member.pos = newPosition;

  // 更新副会长计数
  if (oldPosition === GuildPosition.VICE_PRESIDENT && newPosition !== GuildPosition.VICE_PRESIDENT) {
    this.vicePresident--;
  } else if (oldPosition !== GuildPosition.VICE_PRESIDENT && newPosition === GuildPosition.VICE_PRESIDENT) {
    this.vicePresident++;
  }

  return true;
};

/**
 * 获取副会长数量
 * 基于GuildService: 副会长统计逻辑
 */
GuildSchema.methods.getVicePresidentCount = function(): number {
  return this.vicePresidents.length;
};

// ==================== 应用Result模式适配 ====================

// 在模块末尾应用装饰器
import { SchemaResultAdapter } from '@libs/common/decorators/schema-result.decorator';

// 为GuildSchema的所有方法生成Result版本
SchemaResultAdapter.wrapMethods(GuildSchema, {
  excludeMethods: [
    'toObject', 'toJSON', 'toString', 'valueOf', 'save', 'remove',
    'populate', 'depopulate', 'isModified', 'markModified'
  ]
});

// 现在可以使用以下两种方式调用方法：
// 1. 原始方法: guild.canAddMember() -> boolean
// 2. Result方法: guild.canAddMemberResult() -> XResult<boolean>

console.log('✅ Guild Schema Result方法适配完成');
