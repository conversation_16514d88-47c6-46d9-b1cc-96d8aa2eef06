/**
 * 聊天消息Schema定义
 * 基于MongoDB存储聊天消息和频道信息
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 聊天消息类型枚举
export enum ChatMessageType {
  PRIVATE = 'private',      // 私聊
  GUILD = 'guild',         // 公会聊天
  WORLD = 'world',         // 世界频道
  SYSTEM = 'system'        // 系统消息
}

// 聊天消息状态枚举
export enum ChatMessageStatus {
  SENT = 'sent',           // 已发送
  DELIVERED = 'delivered', // 已送达
  READ = 'read',           // 已读
  DELETED = 'deleted'      // 已删除
}

// 聊天消息Schema
@Schema({ 
  collection: 'chat_messages', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class ChatMessage {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  messageId: string;        // 消息唯一ID

  @Prop({ required: true, index: true })
  senderId: string;         // 发送者角色ID

  @Prop({ required: true })
  senderName: string;       // 发送者名称

  @Prop({ required: true, index: true })
  channelId: string;        // 频道ID

  @Prop({ required: true, enum: ChatMessageType })
  messageType: ChatMessageType; // 消息类型

  // 消息内容
  @Prop({ required: true })
  content: string;          // 消息内容

  @Prop({ type: Object })
  extraData?: any;          // 额外数据（表情、图片等）

  // 接收者信息（私聊时使用）
  @Prop({ index: true })
  receiverId?: string;      // 接收者角色ID

  @Prop()
  receiverName?: string;    // 接收者名称

  // 状态信息
  @Prop({ default: ChatMessageStatus.SENT, enum: ChatMessageStatus })
  status: ChatMessageStatus; // 消息状态

  @Prop({ required: true })
  sendTime: number;         // 发送时间戳

  @Prop({ default: 0 })
  readTime: number;         // 读取时间戳

  // 服务器信息
  @Prop({ required: true, index: true })
  serverId: string;         // 区服ID
}

export const ChatMessageSchema = SchemaFactory.createForClass(ChatMessage);

// 定义ChatMessage方法接口 - 基于ChatService的真实业务逻辑
export interface ChatMessageMethods {
  // 消息状态管理 - 基于ChatService
  markAsDelivered(): void;
  markAsRead(): void;
  markAsDeleted(): void;
  isPrivateMessage(): boolean;
  isSystemMessage(): boolean;

  // 消息验证 - 基于ChatService
  validateMessage(): { isValid: boolean; errors: string[] };
  canBeDeleted(userId: string): boolean;
  isExpired(expireHours?: number): boolean;

  // 数据转换 - 基于ChatService
  toChatMessageResponse(): any;
  toClientMessageData(): any;
  getMessageSummary(): any;
}

// 定义Document类型
export type ChatMessageDocument = ChatMessage & Document & ChatMessageMethods;

// 创建索引
ChatMessageSchema.index({ messageId: 1 }, { unique: true });
ChatMessageSchema.index({ channelId: 1, sendTime: -1 }); // 频道消息按时间倒序
ChatMessageSchema.index({ senderId: 1, sendTime: -1 });  // 发送者消息按时间倒序
ChatMessageSchema.index({ receiverId: 1, sendTime: -1 }); // 接收者消息按时间倒序
ChatMessageSchema.index({ messageType: 1, sendTime: -1 }); // 消息类型按时间倒序
ChatMessageSchema.index({ serverId: 1, sendTime: -1 });   // 区服消息按时间倒序

// 聊天频道Schema
@Schema({ 
  collection: 'chat_channels', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class ChatChannel {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  channelId: string;        // 频道唯一ID

  @Prop({ required: true })
  channelName: string;      // 频道名称

  @Prop({ required: true, enum: ChatMessageType })
  channelType: ChatMessageType; // 频道类型

  // 成员信息
  @Prop({ type: [String], default: [] })
  members: string[];        // 频道成员角色ID列表

  @Prop({ default: 0 })
  memberCount: number;      // 成员数量

  @Prop({ default: 100 })
  maxMembers: number;       // 最大成员数

  // 频道设置
  @Prop({ default: true })
  isActive: boolean;        // 是否活跃

  @Prop({ default: false })
  isPrivate: boolean;       // 是否私有

  @Prop()
  ownerId?: string;         // 频道所有者（公会频道等）

  // 统计信息
  @Prop({ default: 0 })
  messageCount: number;     // 消息总数

  @Prop({ default: 0 })
  lastMessageTime: number;  // 最后消息时间

  @Prop()
  lastMessageContent?: string; // 最后消息内容

  // 服务器信息
  @Prop({ required: true, index: true })
  serverId: string;         // 区服ID
}

export const ChatChannelSchema = SchemaFactory.createForClass(ChatChannel);

// 定义Document类型
export type ChatChannelDocument = ChatChannel & Document;

// 创建索引
ChatChannelSchema.index({ channelId: 1 }, { unique: true });
ChatChannelSchema.index({ channelType: 1, serverId: 1 });
ChatChannelSchema.index({ members: 1 });
ChatChannelSchema.index({ ownerId: 1 });
ChatChannelSchema.index({ serverId: 1 });

// 添加虚拟字段
ChatChannelSchema.virtual('isFull').get(function() {
  return this.memberCount >= this.maxMembers;
});

ChatChannelSchema.virtual('hasRecentActivity').get(function() {
  const oneHourAgo = Date.now() - (60 * 60 * 1000);
  return this.lastMessageTime > oneHourAgo;
});
