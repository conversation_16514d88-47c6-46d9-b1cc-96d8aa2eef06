/**
 * 邮件系统Schema
 * 基于old项目email.js实体迁移
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 邮件类型枚举（基于old项目）
export enum MailType {
  SYSTEM = 1,           // 系统邮件
  REWARD = 2,           // 奖励邮件
  FRIEND = 3,           // 好友邮件
  GUILD = 4,            // 公会邮件
  ACTIVITY = 5,         // 活动邮件
  COMPENSATION = 6,     // 补偿邮件
}

// 邮件状态枚举
export enum MailStatus {
  UNREAD = 0,           // 未读
  READ = 1,             // 已读
  CLAIMED = 2,          // 已领取附件
  DELETED = 3,          // 已删除
}

// 邮件附件子文档
@Schema({ _id: false })
export class MailAttachment {
  @Prop({ required: true })
  itemType: number;     // 物品类型

  @Prop({ required: true })
  resId: number;        // 资源ID

  @Prop({ required: true })
  num: number;          // 数量

  @Prop({ type: Object })
  param1?: any;         // 参数1
}

// 主邮件Schema（基于old项目email.js结构）
@Schema({ 
  collection: 'mails', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Mail {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  uid: string;          // 邮件唯一ID

  @Prop({ required: true, index: true })
  senderUid: string;    // 发送者ID

  @Prop({ required: true, index: true })
  receiverUid: string;  // 接收者ID

  @Prop({ required: true, enum: MailType })
  mailType: MailType;   // 邮件类型

  @Prop({ required: true })
  title: string;        // 邮件标题

  @Prop({ required: true })
  content: string;      // 邮件内容

  // 附件信息
  @Prop({ type: [MailAttachment], default: [] })
  attachList: MailAttachment[]; // 附件列表

  @Prop({ type: Object })
  specialAttachInfo?: any; // 特殊附件信息

  // 状态信息
  @Prop({ default: MailStatus.UNREAD, enum: MailStatus })
  status: MailStatus;   // 邮件状态

  @Prop({ required: true })
  sendTime: number;     // 发送时间

  @Prop({ default: 0 })
  readTime: number;     // 读取时间

  @Prop({ default: 0 })
  claimTime: number;    // 领取时间

  @Prop({ required: true })
  expireTime: number;   // 过期时间

  // 扩展参数（基于old项目）
  @Prop({ type: Object })
  param1?: any;         // 参数1

  @Prop({ type: Object })
  param2?: any;         // 参数2

  @Prop({ type: Object })
  param3?: any;         // 参数3

  @Prop({ type: Object })
  param4?: any;         // 参数4

  // 虚拟字段：是否有附件
  get hasAttachment(): boolean {
    return this.attachList && this.attachList.length > 0;
  }

  // 虚拟字段：是否已过期
  get isExpired(): boolean {
    return this.expireTime > 0 && Date.now() > this.expireTime;
  }

  // 虚拟字段：是否可以领取附件
  get canClaimAttachment(): boolean {
    return this.hasAttachment && this.status !== MailStatus.CLAIMED && !this.isExpired;
  }

  // 虚拟字段：剩余时间
  get remainingTime(): number {
    if (this.expireTime <= 0) return -1;
    const remaining = this.expireTime - Date.now();
    return remaining > 0 ? remaining : 0;
  }
}

export const MailSchema = SchemaFactory.createForClass(Mail);

// 定义方法接口 - 基于MailService的真实业务逻辑
export interface MailMethods {
  // 现有方法
  markAsRead(): void;
  claimAttachments(): MailAttachment[];
  isReadable(): boolean;
  isClaimable(): boolean;

  // 新增实用业务方法 - 基于MailService真实逻辑

  // 邮件状态管理 - 基于MailService
  markAsDeleted(): void;
  markAsClaimed(): void;
  canBeRead(): boolean;
  canBeClaimed(): boolean;
  canBeDeleted(): boolean;

  // 邮件验证 - 基于MailService
  validateMail(): { isValid: boolean; errors: string[] };
  isSystemMail(): boolean;
  isRewardMail(): boolean;
  isFriendMail(): boolean;
  isGuildMail(): boolean;

  // 时间管理 - 基于MailService
  getRemainingDays(): number;
  isExpiringSoon(hours?: number): boolean;
  extendExpireTime(hours: number): void;

  // 附件管理 - 基于MailService
  getAttachmentCount(): number;
  getAttachmentValue(): number;
  hasSpecialAttachment(): boolean;
  getAttachmentSummary(): any;

  // 数据转换 - 基于MailService
  toMailInfoDto(): any;
  toClientMailData(): any;
  getMailSummary(): any;
}

// 定义Document类型
export type MailDocument = Mail & Document & MailMethods;

// 创建索引
MailSchema.index({ uid: 1 }, { unique: true });
MailSchema.index({ receiverUid: 1 });
MailSchema.index({ senderUid: 1 });
MailSchema.index({ mailType: 1 });
MailSchema.index({ status: 1 });
MailSchema.index({ sendTime: 1 });
MailSchema.index({ expireTime: 1 });
MailSchema.index({ receiverUid: 1, status: 1 });
MailSchema.index({ receiverUid: 1, expireTime: 1 });

// 添加虚拟字段
MailSchema.virtual('hasAttachment').get(function() {
  return this.attachList && this.attachList.length > 0;
});

MailSchema.virtual('isExpired').get(function() {
  return this.expireTime > 0 && Date.now() > this.expireTime;
});

MailSchema.virtual('canClaimAttachment').get(function() {
  return this.hasAttachment && this.status !== MailStatus.CLAIMED && !this.isExpired;
});

MailSchema.virtual('remainingTime').get(function() {
  if (this.expireTime <= 0) return -1;
  const remaining = this.expireTime - Date.now();
  return remaining > 0 ? remaining : 0;
});

// 添加实例方法
MailSchema.methods.markAsRead = function() {
  if (this.status === MailStatus.UNREAD) {
    this.status = MailStatus.READ;
    this.readTime = Date.now();
  }
};

MailSchema.methods.claimAttachments = function() {
  if (!this.canClaimAttachment) {
    return [];
  }
  
  this.status = MailStatus.CLAIMED;
  this.claimTime = Date.now();
  
  return this.attachList;
};

MailSchema.methods.isReadable = function() {
  return !this.isExpired;
};

MailSchema.methods.isClaimable = function() {
  return this.canClaimAttachment;
};

// 添加静态方法
MailSchema.statics.findByReceiver = function(receiverUid: string) {
  return this.find({ receiverUid }).sort({ sendTime: -1 });
};

MailSchema.statics.findUnreadByReceiver = function(receiverUid: string) {
  return this.find({ 
    receiverUid, 
    status: MailStatus.UNREAD,
    expireTime: { $gt: Date.now() }
  }).sort({ sendTime: -1 });
};

MailSchema.statics.findExpiredMails = function() {
  return this.find({
    expireTime: { $gt: 0, $lt: Date.now() }
  });
};

// 添加中间件
MailSchema.pre('save', function(next) {
  // 自动设置过期时间（如果未设置）
  if (!this.expireTime) {
    this.expireTime = Date.now() + (7 * 24 * 60 * 60 * 1000); // 默认7天过期
  }
  next();
});

MailSchema.pre('find', function() {
  // 自动过滤已过期的邮件
  this.where({ 
    $or: [
      { expireTime: 0 },
      { expireTime: { $gt: Date.now() } }
    ]
  });
});

MailSchema.pre('findOne', function() {
  // 自动过滤已过期的邮件
  this.where({ 
    $or: [
      { expireTime: 0 },
      { expireTime: { $gt: Date.now() } }
    ]
  });
});
