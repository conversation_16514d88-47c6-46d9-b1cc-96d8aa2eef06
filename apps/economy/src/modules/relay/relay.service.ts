/**
 * 联赛转播服务
 * 基于old项目relay.js业务逻辑迁移
 */

import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { RelayRepository } from '@economy/common/repositories/relay.repository';

import { XResult, XResultUtils } from '@libs/common/types/result.type';

@Injectable()
export class RelayService {
  private readonly logger = new Logger(RelayService.name);

  constructor(
    private readonly relayRepository: RelayRepository,
  ) {}

  /**
   * 获取联赛转播信息
   */
  async getRelayInfo(uid: string, serverId: string): Promise<XResult<any>> {
    try {
      const relay = await this.relayRepository.getOrCreateRelay(uid, serverId);
      
      return {
        uid,
        isJoin: relay.isJoin,
        integral: relay.integral,
        totalIntegral: relay.totalIntegral,
        joinTime: relay.joinTime,
        expireTime: relay.expireTime,
        isExpired: relay.isExpired(),
        awardList: relay.awardList,
        availableAwards: relay.getAvailableAwards(),
        totalAwardsReceived: relay.totalAwardsReceived,
        totalConvertCount: relay.totalConvertCount,
        totalIntegralSpent: relay.totalIntegralSpent,
        seasonId: relay.seasonId,
        weekId: relay.weekId,
      };
    } catch (error) {
      this.logger.error('获取联赛转播信息失败', error);
      throw error;
    }
  }

  /**
   * 购买联赛转播
   * 对应old项目中的buyRelay方法
   */
  async buyRelay(uid: string, serverId: string): Promise<XResult<any>> {
    try {
      const relay = await this.relayRepository.getOrCreateRelay(uid, serverId);
      
      // 检查是否已加入
      if (relay.isJoin === 1) {
        throw new BadRequestException({
          code: ErrorCode.RELAY_ALREADY_JOINED,
          message: ErrorMessages[ErrorCode.RELAY_ALREADY_JOINED],
        });
      }

      // 检查是否过期
      if (relay.isExpired()) {
        throw new BadRequestException({
          code: ErrorCode.RELAY_EXPIRED,
          message: ErrorMessages[ErrorCode.RELAY_EXPIRED],
        });
      }

      // TODO: 检查玩家资源是否足够（钻石等）
      const cost = await this.getRelayCost();
      const playerDiamond = await this.getPlayerDiamond(uid);
      
      if (playerDiamond < cost) {
        throw new BadRequestException({
          code: ErrorCode.DIAMOND_NOT_ENOUGH,
          message: ErrorMessages[ErrorCode.DIAMOND_NOT_ENOUGH],
        });
      }

      // TODO: 扣除钻石
      await this.deductPlayerDiamond(uid, cost);

      // 加入转播
      relay.isJoin = 1;
      relay.joinTime = Date.now();
      
      // 设置过期时间（7天）
      relay.expireTime = Date.now() + (7 * 24 * 60 * 60 * 1000);
      
      // 初始化奖励列表
      relay.awardList = await this.initRelayAwards();
      
      await relay.save();

      this.logger.log(`购买联赛转播成功: ${uid}, 费用: ${cost}`);

      return {
        success: true,
        cost,
        joinTime: relay.joinTime,
        expireTime: relay.expireTime,
        awardList: relay.awardList,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('购买联赛转播失败', error);
      throw error;
    }
  }

  /**
   * 商城兑换
   * 对应old项目中的convertibility方法
   */
  async convertibility(uid: string, serverId: string, id: number): Promise<XResult<any>> {
    try {
      if (id < 1) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
        });
      }

      const relay = await this.relayRepository.getOrCreateRelay(uid, serverId);
      
      // 检查是否已加入转播
      if (relay.isJoin === 0) {
        throw new BadRequestException({
          code: ErrorCode.RELAY_NOT_JOINED,
          message: ErrorMessages[ErrorCode.RELAY_NOT_JOINED],
        });
      }

      // 检查是否过期
      if (relay.isExpired()) {
        throw new BadRequestException({
          code: ErrorCode.RELAY_EXPIRED,
          message: ErrorMessages[ErrorCode.RELAY_EXPIRED],
        });
      }

      // TODO: 获取兑换配置
      const convertConfig = await this.getConvertConfig(id);
      if (!convertConfig) {
        throw new BadRequestException({
          code: ErrorCode.EXCHANGE_CONFIG_NOT_FOUND,
          message: ErrorMessages[ErrorCode.EXCHANGE_CONFIG_NOT_FOUND],
        });
      }

      // 检查积分是否足够
      if (!relay.canConvertItem(convertConfig.ItemId, convertConfig.Price)) {
        throw new BadRequestException({
          code: ErrorCode.INTEGRAL_NOT_ENOUGH,
          message: ErrorMessages[ErrorCode.INTEGRAL_NOT_ENOUGH],
        });
      }

      // TODO: 给予物品
      await this.addPlayerItem(uid, convertConfig.ItemId, convertConfig.Num);

      // 记录兑换
      relay.addConvertRecord(id, convertConfig.ItemId, convertConfig.Price, convertConfig.Num);
      
      await relay.save();

      this.logger.log(`商城兑换成功: ${uid}, 兑换ID: ${id}, 物品: ${convertConfig.ItemId}, 数量: ${convertConfig.Num}, 积分: ${convertConfig.Price}`);

      return {
        success: true,
        convertId: id,
        itemId: convertConfig.ItemId,
        itemNum: convertConfig.Num,
        integralCost: convertConfig.Price,
        remainingIntegral: relay.integral,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('商城兑换失败', error);
      throw error;
    }
  }

  /**
   * 领取奖励
   * 对应old项目中的receiveAward方法
   */
  async receiveAward(uid: string, serverId: string, awardId: number): Promise<XResult<any>> {
    try {
      if (awardId < 1) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
        });
      }

      const relay = await this.relayRepository.getOrCreateRelay(uid, serverId);
      
      // 检查是否已加入转播
      if (relay.isJoin === 0) {
        throw new BadRequestException({
          code: ErrorCode.RELAY_NOT_JOINED,
          message: ErrorMessages[ErrorCode.RELAY_NOT_JOINED],
        });
      }

      // 检查奖励是否可领取
      if (!relay.canReceiveAward(awardId)) {
        throw new BadRequestException({
          code: ErrorCode.AWARD_NOT_AVAILABLE,
          message: ErrorMessages[ErrorCode.AWARD_NOT_AVAILABLE],
        });
      }

      const award = relay.awardList.find(a => a.id === awardId);
      if (!award) {
        throw new NotFoundException({
          code: ErrorCode.AWARD_NOT_FOUND,
          message: ErrorMessages[ErrorCode.AWARD_NOT_FOUND],
        });
      }

      // TODO: 发放奖励
      await this.giveRewards(uid, award.rewards);

      // 标记为已领取
      const success = relay.receiveAward(awardId);
      if (!success) {
        throw new BadRequestException({
          code: ErrorCode.AWARD_RECEIVE_FAILED,
          message: ErrorMessages[ErrorCode.AWARD_RECEIVE_FAILED],
        });
      }

      await relay.save();

      this.logger.log(`领取转播奖励成功: ${uid}, 奖励ID: ${awardId}`);

      return {
        success: true,
        awardId,
        rewards: award.rewards,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('领取转播奖励失败', error);
      throw error;
    }
  }

  /**
   * 一键领取所有奖励
   * 对应old项目中的receiveAllAward方法
   */
  async receiveAllAward(uid: string, serverId: string): Promise<XResult<any>> {
    try {
      const relay = await this.relayRepository.getOrCreateRelay(uid, serverId);
      
      // 检查是否已加入转播
      if (relay.isJoin === 0) {
        throw new BadRequestException({
          code: ErrorCode.RELAY_NOT_JOINED,
          message: ErrorMessages[ErrorCode.RELAY_NOT_JOINED],
        });
      }

      const availableAwards = relay.getAvailableAwards();
      if (availableAwards.length === 0) {
        throw new BadRequestException({
          code: ErrorCode.NO_AWARDS_AVAILABLE,
          message: ErrorMessages[ErrorCode.NO_AWARDS_AVAILABLE],
        });
      }

      const receivedAwards = [];
      let totalRewards = [];

      for (const award of availableAwards) {
        // TODO: 发放奖励
        await this.giveRewards(uid, award.rewards);
        
        // 标记为已领取
        relay.receiveAward(award.id);
        
        receivedAwards.push({
          awardId: award.id,
          rewards: award.rewards,
        });
        
        totalRewards = totalRewards.concat(award.rewards);
      }

      await relay.save();

      this.logger.log(`一键领取转播奖励成功: ${uid}, 领取数量: ${receivedAwards.length}`);

      return {
        success: true,
        receivedCount: receivedAwards.length,
        receivedAwards,
        totalRewards,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('一键领取转播奖励失败', error);
      throw error;
    }
  }

  /**
   * 添加积分
   * 用于游戏内其他系统调用
   */
  async addIntegral(uid: string, serverId: string, amount: number): Promise<XResult<any>> {
    try {
      const relay = await this.relayRepository.getOrCreateRelay(uid, serverId);
      
      // 只有加入转播的玩家才能获得积分
      if (relay.isJoin === 0 || relay.isExpired()) {
        return { success: false, reason: 'Not joined or expired' };
      }

      relay.addIntegral(amount);
      await relay.save();

      this.logger.log(`添加转播积分: ${uid}, 数量: ${amount}, 总积分: ${relay.integral}`);

      return {
        success: true,
        addedIntegral: amount,
        totalIntegral: relay.integral,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('添加转播积分失败', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 获取转播费用
   */
  private async getRelayCost(): Promise<XResult<number>> {
    // TODO: 从配置表获取
    return 100; // 默认100钻石
  }

  /**
   * 获取玩家钻石数量
   */
  private async getPlayerDiamond(uid: string): Promise<XResult<number>> {
    // TODO: 调用Character服务获取玩家钻石数量
    return 1000; // 模拟数据
  }

  /**
   * 扣除玩家钻石
   */
  private async deductPlayerDiamond(uid: string, amount: number): Promise<XResult<void>> {
    // TODO: 调用Character服务扣除玩家钻石
    this.logger.log(`扣除玩家钻石: ${uid}, 数量: ${amount}`);
  }

  /**
   * 给予玩家物品
   */
  private async addPlayerItem(uid: string, itemId: number, num: number): Promise<XResult<void>> {
    // TODO: 调用Character服务给予玩家物品
    this.logger.log(`给予玩家物品: ${uid}, 物品: ${itemId}, 数量: ${num}`);
  }

  /**
   * 发放奖励
   */
  private async giveRewards(uid: string, rewards: any): Promise<XResult<void>> {
    // TODO: 根据奖励类型发放不同奖励
    this.logger.log(`发放转播奖励: ${uid}, 奖励: ${JSON.stringify(rewards)}`);
  }

  /**
   * 初始化转播奖励
   */
  private async initRelayAwards(): Promise<XResult<any[]>> {
    // TODO: 从配置表获取转播奖励配置
    return [
      { id: 1, type: 1, integral: 100, isReceive: 0, rewards: { itemType: 'ITEM', resId: 3001, num: 1 }, receiveTime: 0 },
      { id: 2, type: 1, integral: 300, isReceive: 0, rewards: { itemType: 'ITEM', resId: 3002, num: 1 }, receiveTime: 0 },
      { id: 3, type: 1, integral: 500, isReceive: 0, rewards: { itemType: 'ITEM', resId: 3003, num: 1 }, receiveTime: 0 },
    ];
  }

  /**
   * 获取兑换配置
   */
  private async getConvertConfig(id: number): Promise<XResult<any>> {
    // TODO: 从配置表获取兑换配置
    return {
      ItemId: 4001,
      Num: 1,
      Price: 50,
    };
  }
}
