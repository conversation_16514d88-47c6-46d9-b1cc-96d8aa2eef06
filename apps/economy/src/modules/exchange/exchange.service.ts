/**
 * 兑换大厅服务
 * 基于old项目exchangeHall.js业务逻辑迁移
 */

import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { ExchangeRepository } from '@economy/common/repositories/exchange.repository';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';

import { XResult, XResultUtils } from '@libs/common/types/result.type';

@Injectable()
export class ExchangeService {
  private readonly logger = new Logger(ExchangeService.name);

  constructor(
    private readonly exchangeRepository: ExchangeRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 获取兑换大厅信息
   * 对应old项目中的getExchangeHallInfo方法
   */
  async getExchangeHallInfo(uid: string, serverId: string): Promise<XResult<any>> {
    try {
      const exchange = await this.exchangeRepository.getOrCreateExchange(uid, serverId);
      
      // 检查是否需要刷新
      if (exchange.needsDailyRefresh()) {
        // 生成新的兑换列表
        const newExchangeList = await this.generateExchangeList(1);
        exchange.exchangeList = newExchangeList;
        exchange.reExchangeHallTime = Date.now();
        
        // 重置刷新次数
        exchange.exchangeNumList = [
          { type: 1, num: 1 },
          { type: 2, num: 1 },
          { type: 3, num: 1 },
        ];
        
        await exchange.save();
      }

      // TODO: 获取玩家碎片数量
      const chipCount = await this.getPlayerChipCount(uid);

      return {
        uid,
        chip: chipCount,
        itemList: exchange.exchangeList,
        exchangeNumList: exchange.exchangeNumList,
        totalExchanges: exchange.totalExchanges,
        totalChipSpent: exchange.totalChipSpent,
      };
    } catch (error) {
      this.logger.error('获取兑换大厅信息失败', error);
      throw error;
    }
  }

  /**
   * 合成物品
   * 对应old项目中的compoundItem方法
   */
  async compoundItem(uid: string, serverId: string, resId: number, num: number): Promise<XResult<any>> {
    try {
      if (!resId || num < 1) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
        });
      }

      const exchange = await this.exchangeRepository.getOrCreateExchange(uid, serverId);

      // TODO: 检查玩家是否有足够的物品
      const itemCount = await this.getPlayerItemCount(uid, resId);
      
      // TODO: 获取物品配置
      const itemConfig = await this.getItemConfig(resId);
      if (!itemConfig || !itemConfig.IsUpgrade) {
        throw new BadRequestException({
          code: ErrorCode.ITEM_CONFIG_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ITEM_CONFIG_NOT_FOUND],
        });
      }

      const needNum = itemConfig.UpgradeNum * num;
      if (itemCount < needNum) {
        throw new BadRequestException({
          code: ErrorCode.ITEM_NOT_ENOUGH,
          message: ErrorMessages[ErrorCode.ITEM_NOT_ENOUGH],
        });
      }

      // TODO: 扣除材料物品
      await this.deductPlayerItem(uid, resId, needNum);

      // TODO: 给予合成结果
      const resultItemId = itemConfig.UpgradeId;
      await this.addPlayerItem(uid, resultItemId, num);

      // 记录合成历史
      exchange.addCompoundRecord(resId, num, resultItemId, num);
      await exchange.save();

      this.logger.log(`物品合成成功: ${uid}, 物品: ${resId}, 数量: ${num}, 结果: ${resultItemId}`);

      return {
        success: true,
        resId,
        num,
        resultItemId,
        resultNum: num,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('合成物品失败', error);
      throw error;
    }
  }

  /**
   * 分解物品
   * 对应old项目中的decomposeItem方法
   */
  async decomposeItem(uid: string, serverId: string, resId: number, num: number): Promise<XResult<any>> {
    try {
      if (!resId || num < 1) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
        });
      }

      const exchange = await this.exchangeRepository.getOrCreateExchange(uid, serverId);

      // TODO: 检查玩家是否有足够的物品
      const itemCount = await this.getPlayerItemCount(uid, resId);
      if (itemCount < num) {
        throw new BadRequestException({
          code: ErrorCode.ITEM_NOT_ENOUGH,
          message: ErrorMessages[ErrorCode.ITEM_NOT_ENOUGH],
        });
      }

      // TODO: 获取物品配置
      const itemConfig = await this.getItemConfig(resId);
      if (!itemConfig) {
        throw new BadRequestException({
          code: ErrorCode.ITEM_CONFIG_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ITEM_CONFIG_NOT_FOUND],
        });
      }

      const chipNum = itemConfig.Get * num;

      // TODO: 扣除物品
      await this.deductPlayerItem(uid, resId, num);

      // TODO: 给予碎片
      await this.addPlayerChip(uid, chipNum);

      // 记录分解历史
      exchange.addDecomposeRecord(resId, num, chipNum);
      await exchange.save();

      this.logger.log(`物品分解成功: ${uid}, 物品: ${resId}, 数量: ${num}, 获得碎片: ${chipNum}`);

      return {
        success: true,
        resId,
        num,
        chipGained: chipNum,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('分解物品失败', error);
      throw error;
    }
  }

  /**
   * 刷新兑换大厅
   * 对应old项目中的flushExchangeHall方法
   */
  async flushExchangeHall(uid: string, serverId: string, type: number, teamId?: number): Promise<XResult<any>> {
    try {
      const exchange = await this.exchangeRepository.getOrCreateExchange(uid, serverId);

      // 获取刷新配置
      const refreshConfig = await this.getRefreshConfig(type, exchange.getRefreshNumByType(type));
      if (!refreshConfig) {
        throw new BadRequestException({
          code: ErrorCode.REFRESH_CONFIG_NOT_FOUND,
          message: ErrorMessages[ErrorCode.REFRESH_CONFIG_NOT_FOUND],
        });
      }

      // TODO: 检查玩家资源是否足够
      const chipCost = refreshConfig.Num1;
      const goldCost = refreshConfig.Num2;

      const chipCount = await this.getPlayerChipCount(uid);
      const goldCount = await this.getPlayerGoldCount(uid);

      if (chipCount < chipCost) {
        throw new BadRequestException({
          code: ErrorCode.CHIP_NOT_ENOUGH,
          message: ErrorMessages[ErrorCode.CHIP_NOT_ENOUGH],
        });
      }

      if (goldCount < goldCost) {
        throw new BadRequestException({
          code: ErrorCode.GOLD_NOT_ENOUGH,
          message: ErrorMessages[ErrorCode.GOLD_NOT_ENOUGH],
        });
      }

      // TODO: 扣除资源
      await this.deductPlayerChip(uid, chipCost);
      await this.deductPlayerGold(uid, goldCost);

      // 生成新的兑换列表
      const newExchangeList = await this.generateExchangeList(type, teamId);
      exchange.exchangeList = newExchangeList;

      // 增加刷新次数
      const newRefreshNum = exchange.addRefreshNumByType(type);
      
      await exchange.save();

      this.logger.log(`兑换大厅刷新成功: ${uid}, 类型: ${type}, 刷新次数: ${newRefreshNum}`);

      return {
        success: true,
        type,
        itemList: newExchangeList,
        refreshNum: newRefreshNum,
        chipCost,
        goldCost,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('刷新兑换大厅失败', error);
      throw error;
    }
  }

  /**
   * 兑换物品
   * 对应old项目中的exchangeItem方法
   */
  async exchangeItem(uid: string, serverId: string, id: number): Promise<XResult<any>> {
    try {
      if (id < 1) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
        });
      }

      const exchange = await this.exchangeRepository.getOrCreateExchange(uid, serverId);

      // TODO: 获取兑换配置
      const exchangeConfig = await this.getExchangeConfig(id);
      if (!exchangeConfig) {
        throw new BadRequestException({
          code: ErrorCode.EXCHANGE_CONFIG_NOT_FOUND,
          message: ErrorMessages[ErrorCode.EXCHANGE_CONFIG_NOT_FOUND],
        });
      }

      // 检查是否已购买
      if (exchange.checkItemIsBuy(id)) {
        throw new BadRequestException({
          code: ErrorCode.ITEM_ALREADY_BOUGHT,
          message: ErrorMessages[ErrorCode.ITEM_ALREADY_BOUGHT],
        });
      }

      // TODO: 检查碎片是否足够
      const chipCount = await this.getPlayerChipCount(uid);
      if (chipCount < exchangeConfig.ShowPrice) {
        throw new BadRequestException({
          code: ErrorCode.CHIP_NOT_ENOUGH,
          message: ErrorMessages[ErrorCode.CHIP_NOT_ENOUGH],
        });
      }

      // TODO: 扣除碎片
      await this.deductPlayerChip(uid, exchangeConfig.ShowPrice);

      // TODO: 给予物品
      await this.addPlayerItem(uid, exchangeConfig.ItemId, exchangeConfig.Num);

      // 标记为已购买
      exchange.markItemAsBought(id);
      exchange.totalChipSpent += exchangeConfig.ShowPrice;
      
      await exchange.save();

      this.logger.log(`兑换物品成功: ${uid}, 兑换ID: ${id}, 物品: ${exchangeConfig.ItemId}, 数量: ${exchangeConfig.Num}`);

      return {
        success: true,
        exchangeId: id,
        itemId: exchangeConfig.ItemId,
        itemNum: exchangeConfig.Num,
        chipCost: exchangeConfig.ShowPrice,
        operationTime: Date.now(),
      };
    } catch (error) {
      this.logger.error('兑换物品失败', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 生成兑换列表
   */
  private async generateExchangeList(type: number, teamId?: number): Promise<XResult<any[]>> {
    // 基于old项目: 权重随机算法生成兑换商品
    try {
      // 1. 获取兑换配置表数据
      const exchangeConfigs = await this.getExchangeConfigsByType(type, teamId);
      if (!exchangeConfigs || exchangeConfigs.length === 0) {
        this.logger.warn(`没有找到类型${type}的兑换配置`);
        return [];
      }

      // 2. 计算总权重
      const totalWeight = exchangeConfigs.reduce((sum, config) => sum + (config.rate || 100), 0);

      // 3. 生成兑换列表（基于old项目：每次刷新生成6个物品）
      const exchangeList = [];
      const maxItems = 6;
      const usedItems = new Set<number>();

      for (let i = 0; i < maxItems && exchangeList.length < exchangeConfigs.length; i++) {
        // 4. 权重随机选择
        const selectedConfig = this.selectByWeight(exchangeConfigs, totalWeight, usedItems);
        if (selectedConfig) {
          exchangeList.push({
            id: selectedConfig.id,
            itemId: selectedConfig.itemId,
            price: selectedConfig.price || 100,
            isBuy: 0,
            awardGroup: selectedConfig.awardGroup || type,
            rate: selectedConfig.rate || 100,
            rewardTeam: selectedConfig.rewardTeam || 1,
          });
          usedItems.add(selectedConfig.id);
        }
      }

      this.logger.debug(`生成兑换列表: 类型${type}, 数量${exchangeList.length}`);
      return exchangeList;
    } catch (error) {
      this.logger.error('生成兑换列表失败', error);
      // 返回默认数据
      return [
        { id: 1001, itemId: 2001, price: 100, isBuy: 0, awardGroup: type, rate: 100, rewardTeam: 1 },
        { id: 1002, itemId: 2002, price: 200, isBuy: 0, awardGroup: type, rate: 80, rewardTeam: 1 },
        { id: 1003, itemId: 2003, price: 300, isBuy: 0, awardGroup: type, rate: 60, rewardTeam: 1 },
      ];
    }
  }

  /**
   * 根据类型获取兑换配置
   * 基于old项目: Exchange配置表查询
   */
  private async getExchangeConfigsByType(type: number, teamId?: number): Promise<XResult<any[]>> {
    try {
      // TODO: 从配置表获取兑换配置
      // const configs = await this.gameConfig.exchange?.getByType(type);
      // return configs.filter(config => !teamId || config.rewardTeam === teamId);

      // 暂时返回模拟数据
      return [
        { id: 1001, itemId: 2001, price: 100, rate: 100, awardGroup: type, rewardTeam: teamId || 1 },
        { id: 1002, itemId: 2002, price: 200, rate: 80, awardGroup: type, rewardTeam: teamId || 1 },
        { id: 1003, itemId: 2003, price: 300, rate: 60, awardGroup: type, rewardTeam: teamId || 1 },
        { id: 1004, itemId: 2004, price: 400, rate: 40, awardGroup: type, rewardTeam: teamId || 1 },
        { id: 1005, itemId: 2005, price: 500, rate: 20, awardGroup: type, rewardTeam: teamId || 1 },
        { id: 1006, itemId: 2006, price: 600, rate: 10, awardGroup: type, rewardTeam: teamId || 1 },
      ];
    } catch (error) {
      this.logger.error('获取兑换配置失败', error);
      return [];
    }
  }

  /**
   * 权重随机选择
   * 基于old项目: 权重随机算法
   */
  private selectByWeight(configs: any[], totalWeight: number, usedItems: Set<number>): any {
    // 过滤已使用的物品
    const availableConfigs = configs.filter(config => !usedItems.has(config.id));
    if (availableConfigs.length === 0) {
      return null;
    }

    // 重新计算可用配置的总权重
    const availableTotalWeight = availableConfigs.reduce((sum, config) => sum + (config.rate || 100), 0);

    // 生成随机数
    let randomWeight = Math.random() * availableTotalWeight;

    // 根据权重选择
    for (const config of availableConfigs) {
      randomWeight -= (config.rate || 100);
      if (randomWeight <= 0) {
        return config;
      }
    }

    // 如果没有选中，返回第一个可用的
    return availableConfigs[0];
  }

  /**
   * 获取玩家碎片数量
   * 基于old项目: player.chip字段
   */
  private async getPlayerChipCount(uid: string): Promise<XResult<number>> {
    try {
      // 调用Character服务获取玩家碎片数量（基于old项目player.chip字段）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId: uid }
      );

      if (result && result.code === 0 && result.data) {
        return result.data.chip || 0;
      }

      this.logger.warn(`获取玩家碎片数量失败，玩家不存在: ${uid}`);
      return 0;
    } catch (error) {
      this.logger.error('获取玩家碎片数量失败', error);
      return 0;
    }
  }

  /**
   * 获取玩家物品数量
   * 基于old项目: bag.getItemNum方法
   */
  private async getPlayerItemCount(uid: string, resId: number): Promise<XResult<number>> {
    try {
      // 调用Character服务获取玩家物品数量（基于old项目bag.getItemNumByResID）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'item.getItemQuantityByConfigId',
        { characterId: uid, configId: resId }
      );

      if (result && result.code === 0) {
        return result.quantity || 0;
      }

      return 0;
    } catch (error) {
      this.logger.error(`获取玩家物品数量失败: ${uid}, 物品: ${resId}`, error);
      return 0;
    }
  }

  /**
   * 获取玩家金币数量
   * 基于old项目: player.gold字段
   */
  private async getPlayerGoldCount(uid: string): Promise<XResult<number>> {
    try {
      // 调用Character服务获取玩家金币数量（基于old项目player.gold字段）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId: uid }
      );

      if (result && result.code === 0 && result.data) {
        return result.data.gold || 0;
      }

      return 0;
    } catch (error) {
      this.logger.error('获取玩家金币数量失败', error);
      return 0;
    }
  }

  /**
   * 获取物品配置
   * 基于old项目: dataApi.allData.data["Item"][resId]
   */
  private async getItemConfig(resId: number): Promise<XResult<any>> {
    try {
      // 从Item配置表获取物品配置（基于old项目Item.json）
      const config = await this.gameConfig.item.get(resId);
      if (!config) {
        this.logger.warn(`物品配置不存在: ${resId}`);
        return null;
      }

      // 转换为old项目兼容的格式
      const itemConfig = {
        id: config.id,
        IsUpgrade: config.isUpgrade || 0,        // 是否可升级 0不可 1可以
        UpgradeNum: config.upgradeNumber || 1,   // 升级需要的数量
        UpgradeId: config.upgradeId || 0,        // 升级后的物品ID
        Get: config.get || 0,                    // 分解获得的碎片数量
        name: config.name || `物品${resId}`,
        type: config.type || 0,
      };

      this.logger.debug(`获取物品配置成功: ${resId}`, itemConfig);
      return itemConfig;
    } catch (error) {
      this.logger.error('获取物品配置失败', error);
      return null;
    }
  }

  /**
   * 获取刷新配置
   * 基于old项目: dataApi.allData.data["ExchangeControl"][type]
   */
  private async getRefreshConfig(type: number, refreshNum: number): Promise<XResult<any>> {
    try {
      // 从ExchangeControl配置表获取刷新配置（基于old项目ExchangeControl.json）
      const configs = await this.gameConfig.exchangeControl.getAll();
      if (!configs || configs.length === 0) {
        this.logger.warn('ExchangeControl配置表为空');
        return {
          Num1: 50 * refreshNum,  // 默认碎片消耗
          Num2: 100 * refreshNum, // 默认金币消耗
        };
      }

      // 查找对应类型的刷新配置（基于old项目ExchangeControl结构）
      const refreshConfig = configs.find(config => config.id === type);
      if (!refreshConfig) {
        this.logger.warn(`刷新配置不存在: 类型${type}`);
        return {
          Num1: 50 * refreshNum,  // 默认碎片消耗
          Num2: 100 * refreshNum, // 默认金币消耗
        };
      }

      return {
        Num1: (refreshConfig.num1 || 50) * refreshNum,  // 碎片消耗（Type1对应的数量）
        Num2: (refreshConfig.num2 || 100) * refreshNum, // 金币消耗（Type2对应的数量）
      };
    } catch (error) {
      this.logger.error('获取刷新配置失败', error);
      return {
        Num1: 50 * refreshNum,  // 默认碎片消耗
        Num2: 100 * refreshNum, // 默认金币消耗
      };
    }
  }

  /**
   * 获取兑换配置
   * 基于old项目: dataApi.allData.data["ExchangeShop"][id]
   */
  private async getExchangeConfig(id: number): Promise<XResult<any>> {
    try {
      // 从ExchangeShop配置表获取兑换配置（基于old项目ExchangeShop.json）
      const config = await this.gameConfig.exchangeShop.get(id);
      if (!config) {
        this.logger.warn(`兑换配置不存在: ${id}`);
        return null;
      }

      // 转换为old项目兼容的格式
      const exchangeConfig = {
        id: config.id,
        ShowPrice: config.showPrice || 100,      // 显示价格（碎片消耗）
        ItemId: config.itemId || 0,              // 物品ID
        Num: config.num || 1,                    // 物品数量
        TotalPrice: config.totalPrice || 0,      // 总价格
        MoneyType: config.moneyType || 1,        // 货币类型
        Rate: config.rate || 100,                // 权重
        AwardGroup: config.awardGroup || 1,      // 奖励组
        Club: config.club || 0,                  // 俱乐部限制
        RewardTeam: config.rewardTeam || 1,      // 奖励队伍
      };

      this.logger.debug(`获取兑换配置成功: ${id}`, exchangeConfig);
      return exchangeConfig;
    } catch (error) {
      this.logger.error('获取兑换配置失败', error);
      return null;
    }
  }

  /**
   * 扣除玩家物品
   * 基于old项目: bag.delItem方法
   */
  private async deductPlayerItem(uid: string, resId: number, num: number): Promise<XResult<void>> {
    try {
      // 调用Character服务扣除玩家物品（基于old项目bag.delItem）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'item.removeItem',
        { characterId: uid, configId: resId, quantity: num, reason: 'exchange' }
      );

      if (result.code !== 0) {
        throw new Error(`扣除物品失败: ${result.message || '未知错误'}`);
      }

      this.logger.log(`扣除玩家物品成功: ${uid}, 物品: ${resId}, 数量: ${num}`);
    } catch (error) {
      this.logger.error('扣除玩家物品失败', error);
      throw error;
    }
  }

  /**
   * 给予玩家物品
   * 基于old项目: bag.addItem方法
   */
  private async addPlayerItem(uid: string, resId: number, num: number): Promise<XResult<void>> {
    try {
      // 调用Character服务给予玩家物品（基于old项目bag.addItem）
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'item.addItem',
        { characterId: uid, resId: resId, num: num }
      );

      if (result.code !== 0) {
        throw new Error(`添加物品失败: ${result.message || '未知错误'}`);
      }

      this.logger.log(`给予玩家物品成功: ${uid}, 物品: ${resId}, 数量: ${num}`);
    } catch (error) {
      this.logger.error('给予玩家物品失败', error);
      throw error;
    }
  }

  /**
   * 给予玩家碎片
   */
  private async addPlayerChip(uid: string, num: number): Promise<XResult<void>> {
    // TODO: 调用Character服务给予玩家碎片
    this.logger.log(`给予玩家碎片: ${uid}, 数量: ${num}`);
  }

  /**
   * 扣除玩家碎片
   */
  private async deductPlayerChip(uid: string, num: number): Promise<XResult<void>> {
    // TODO: 调用Character服务扣除玩家碎片
    this.logger.log(`扣除玩家碎片: ${uid}, 数量: ${num}`);
  }

  /**
   * 扣除玩家金币
   */
  private async deductPlayerGold(uid: string, num: number): Promise<XResult<void>> {
    // TODO: 调用Character服务扣除玩家金币
    this.logger.log(`扣除玩家金币: ${uid}, 数量: ${num}`);
  }
}
