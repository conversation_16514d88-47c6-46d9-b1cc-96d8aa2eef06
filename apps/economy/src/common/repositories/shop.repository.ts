import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, ClientSession } from 'mongoose';
import { Shop, ShopDocument, ShopType, RefreshCycle } from '../schemas/shop.schema';
import { GetShopListDto, GetPurchaseHistoryDto } from '../dto/shop.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 商店数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 商店记录CRUD操作
 * - 商店刷新管理
 * - 购买历史查询
 * - 商店统计分析
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class ShopRepository extends BaseRepository<ShopDocument> {
  constructor(
    @InjectModel(Shop.name) shopModel: Model<ShopDocument>
  ) {
    super(shopModel, 'ShopRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 创建商店记录
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async createShop(shopData: Partial<Shop>): Promise<XResult<ShopDocument>> {
    const shopWithDefaults = {
      ...shopData,
      everyDayReTime: Date.now(),
      weeklyReTime: Date.now(),
      monthlyReTime: Date.now(),
      seasonReTime: Date.now(),
    };

    return this.createOne(shopWithDefaults);
  }

  /**
   * 根据ID查找商店记录
   * 使用BaseRepository的findOne方法优化性能
   */
  async findShopById(shopId: string): Promise<XResult<ShopDocument | null>> {
    return this.findOne({ shopId });
  }

  /**
   * 根据ID查找商店记录（Lean查询优化版本）
   */
  async findShopByIdLean(shopId: string): Promise<XResult<any | null>> {
    return this.findOneLean({ shopId });
  }

  /**
   * 根据角色ID和商店类型查找商店记录
   * 使用BaseRepository的findOne方法优化性能
   */
  async findShopByCharacterAndType(characterId: string, shopType: ShopType): Promise<XResult<ShopDocument | null>> {
    return this.findOne({ characterId, shopType });
  }

  /**
   * 根据角色ID和商店类型查找商店记录（Lean查询优化版本）
   */
  async findShopByCharacterAndTypeLean(characterId: string, shopType: ShopType): Promise<XResult<any | null>> {
    return this.findOneLean({ characterId, shopType });
  }

  /**
   * 获取或创建商店记录
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async getOrCreateShop(characterId: string, serverId: string, shopType: ShopType): Promise<XResult<ShopDocument>> {
    const shopResult = await this.findShopByCharacterAndType(characterId, shopType);

    if (XResultUtils.isSuccess(shopResult) && shopResult.data) {
      return shopResult;
    }

    // 创建新商店记录
    const shopId = this.generateShopId(characterId, shopType);
    const createResult = await this.createShop({
      shopId,
      characterId,
      serverId,
      shopType,
    });

    if (XResultUtils.isSuccess(createResult)) {
      this.logger.log(`创建新商店记录: ${shopId}`);
    }

    return createResult;
  }

  /**
   * 根据角色ID查找商店列表
   * 使用BaseRepository的findMany方法优化性能
   */
  async findShopsByCharacterId(query: GetShopListDto): Promise<XResult<ShopDocument[]>> {
    const filter: FilterQuery<ShopDocument> = { characterId: query.characterId };

    if (query.shopType !== undefined) {
      filter.shopType = query.shopType;
    }

    if (query.hasRecordsOnly) {
      filter.totalPurchases = { $gt: 0 };
    }

    const sortField = query.sortBy || 'lastPurchaseTime';
    const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
    const sort: any = { [sortField]: sortOrder };

    return this.findMany(filter, { sort });
  }

  /**
   * 根据角色ID查找商店列表（Lean查询优化版本）
   */
  async findShopsByCharacterIdLean(query: GetShopListDto): Promise<XResult<any[]>> {
    const filter: FilterQuery<ShopDocument> = { characterId: query.characterId };

    if (query.shopType !== undefined) {
      filter.shopType = query.shopType;
    }

    if (query.hasRecordsOnly) {
      filter.totalPurchases = { $gt: 0 };
    }

    const sortField = query.sortBy || 'lastPurchaseTime';
    const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
    const sort: any = { [sortField]: sortOrder };

    return this.findManyLean(filter, {
      sort,
      select: ['shopId', 'characterId', 'shopType', 'totalPurchases', 'totalSpent', 'lastPurchaseTime']
    });
  }

  /**
   * 更新商店记录
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateShop(
    shopId: string,
    updateData: UpdateQuery<ShopDocument>,
    session?: ClientSession
  ): Promise<XResult<ShopDocument | null>> {
    return this.updateOne({ shopId }, updateData, session);
  }

  /**
   * 删除商店记录
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteShop(shopId: string): Promise<XResult<boolean | null>> {
    return this.deleteOne({ shopId });
  }

  /**
   * 获取需要刷新的商店
   * 使用BaseRepository的findMany方法优化性能
   */
  async findShopsNeedingRefresh(cycle: RefreshCycle): Promise<XResult<ShopDocument[]>> {
    const now = Date.now();
    let timeField: string;
    let intervalMs: number;

    switch (cycle) {
      case RefreshCycle.DAILY:
        timeField = 'everyDayReTime';
        intervalMs = 24 * 60 * 60 * 1000; // 1天
        break;
      case RefreshCycle.WEEKLY:
        timeField = 'weeklyReTime';
        intervalMs = 7 * 24 * 60 * 60 * 1000; // 7天
        break;
      case RefreshCycle.MONTHLY:
        timeField = 'monthlyReTime';
        intervalMs = 30 * 24 * 60 * 60 * 1000; // 30天
        break;
      case RefreshCycle.SEASON:
        timeField = 'seasonReTime';
        intervalMs = 90 * 24 * 60 * 60 * 1000; // 90天
        break;
      default:
        return XResultUtils.error(`不支持的刷新周期: ${cycle}`, 'INVALID_REFRESH_CYCLE');
    }

    const filter: FilterQuery<ShopDocument> = {
      [timeField]: { $lte: now - intervalMs }
    };

    return this.findMany(filter);
  }

  /**
   * 获取需要刷新的商店（Lean查询优化版本）
   */
  async findShopsNeedingRefreshLean(cycle: RefreshCycle): Promise<XResult<any[]>> {
    const now = Date.now();
    let timeField: string;
    let intervalMs: number;

    switch (cycle) {
      case RefreshCycle.DAILY:
        timeField = 'everyDayReTime';
        intervalMs = 24 * 60 * 60 * 1000;
        break;
      case RefreshCycle.WEEKLY:
        timeField = 'weeklyReTime';
        intervalMs = 7 * 24 * 60 * 60 * 1000;
        break;
      case RefreshCycle.MONTHLY:
        timeField = 'monthlyReTime';
        intervalMs = 30 * 24 * 60 * 60 * 1000;
        break;
      case RefreshCycle.SEASON:
        timeField = 'seasonReTime';
        intervalMs = 90 * 24 * 60 * 60 * 1000;
        break;
      default:
        return XResultUtils.error(`不支持的刷新周期: ${cycle}`, 'INVALID_REFRESH_CYCLE');
    }

    const filter: FilterQuery<ShopDocument> = {
      [timeField]: { $lte: now - intervalMs }
    };

    return this.findManyLean(filter, {
      select: ['shopId', 'characterId', 'shopType', timeField]
    });
  }

  /**
   * 批量刷新商店限制
   * 使用BaseRepository的updateMany方法优化性能
   */
  async batchRefreshShops(shopIds: string[], cycle: RefreshCycle): Promise<XResult<any>> {
    const now = Date.now();
    let updateData: any = {};

    switch (cycle) {
      case RefreshCycle.DAILY:
        updateData = {
          everyDayBuy: [],
          everyDayReTime: now,
        };
        break;
      case RefreshCycle.WEEKLY:
        updateData = {
          weeklyBuy: [],
          weeklyReTime: now,
        };
        break;
      case RefreshCycle.MONTHLY:
        updateData = {
          monthlyBuy: [],
          monthlyReTime: now,
        };
        break;
      case RefreshCycle.SEASON:
        updateData = {
          seasonBuy: [],
          seasonReTime: now,
        };
        break;
      default:
        return XResultUtils.error(`不支持的刷新周期: ${cycle}`, 'INVALID_REFRESH_CYCLE');
    }

    return this.updateMany(
      { shopId: { $in: shopIds } },
      { $set: updateData }
    );
  }

  /**
   * 获取购买历史
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getPurchaseHistory(query: GetPurchaseHistoryDto): Promise<XResult<PaginationResult<any>>> {
    const filter: FilterQuery<ShopDocument> = { characterId: query.characterId };

    if (query.shopType !== undefined) {
      filter.shopType = query.shopType;
    }

    const page = query.page || 1;
    const limit = query.limit || 20;
    const skip = (page - 1) * limit;

    // 使用聚合查询获取购买历史
    const pipeline: any[] = [
      { $match: filter },
      { $unwind: '$purchaseHistory' },
    ];

    if (query.goodsId) {
      pipeline.push({ $match: { 'purchaseHistory.goodsId': query.goodsId } });
    }

    if (query.startTime || query.endTime) {
      const timeFilter: any = {};
      if (query.startTime) timeFilter.$gte = query.startTime;
      if (query.endTime) timeFilter.$lte = query.endTime;
      pipeline.push({ $match: { 'purchaseHistory.purchaseTime': timeFilter } });
    }

    pipeline.push(
      { $sort: { 'purchaseHistory.purchaseTime': -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $project: {
          shopId: 1,
          shopType: 1,
          characterId: 1,
          purchase: '$purchaseHistory',
        }
      }
    );

    // 并行执行数据查询和总数统计
    const [dataResult, totalResult] = await Promise.all([
      this.aggregate(pipeline),
      this.aggregate([
        ...pipeline.slice(0, -3), // 移除 sort, skip, limit, project
        { $count: 'total' }
      ])
    ]);

    if (XResultUtils.isFailure(dataResult) || XResultUtils.isFailure(totalResult)) {
      return XResultUtils.error('获取购买历史失败', 'PURCHASE_HISTORY_QUERY_FAILED');
    }

    const data = dataResult.data;
    const total = totalResult.data[0]?.total || 0;
    const pages = Math.ceil(total / limit);

    return XResultUtils.ok({
      data,
      total,
      page,
      limit,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1,
    });
  }

  /**
   * 获取商店统计
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getShopStats(characterId: string, days: number = 30, shopType?: ShopType): Promise<XResult<any>> {
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);
    const filter: FilterQuery<ShopDocument> = {
      characterId,
      lastPurchaseTime: { $gte: startTime }
    };

    if (shopType !== undefined) {
      filter.shopType = shopType;
    }

    const pipeline = [
      { $match: filter },
      {
        $group: {
          _id: null,
          totalShops: { $sum: 1 },
          totalSpent: { $sum: '$totalSpent' },
          totalPurchases: { $sum: '$totalPurchases' },
          avgSpentPerShop: { $avg: '$totalSpent' },
          avgPurchasesPerShop: { $avg: '$totalPurchases' },
          shopTypeDistribution: {
            $push: {
              shopType: '$shopType',
              spent: '$totalSpent',
              purchases: '$totalPurchases'
            }
          }
        }
      }
    ];

    const result = await this.aggregate(pipeline);
    if (XResultUtils.isSuccess(result) && result.data.length > 0) {
      return XResultUtils.ok(result.data[0]);
    }

    // 返回默认统计信息
    return XResultUtils.ok({
      totalShops: 0,
      totalSpent: 0,
      totalPurchases: 0,
      avgSpentPerShop: 0,
      avgPurchasesPerShop: 0,
      shopTypeDistribution: []
    });
  }

  /**
   * 生成商店ID
   */
  private generateShopId(characterId: string, shopType: ShopType): string {
    return `shop_${characterId}_${shopType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加商店特定的验证规则
   */
  protected validateData(data: Partial<Shop>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.shopId) {
        return XResultUtils.error('商店ID不能为空', 'SHOP_ID_REQUIRED');
      }

      if (!data.characterId) {
        return XResultUtils.error('角色ID不能为空', 'CHARACTER_ID_REQUIRED');
      }

      if (!data.shopType) {
        return XResultUtils.error('商店类型不能为空', 'SHOP_TYPE_REQUIRED');
      }
    }

    if (data.totalPurchases !== undefined && data.totalPurchases < 0) {
      return XResultUtils.error('购买次数不能为负数', 'INVALID_PURCHASES');
    }

    if (data.totalSpent !== undefined && data.totalSpent < 0) {
      return XResultUtils.error('消费金额不能为负数', 'INVALID_SPENT');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对商店数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findShopById': 300,               // 商店记录缓存5分钟
      'findShopByIdLean': 180,           // 商店简介缓存3分钟
      'findShopByCharacterAndType': 300, // 角色商店缓存5分钟
      'findShopsByCharacterId': 180,     // 商店列表缓存3分钟
      'getShopStats': 600,               // 统计信息缓存10分钟
      'getPurchaseHistory': 300,         // 购买历史缓存5分钟
      'findShopsNeedingRefresh': 60,     // 刷新查询缓存1分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
