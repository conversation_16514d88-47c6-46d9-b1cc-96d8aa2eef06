/**
 * Exchange模块的Payload DTO定义
 * 
 * 为exchange.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 兑换大厅信息相关 ====================

/**
 * 获取兑换大厅信息Payload DTO
 * @MessagePattern('exchange.getInfo')
 * 基于真实接口结构：{ uid: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetExchangeInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 2. 物品合成相关 ====================

/**
 * 合成物品Payload DTO
 * @MessagePattern('exchange.compound')
 * 基于真实接口结构：{ uid: string; serverId: string; resId: number; num: number; injectedContext?: InjectedContext }
 */
export class CompoundItemPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '物品资源ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '物品资源ID必须是数字' })
  @Min(1, { message: '物品资源ID不能小于1' })
  @Max(999999, { message: '物品资源ID不能大于999999' })
  resId: number;

  @ApiProperty({ description: '合成数量', example: 1 })
  @Expose()
  @IsNumber({}, { message: '合成数量必须是数字' })
  @Min(1, { message: '合成数量不能小于1' })
  @Max(999, { message: '合成数量不能大于999' })
  num: number;
}

// ==================== 3. 物品分解相关 ====================

/**
 * 分解物品Payload DTO
 * @MessagePattern('exchange.decompose')
 * 基于真实接口结构：{ uid: string; serverId: string; resId: number; num: number; injectedContext?: InjectedContext }
 */
export class DecomposeItemPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '物品资源ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '物品资源ID必须是数字' })
  @Min(1, { message: '物品资源ID不能小于1' })
  @Max(999999, { message: '物品资源ID不能大于999999' })
  resId: number;

  @ApiProperty({ description: '分解数量', example: 1 })
  @Expose()
  @IsNumber({}, { message: '分解数量必须是数字' })
  @Min(1, { message: '分解数量不能小于1' })
  @Max(999, { message: '分解数量不能大于999' })
  num: number;
}

// ==================== 4. 兑换大厅刷新相关 ====================

/**
 * 刷新兑换大厅Payload DTO
 * @MessagePattern('exchange.refresh')
 * 基于真实接口结构：{ uid: string; serverId: string; type: number; teamId?: number; injectedContext?: InjectedContext }
 */
export class RefreshExchangeHallPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '刷新类型', example: 1 })
  @Expose()
  @IsNumber({}, { message: '刷新类型必须是数字' })
  @Min(1, { message: '刷新类型不能小于1' })
  @Max(10, { message: '刷新类型不能大于10' })
  type: number;

  @ApiPropertyOptional({ description: '队伍ID（可选）', example: 101 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '队伍ID必须是数字' })
  @Min(1, { message: '队伍ID不能小于1' })
  @Max(999999, { message: '队伍ID不能大于999999' })
  teamId?: number;
}

// ==================== 5. 物品兑换相关 ====================

/**
 * 兑换物品Payload DTO
 * @MessagePattern('exchange.exchangeItem')
 * 基于真实接口结构：{ uid: string; serverId: string; id: number; injectedContext?: InjectedContext }
 */
export class ExchangeItemPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '兑换ID', example: 2001 })
  @Expose()
  @IsNumber({}, { message: '兑换ID必须是数字' })
  @Min(1, { message: '兑换ID不能小于1' })
  @Max(999999, { message: '兑换ID不能大于999999' })
  id: number;
}

// ==================== 6. 统计信息相关 ====================

/**
 * 获取兑换统计信息Payload DTO
 * @MessagePattern('exchange.getStats')
 * 基于真实接口结构：{ adminToken?: string; injectedContext?: InjectedContext }
 */
export class GetExchangeStatsPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '管理员令牌', example: 'admin_token_123' })
  @Expose()
  @IsOptional()
  @IsString({ message: '管理员令牌必须是字符串' })
  adminToken?: string;
}
