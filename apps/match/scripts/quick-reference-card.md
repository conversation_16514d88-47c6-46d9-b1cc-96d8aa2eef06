# Match服务测试快速参考卡

## 🚨 关键提醒

### 必须使用高级模型
```
⚠️ 重要：本项目复杂问题必须使用 Claude 4 高级模型
低版本模型无法有效处理微服务架构的复杂问题
```

## 🔧 常见问题快速修复

### 1. Mongoose文档对象处理
```typescript
// ❌ 错误
const result = { ...mongooseDoc };

// ✅ 正确  
const obj = (mongooseDoc as any)?.toObject ? 
  (mongooseDoc as any).toObject() : mongooseDoc;
const result = { ...obj };
```

### 2. 微服务响应解析
```javascript
// ❌ 错误
if (response.code === 0) { ... }

// ✅ 正确
const code = response?.payload?.data?.code;
if (code === 0) { ... }
```

### 3. 依赖服务处理
```typescript
// ❌ 硬依赖
await this.otherService.call(...);

// ✅ 软依赖
// TODO: 服务完成后启用
// await this.otherService.call(...);
this.logger.log('TODO: 需要调用其他服务');
```

## 📊 调试日志模板

### 数据流跟踪
```typescript
this.logger.log(`=== ${功能名称} 开始 ===`);
this.logger.log(`输入数据类型: ${typeof data}`);
this.logger.log(`输入数据.关键字段类型: ${typeof data.field}`);
this.logger.log(`输入数据.关键字段长度: ${data.field?.length || 0}`);
this.logger.log(`输入数据完整内容: ${JSON.stringify(data, null, 2)}`);
// ... 处理逻辑 ...
this.logger.log(`输出数据类型: ${typeof result}`);
this.logger.log(`=== ${功能名称} 结束 ===`);
```

## ✅ 测试开发检查清单

### 开发前
- [ ] 使用Claude 4高级模型
- [ ] 确认依赖服务已启动
- [ ] 准备完整测试数据
- [ ] 确认网络和Redis连接

### 开发中
- [ ] 添加详细数据流日志
- [ ] 正确处理Mongoose文档
- [ ] 使用正确响应格式解析
- [ ] 添加错误处理机制
- [ ] 标记依赖服务调用

### 测试后
- [ ] 验证微服务调用正确
- [ ] 验证数据转换正确
- [ ] 验证数据传递完整
- [ ] 验证业务逻辑正确
- [ ] 验证错误处理机制

## 🎯 成功案例参考

### 自动布阵功能
- **问题**: 球员数据在战斗时丢失
- **原因**: Mongoose文档对象处理错误
- **解决**: 使用toObject()方法转换

### 商业赛匹配功能  
- **问题**: 测试脚本显示"未知错误"
- **原因**: 响应格式解析错误
- **解决**: 修复嵌套数据结构解析

## 🛠️ 常用命令

### 服务启动
```bash
npm run start:match      # Match服务
npm run start:character  # Character服务  
npm run start:hero      # Hero服务
npm run start:economy   # Economy服务
```

### 测试执行
```bash
# 商业赛测试
node apps/match/scripts/test-match-system.js business char_xxx

# 其他模块测试
node apps/match/scripts/test-match-system.js league char_xxx
node apps/match/scripts/test-match-system.js tournament char_xxx
```

## 📞 问题升级

如果遇到复杂问题：
1. 确认使用Claude 4高级模型
2. 添加详细数据流日志
3. 分层验证数据传递
4. 参考成功案例解决方案
5. 更新本参考文档
