import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 锦标赛战斗记录
 * 基于old项目worldCup.js、middleEastCup.js等的战斗记录结构
 */
@Schema({ _id: false })
export class TournamentBattleRecord {
  @Prop({ required: true })
  roundNum: number;                      // 轮次编号

  @Prop({ required: true })
  enemyTeamId: number;                   // 对手队伍ID

  @Prop({ default: 0 })
  selfScore: number;                     // 己方比分

  @Prop({ default: 0 })
  enemyScore: number;                    // 敌方比分

  @Prop({ default: 0 })
  result: number;                        // 战斗结果 (0失败 1胜利)

  @Prop({ default: Date.now })
  battleTime: Date;                      // 战斗时间

  @Prop({ type: Object, default: {} })
  rewardInfo: any;                       // 奖励信息
}

/**
 * 锦标赛奖励记录
 * 基于old项目的奖励处理逻辑
 */
@Schema({ _id: false })
export class TournamentReward {
  @Prop({ default: 0 })
  bonus: number;                         // 奖励状态 (0不可领取 1可领取 4已领取)

  @Prop({ default: 0 })
  added: number;                         // 额外奖励状态

  @Prop({ type: [Object], default: [] })
  itemList: any[];                       // 物品奖励列表

  @Prop({ default: 0 })
  cash: number;                          // 现金奖励

  @Prop({ default: 0 })
  gold: number;                          // 球币奖励
}

/**
 * 世界杯锦标赛数据
 * 基于old项目worldCup.js的数据结构
 */
@Schema({ _id: false })
export class WorldCupData {
  @Prop({ default: Date.now })
  reTime: Date;                          // 刷新时间

  @Prop({ default: 0 })
  buyCount: number;                      // 购买次数

  @Prop({ default: 0 })
  joinCount: number;                     // 可进入次数

  @Prop({ default: 0 })
  isJoin: number;                        // 是否已经在比赛了 (0没有 1有)

  @Prop({ type: Object, default: {} })
  groupMatch: any;                       // 记录小组赛

  @Prop({ default: 1 })
  groupNum: number;                      // 小组赛轮次

  @Prop({ default: 0 })
  worldCupId: number;                    // 记录副本ID

  @Prop({ default: 0 })
  isGetReward: number;                   // 是否领取奖励

  @Prop({ default: 0 })
  isOut: number;                         // 是否出局

  @Prop({ default: 0 })
  isRecord: number;                      // 是否记录过

  @Prop({ type: Object, default: {} })
  eliminateMatch: any;                   // 记录淘汰赛
}

/**
 * 区域杯赛数据（中东杯、海湾杯、MLS等）
 * 基于old项目middleEastCup.js、gulfCup.js、MLS.js的数据结构
 */
@Schema({ _id: false })
export class RegionalCupData {
  @Prop({ default: 0 })
  contestNum: number;                    // 挑战次数

  @Prop({ default: 0 })
  sponsorId: number;                     // 赞助商ID

  @Prop({ default: 0 })
  isBegin: number;                       // 是否已经在挑战 (0没有 1有)

  @Prop({ type: [Number], default: [] })
  rivalTeamList: number[];               // 对手列表

  @Prop({ type: [Number], default: [] })
  teamList: number[];                    // 选择的队伍

  @Prop({ type: [TournamentReward], default: [] })
  awardList: TournamentReward[];         // 奖励列表

  @Prop({ default: Date.now })
  flashTime: Date;                       // 刷新时间

  @Prop({ default: 0 })
  contestNumMax: number;                 // 挑战次数上限

  @Prop({ default: 0 })
  luckyValue: number;                    // 幸运值

  @Prop({ default: 0 })
  luckyMax: number;                      // 幸运值上限

  @Prop({ default: 0 })
  conditionId: number;                   // 奖励条件ID

  @Prop({ default: 0 })
  goal: number;                          // 总进球数

  @Prop({ default: 0 })
  fumble: number;                        // 总失球数

  @Prop({ default: 0 })
  diff: number;                          // 总净胜球数

  @Prop({ default: 0 })
  win: number;                           // 胜利次数

  @Prop({ type: [TournamentBattleRecord], default: [] })
  combatList: TournamentBattleRecord[];  // 战斗列表

  @Prop({ type: [Object], default: [] })
  rewardList: any[];                     // 保存发送邮件奖励列表
}

/**
 * 锦标赛主文档
 * 基于old项目中各种锦标赛的数据结构
 */
@Schema({
  collection: 'tournaments',
  timestamps: true,
  versionKey: false,
})
export class Tournament extends Document {
  @Prop({ required: true, unique: true })
  uid: string;                           // 玩家UID

  @Prop({ type: WorldCupData })
  worldCup: WorldCupData;                // 世界杯数据

  @Prop({ type: RegionalCupData })
  middleEastCup: RegionalCupData;        // 中东杯数据

  @Prop({ type: RegionalCupData })
  gulfCup: RegionalCupData;              // 海湾杯数据

  @Prop({ type: RegionalCupData })
  mls: RegionalCupData;                  // MLS数据

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const TournamentSchema = SchemaFactory.createForClass(Tournament);

// 定义方法接口 - 基于TournamentService的真实业务逻辑
export interface TournamentMethods {
  // 锦标赛管理 - 基于TournamentService
  getTournamentData(tournamentType: string): WorldCupData | RegionalCupData | null;
  joinTournament(tournamentType: string): boolean;
  leaveTournament(tournamentType: string): boolean;
  isJoinedTournament(tournamentType: string): boolean;

  // 战斗记录管理 - 基于TournamentService
  addBattleRecord(tournamentType: string, record: TournamentBattleRecord): boolean;
  getBattleRecords(tournamentType: string): TournamentBattleRecord[];
  getLastBattleRecord(tournamentType: string): TournamentBattleRecord | null;
  updateBattleStats(tournamentType: string, isWin: boolean, goals: number, conceded: number): void;

  // 挑战次数管理 - 基于TournamentService
  canChallenge(tournamentType: string): boolean;
  consumeChallenge(tournamentType: string): boolean;
  addChallengeTimes(tournamentType: string, times: number): void;
  resetDailyChallenges(tournamentType: string): void;

  // 幸运值管理 - 基于TournamentService
  addLuckyValue(tournamentType: string, value: number): boolean;
  canUseLuckyValue(tournamentType: string, requiredValue: number): boolean;
  consumeLuckyValue(tournamentType: string, value: number): boolean;

  // 奖励管理 - 基于TournamentService
  addReward(tournamentType: string, reward: any): void;
  getUnclaimedRewards(tournamentType: string): any[];
  claimReward(tournamentType: string, rewardId: string): boolean;

  // 统计分析 - 基于TournamentService
  getTournamentStats(tournamentType: string): any;
  getOverallStats(): any;
  getWinRate(tournamentType: string): number;

  // 数据转换 - 基于TournamentService
  toClientTournamentData(tournamentType?: string): any;
  getTournamentSummary(): any;
}

// 定义Document类型
export type TournamentDocument = Tournament & Document & TournamentMethods;

// 创建索引
TournamentSchema.index({ uid: 1 });
TournamentSchema.index({ 'worldCup.isJoin': 1 });
TournamentSchema.index({ 'worldCup.worldCupId': 1 });
TournamentSchema.index({ lastUpdateTime: 1 });
