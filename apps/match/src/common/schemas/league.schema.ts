import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 联赛副本数据结构
 * 基于old项目leagueCopy.js的数据模型
 */
@Schema({ _id: false })
export class LeagueCopyData {
  @Prop({ required: true })
  teamCopyId: number;                    // 球队副本ID 配置的球队副本ID

  @Prop({ default: 0 })
  process: number;                       // 进度 0为没有挑战过 1为一星 2为二星 3为三星

  @Prop({ default: 0 })
  takeCopyRewardProcess: number;         // 领取副本奖励进度

  @Prop({ default: 0 })
  isFinished: number;                    // 是否完成三星挑战(服务器端)

  @Prop({ default: 0 })
  unlockState: number;                   // 解锁状态 0为未解锁 1为解锁 默认为未解锁
}

/**
 * 单个联赛数据结构
 * 基于old项目leagueCopy.js的oneLeagueObj结构
 */
@Schema({ _id: false })
export class OneLeague {
  @Prop({ required: true })
  uid: number;                           // 联赛ID，不需要自己生成,直接使用配置的ID

  @Prop({ default: 0 })
  isTakeLeagueReward: number;            // 是否领取了奖励,通过邮件自动发放 0为未领取，未发放 1为已领取

  @Prop({ default: 0 })
  isAllCopyPassed: number;               // 当前联赛是否全部通关  0为未全部通过 1为全部通过

  @Prop({ default: 0 })
  curPassCount: number;                  // 当前通关副本总星数(计算配置表来计算)

  @Prop({ default: 0 })
  count: number;                         // 联赛总星数，考虑到后期策划可能增加或者减少副本数量，此字段在停服或者起服加载需重新计算

  @Prop({ type: [LeagueCopyData], default: [] })
  copyData: LeagueCopyData[];            // 副本数据
}

/**
 * 联赛副本主文档
 * 基于old项目leagueCopy.js的主要数据结构
 */
@Schema({
  collection: 'league_copies',
  timestamps: true,
  versionKey: false,
})
export class League extends Document {
  @Prop({ required: true, unique: true })
  uid: string;                           // 玩家UID

  @Prop({ type: [OneLeague], default: [] })
  allLeagueCopys: OneLeague[];           // 所有联赛数据 leagueID => oneLeaguObj

  @Prop({ default: 0 })
  buyTimes: number;                      // 购买联赛次数（基于old项目的购买次数逻辑）

  @Prop({ default: 0 })
  fixData: number;                       // 修复数据标记

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const LeagueSchema = SchemaFactory.createForClass(League);

// 定义方法接口 - 基于LeagueService的真实业务逻辑
export interface LeagueMethods {
  // 联赛管理 - 基于LeagueService
  getLeague(leagueId: number): OneLeague | null;
  addLeague(league: OneLeague): void;
  updateLeague(leagueId: number, updates: Partial<OneLeague>): boolean;

  // 副本管理 - 基于LeagueService
  getCopyData(leagueId: number, copyId: number): LeagueCopyData | null;
  updateCopyProgress(leagueId: number, copyId: number, stars: number): boolean;
  unlockNextCopy(leagueId: number, copyId: number): boolean;

  // 奖励管理 - 基于LeagueService
  canTakeLeagueReward(leagueId: number): boolean;
  takeLeagueReward(leagueId: number): boolean;
  canTakeCopyReward(leagueId: number, copyId: number): boolean;
  takeCopyReward(leagueId: number, copyId: number): boolean;

  // 进度统计 - 基于LeagueService
  getTotalStars(leagueId: number): number;
  getCompletedCopies(leagueId: number): number;
  isLeagueCompleted(leagueId: number): boolean;
  getOverallProgress(): { totalStars: number; completedLeagues: number };

  // 解锁逻辑 - 基于LeagueService
  getUnlockedCopies(leagueId: number): LeagueCopyData[];
  getNextUnlockableCopy(leagueId: number): LeagueCopyData | null;

  // 数据转换 - 基于LeagueService
  toClientLeagueData(type?: string): any[];
  getLeagueStats(): any;
}

// 定义Document类型
export type LeagueDocument = League & Document & LeagueMethods;

// 创建索引
LeagueSchema.index({ uid: 1 });
LeagueSchema.index({ 'allLeagueCopys.Uid': 1 });
LeagueSchema.index({ lastUpdateTime: 1 });

// ==================== 实例方法实现 ====================

/**
 * 获取指定联赛
 * 基于LeagueService: 联赛查询逻辑
 */
LeagueSchema.methods.getLeague = function(leagueId: number): OneLeague | null {
  return this.allLeagueCopys.find((league: OneLeague) => league.uid === leagueId) || null;
};

/**
 * 添加联赛
 * 基于LeagueService: initLeagueData方法逻辑
 */
LeagueSchema.methods.addLeague = function(league: OneLeague): void {
  // 检查是否已存在
  const existingIndex = this.allLeagueCopys.findIndex((l: OneLeague) => l.uid === league.uid);
  if (existingIndex !== -1) {
    this.allLeagueCopys[existingIndex] = league;
  } else {
    this.allLeagueCopys.push(league);
  }
  this.lastUpdateTime = new Date();
};

/**
 * 更新联赛信息
 * 基于LeagueService: 联赛更新逻辑
 */
LeagueSchema.methods.updateLeague = function(leagueId: number, updates: Partial<OneLeague>): boolean {
  const league = this.getLeague(leagueId);
  if (!league) return false;

  Object.assign(league, updates);
  this.lastUpdateTime = new Date();
  return true;
};

/**
 * 获取副本数据
 * 基于LeagueService: 副本查询逻辑
 */
LeagueSchema.methods.getCopyData = function(leagueId: number, copyId: number): LeagueCopyData | null {
  const league = this.getLeague(leagueId);
  if (!league) return null;

  return league.copyData.find((copy: LeagueCopyData) => copy.teamCopyId === copyId) || null;
};

/**
 * 更新副本进度
 * 基于LeagueService: pveBattle方法中的进度更新逻辑
 */
LeagueSchema.methods.updateCopyProgress = function(leagueId: number, copyId: number, stars: number): boolean {
  const copy = this.getCopyData(leagueId, copyId);
  if (!copy) return false;

  // 更新进度（取最高星级）
  copy.process = Math.max(copy.process, stars);

  // 如果达到3星，标记为完成
  if (stars >= 3) {
    copy.isFinished = 1;
  }

  // 更新联赛总进度
  const league = this.getLeague(leagueId);
  if (league) {
    league.curPassCount = this.getTotalStars(leagueId);

    // 检查是否全部通关
    const allCompleted = league.copyData.every((c: LeagueCopyData) => c.isFinished === 1);
    if (allCompleted) {
      league.isAllCopyPassed = 1;
    }
  }

  this.lastUpdateTime = new Date();
  return true;
};

/**
 * 解锁下一个副本
 * 基于LeagueService: 副本解锁逻辑
 */
LeagueSchema.methods.unlockNextCopy = function(leagueId: number, copyId: number): boolean {
  const league = this.getLeague(leagueId);
  if (!league) return false;

  const currentCopyIndex = league.copyData.findIndex((copy: LeagueCopyData) => copy.teamCopyId === copyId);
  if (currentCopyIndex === -1) return false;

  // 解锁下一个副本
  if (currentCopyIndex + 1 < league.copyData.length) {
    league.copyData[currentCopyIndex + 1].unlockState = 1;
    this.lastUpdateTime = new Date();
    return true;
  }

  // 如果是最后一个副本，尝试解锁下一个联赛的第一个副本
  const currentLeagueIndex = this.allLeagueCopys.findIndex((l: OneLeague) => l.uid === leagueId);
  if (currentLeagueIndex !== -1 && currentLeagueIndex + 1 < this.allLeagueCopys.length) {
    const nextLeague = this.allLeagueCopys[currentLeagueIndex + 1];
    if (nextLeague.copyData.length > 0) {
      nextLeague.copyData[0].unlockState = 1;
      this.lastUpdateTime = new Date();
      return true;
    }
  }

  return false;
};

/**
 * 检查是否可以领取联赛奖励
 * 基于LeagueService: takeLeagueReward方法逻辑
 */
LeagueSchema.methods.canTakeLeagueReward = function(leagueId: number): boolean {
  const league = this.getLeague(leagueId);
  if (!league) return false;

  // 必须全部通关且未领取奖励
  return league.isAllCopyPassed === 1 && league.isTakeLeagueReward === 0;
};

/**
 * 领取联赛奖励
 * 基于LeagueService: takeLeagueReward方法逻辑
 */
LeagueSchema.methods.takeLeagueReward = function(leagueId: number): boolean {
  if (!this.canTakeLeagueReward(leagueId)) return false;

  const league = this.getLeague(leagueId);
  if (league) {
    league.isTakeLeagueReward = 1;
    this.lastUpdateTime = new Date();
    return true;
  }

  return false;
};

/**
 * 检查是否可以领取副本奖励
 * 基于LeagueService: 副本奖励逻辑
 */
LeagueSchema.methods.canTakeCopyReward = function(leagueId: number, copyId: number): boolean {
  const copy = this.getCopyData(leagueId, copyId);
  if (!copy) return false;

  // 必须完成且未领取奖励
  return copy.process > 0 && copy.takeCopyRewardProcess === 0;
};

/**
 * 领取副本奖励
 * 基于LeagueService: 副本奖励领取逻辑
 */
LeagueSchema.methods.takeCopyReward = function(leagueId: number, copyId: number): boolean {
  if (!this.canTakeCopyReward(leagueId, copyId)) return false;

  const copy = this.getCopyData(leagueId, copyId);
  if (copy) {
    copy.takeCopyRewardProcess = 1;
    this.lastUpdateTime = new Date();
    return true;
  }

  return false;
};

/**
 * 获取联赛总星数
 * 基于LeagueService: 星数统计逻辑
 */
LeagueSchema.methods.getTotalStars = function(leagueId: number): number {
  const league = this.getLeague(leagueId);
  if (!league) return 0;

  return league.copyData.reduce((total: number, copy: LeagueCopyData) => total + copy.process, 0);
};

/**
 * 获取已完成副本数量
 * 基于LeagueService: 完成度统计逻辑
 */
LeagueSchema.methods.getCompletedCopies = function(leagueId: number): number {
  const league = this.getLeague(leagueId);
  if (!league) return 0;

  return league.copyData.filter((copy: LeagueCopyData) => copy.isFinished === 1).length;
};

/**
 * 检查联赛是否完成
 * 基于LeagueService: 完成状态检查逻辑
 */
LeagueSchema.methods.isLeagueCompleted = function(leagueId: number): boolean {
  const league = this.getLeague(leagueId);
  if (!league) return false;

  return league.isAllCopyPassed === 1;
};

/**
 * 获取总体进度
 * 基于LeagueService: 总体统计逻辑
 */
LeagueSchema.methods.getOverallProgress = function(): { totalStars: number; completedLeagues: number } {
  let totalStars = 0;
  let completedLeagues = 0;

  for (const league of this.allLeagueCopys) {
    totalStars += this.getTotalStars(league.uid);
    if (league.isAllCopyPassed === 1) {
      completedLeagues++;
    }
  }

  return { totalStars, completedLeagues };
};

/**
 * 获取已解锁的副本
 * 基于LeagueService: 解锁状态查询逻辑
 */
LeagueSchema.methods.getUnlockedCopies = function(leagueId: number): LeagueCopyData[] {
  const league = this.getLeague(leagueId);
  if (!league) return [];

  return league.copyData.filter((copy: LeagueCopyData) => copy.unlockState === 1);
};

/**
 * 获取下一个可解锁的副本
 * 基于LeagueService: 解锁逻辑
 */
LeagueSchema.methods.getNextUnlockableCopy = function(leagueId: number): LeagueCopyData | null {
  const league = this.getLeague(leagueId);
  if (!league) return null;

  return league.copyData.find((copy: LeagueCopyData) => copy.unlockState === 0) || null;
};

/**
 * 转换为客户端联赛数据
 * 基于LeagueService: getLeagueCopyData方法逻辑
 */
LeagueSchema.methods.toClientLeagueData = function(type?: string): any[] {
  // 根据类型过滤联赛（如果指定了类型）
  let leagues = this.allLeagueCopys;

  if (type) {
    // 这里可以根据type参数过滤特定类型的联赛
    // 基于LeagueService中的类型过滤逻辑
    const typeMap: Record<string, { min: number; max: number }> = {
      'domestic': { min: 1, max: 10 },
      'international': { min: 11, max: 20 },
      'cup': { min: 21, max: 30 }
    };

    if (typeMap[type]) {
      const { min, max } = typeMap[type];
      leagues = leagues.filter((league: OneLeague) => league.uid >= min && league.uid <= max);
    }
  }

  return leagues.map((league: OneLeague) => ({
    uid: league.uid,
    isTakeLeagueReward: league.isTakeLeagueReward,
    isAllCopyPassed: league.isAllCopyPassed,
    curPassCount: league.curPassCount,
    count: league.count,
    totalStars: this.getTotalStars(league.uid),
    completedCopies: this.getCompletedCopies(league.uid),
    unlockedCopies: this.getUnlockedCopies(league.uid).length,
    copyData: league.copyData.map((copy: LeagueCopyData) => ({
      teamCopyId: copy.teamCopyId,
      process: copy.process,
      takeCopyRewardProcess: copy.takeCopyRewardProcess,
      isFinished: copy.isFinished,
      unlockState: copy.unlockState,
      canTakeReward: this.canTakeCopyReward(league.uid, copy.teamCopyId)
    }))
  }));
};

/**
 * 获取联赛统计数据
 * 基于LeagueService: 统计分析需求
 */
LeagueSchema.methods.getLeagueStats = function(): any {
  const overallProgress = this.getOverallProgress();

  // 计算各种统计数据
  let totalCopies = 0;
  let completedCopies = 0;
  let unlockedCopies = 0;
  let availableRewards = 0;

  for (const league of this.allLeagueCopys) {
    totalCopies += league.copyData.length;
    completedCopies += this.getCompletedCopies(league.uid);
    unlockedCopies += this.getUnlockedCopies(league.uid).length;

    // 统计可领取的奖励
    if (this.canTakeLeagueReward(league.uid)) {
      availableRewards++;
    }

    for (const copy of league.copyData) {
      if (this.canTakeCopyReward(league.uid, copy.teamCopyId)) {
        availableRewards++;
      }
    }
  }

  return {
    totalLeagues: this.allLeagueCopys.length,
    completedLeagues: overallProgress.completedLeagues,
    totalCopies,
    completedCopies,
    unlockedCopies,
    totalStars: overallProgress.totalStars,
    maxPossibleStars: totalCopies * 3,
    completionRate: totalCopies > 0 ? Math.round((completedCopies / totalCopies) * 100) : 0,
    starRate: totalCopies > 0 ? Math.round((overallProgress.totalStars / (totalCopies * 3)) * 100) : 0,
    availableRewards,
    buyTimes: this.buyTimes,
    lastUpdateTime: this.lastUpdateTime
  };
};
