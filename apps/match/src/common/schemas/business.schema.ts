import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 商业赛匹配记录
 * 基于old项目businessMatch.js的matchRecord结构
 */
@Schema({ _id: false })
export class BusinessMatchRecord {
  @Prop({ required: true })
  characterId: string;                      // 玩家ID

  @Prop({ required: true })
  roomUid: string;                       // 房间UID

  @Prop({ required: true })
  teamA: string;                         // A队玩家ID

  @Prop({ required: true })
  teamB: string;                         // B队玩家ID

  @Prop({ default: 0 })
  result: number;                        // 比赛结果 (0平局 1胜利 2失败)

  @Prop({ default: 0 })
  teamAScore: number;                    // A队比分

  @Prop({ default: 0 })
  teamBScore: number;                    // B队比分

  @Prop({ default: Date.now })
  beginTime: Date;                       // 比赛开始时间

  @Prop({ default: 0 })
  teamARank: number;                     // A队排名

  @Prop({ default: 0 })
  teamBRank: number;                     // B队排名

  @Prop({ default: 0 })
  fansChangeNum: number;                 // 球迷变化数量

  @Prop({ default: 0 })
  cash: number;                          // 获得的现金奖励
}

/**
 * 商业赛对手信息
 * 基于old项目的敌方信息结构
 */
@Schema({ _id: false })
export class BusinessRivalInfo {
  @Prop({ required: true })
  characterId: string;                     // 对手玩家ID

  @Prop({ default: '' })
  name: string;                          // 对手名称

  @Prop({ default: 0 })
  faceIcon: number;                      // 头像图标

  @Prop({ default: 0 })
  actualStrength: number;                // 实际战力

  @Prop({ default: 0 })
  ballFanCount: number;                  // 球迷数量

  @Prop({ default: 0 })
  fanRank: number;                       // 球迷排名

  @Prop({ default: false })
  isGroundOpen: boolean;                 // 球场是否开放

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

/**
 * 商业赛战斗次数信息
 * 基于old项目的fightTimesInfo结构
 */
@Schema({ _id: false })
export class BusinessFightTimes {
  @Prop({ default: 0 })
  totalTimes: number;                    // 总次数

  @Prop({ default: 0 })
  leftTimes: number;                     // 剩余次数

  @Prop({ default: 0 })
  usedTimes: number;                     // 已使用次数

  @Prop({ default: Date.now })
  lastResetTime: Date;                   // 最后重置时间
}

/**
 * 商业赛奖励信息
 * 基于old项目的businessRewardInfo结构
 */
@Schema({ _id: false })
export class BusinessRewardInfo {
  @Prop({ default: 0 })
  totalCash: number;                     // 总现金

  @Prop({ default: 0 })
  winCash: number;                       // 胜利现金

  @Prop({ default: 0 })
  loseCash: number;                      // 失败现金

  @Prop({ default: 0 })
  drawCash: number;                      // 平局现金

  @Prop({ default: 0 })
  matchWinRatio: number;                 // 胜利比例

  @Prop({ default: 0 })
  matchLoseRatio: number;                // 失败比例

  @Prop({ default: 0 })
  matchDrawRatio: number;                // 平局比例

  @Prop({ default: 0 })
  enemyFansCount: number;                // 敌方球迷数量

  @Prop({ default: 0 })
  myFansCount: number;                   // 我方球迷数量

  @Prop({ default: 0 })
  fansQ: number;                         // 球迷质量系数
}

/**
 * 商业赛主文档
 * 基于old项目businessMatch.js的主要数据结构
 */
@Schema({
  collection: 'business_matches',
  timestamps: true,
  versionKey: false,
})
export class BusinessMatch extends Document {
  @Prop({ required: true, unique: true })
  uid: string;                           // 玩家UID

  @Prop({ default: 0 })
  searchCost: number;                    // 搜索费用

  @Prop({ type: [BusinessMatchRecord], default: [] })
  matchRecordList: BusinessMatchRecord[]; // 匹配记录列表（最多30条）

  @Prop({ type: [BusinessRivalInfo], default: [] })
  rivalList: BusinessRivalInfo[];        // 对手列表

  @Prop({ type: BusinessFightTimes })
  fightTimes: BusinessFightTimes;        // 战斗次数信息

  @Prop({ default: 30 })
  maxRecordNum: number;                  // 最大记录数量

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const BusinessMatchSchema = SchemaFactory.createForClass(BusinessMatch);

// 定义方法接口 - 基于BusinessService的真实业务逻辑
export interface BusinessMatchMethods {
  // 战斗次数管理 - 基于BusinessService
  canFight(): boolean;
  consumeFightTime(): boolean;
  addFightTimes(times: number): void;
  resetDailyFightTimes(): void;
  getFightTimesInfo(): any;

  // 匹配记录管理 - 基于BusinessService
  addMatchRecord(record: BusinessMatchRecord): void;
  getLastMatchRecords(limit: number): BusinessMatchRecord[];
  getMatchRecordByRoomUid(roomUid: string): BusinessMatchRecord | null;
  removeOldRecords(): void;

  // 对手管理 - 基于BusinessService
  addRival(rival: BusinessRivalInfo): void;
  removeRival(characterId: string): boolean;
  getRival(characterId: string): BusinessRivalInfo | null;
  updateRivalInfo(characterId: string, updates: Partial<BusinessRivalInfo>): boolean;

  // 统计分析 - 基于BusinessService
  getWinRate(): number;
  getTotalMatches(): number;
  getWinCount(): number;
  getLossCount(): number;
  getDrawCount(): number;
  getTotalCashEarned(): number;

  // 数据验证 - 基于BusinessService
  isSearchCostValid(): boolean;
  needsDailyReset(): boolean;

  // 数据转换 - 基于BusinessService
  toClientBusinessInfo(): any;
  getBusinessStats(): any;
}

// 定义Document类型
export type BusinessMatchDocument = BusinessMatch & Document & BusinessMatchMethods;

// 创建索引
BusinessMatchSchema.index({ uid: 1 });
BusinessMatchSchema.index({ 'matchRecordList.roomUid': 1 });
BusinessMatchSchema.index({ 'matchRecordList.beginTime': -1 });
BusinessMatchSchema.index({ lastUpdateTime: 1 });
