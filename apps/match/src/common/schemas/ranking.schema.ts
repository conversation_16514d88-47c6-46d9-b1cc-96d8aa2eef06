import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 排名条目数据
 * 基于old项目中各种排名功能的数据结构
 */
@Schema({ _id: false })
export class RankingEntry {
  @Prop({ required: true })
  characterId: string;                      // 玩家ID

  @Prop({ default: '' })
  characterName: string;                    // 玩家名称

  @Prop({ default: 0 })
  faceIcon: number;                      // 头像图标

  @Prop({ default: 0 })
  rank: number;                          // 排名

  @Prop({ default: 0 })
  score: number;                         // 分数/数值

  @Prop({ default: 0 })
  actualStrength: number;                // 实际战力

  @Prop({ default: 0 })
  ballFanCount: number;                  // 球迷数量

  @Prop({ default: 0 })
  level: number;                         // 等级

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间

  @Prop({ type: Object, default: {} })
  extraData: any;                        // 额外数据
}

/**
 * 排名奖励记录
 * 基于old项目的排名奖励发放逻辑
 */
@Schema({ _id: false })
export class RankingReward {
  @Prop({ required: true })
  rankType: string;                      // 排名类型

  @Prop({ default: 0 })
  rank: number;                          // 获得的排名

  @Prop({ default: 0 })
  season: number;                        // 赛季

  @Prop({ type: [Object], default: [] })
  rewards: any[];                        // 奖励列表

  @Prop({ default: 0 })
  status: number;                        // 状态 (0未领取 1已领取)

  @Prop({ default: Date.now })
  rewardTime: Date;                      // 奖励时间
}

/**
 * 全球排名数据
 * 基于old项目中各种全球排名的实现
 */
@Schema({
  collection: 'global_rankings',
  timestamps: true,
  versionKey: false,
})
export class GlobalRanking extends Document {
  @Prop({ required: true })
  rankType: string;                      // 排名类型 (fans/strength/level等)

  @Prop({ default: 0 })
  season: number;                        // 赛季

  @Prop({ type: [RankingEntry], default: [] })
  rankings: RankingEntry[];              // 排名列表

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间

  @Prop({ default: Date.now })
  nextUpdateTime: Date;                  // 下次更新时间
}

/**
 * 玩家排名数据
 * 基于old项目中玩家个人排名记录
 */
@Schema({
  collection: 'character_rankings',
  timestamps: true,
  versionKey: false,
})
export class CharacterRanking extends Document {
  @Prop({ required: true, unique: true })
  uid: string;                           // 玩家UID

  @Prop({ type: [RankingReward], default: [] })
  rewardHistory: RankingReward[];        // 奖励历史

  @Prop({ type: Object, default: {} })
  currentRanks: any;                     // 当前各种排名 {fans: 100, strength: 50}

  @Prop({ type: Object, default: {} })
  bestRanks: any;                        // 历史最佳排名

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const GlobalRankingSchema = SchemaFactory.createForClass(GlobalRanking);
export const CharacterRankingSchema = SchemaFactory.createForClass(CharacterRanking);

// 定义方法接口 - 基于RankingService的真实业务逻辑
export interface CharacterRankingMethods {
  // 排名管理 - 基于RankingService
  updateCurrentRank(rankType: string, rank: number, score: number): void;
  getCurrentRank(rankType: string): number;
  updateBestRank(rankType: string, rank: number): boolean;
  getBestRank(rankType: string): number;

  // 奖励管理 - 基于RankingService
  addReward(reward: RankingReward): void;
  getUnclaimedRewards(): RankingReward[];
  claimReward(rankType: string, season: number): boolean;
  hasUnclaimedReward(rankType: string, season: number): boolean;

  // 统计分析 - 基于RankingService
  getTotalRewardsClaimed(): number;
  getRankingHistory(rankType: string): RankingReward[];
  getBestRanksOverall(): any;

  // 数据转换 - 基于RankingService
  toClientRankingData(): any;
  getRankingStats(): any;
}

export interface GlobalRankingMethods {
  // 排名管理 - 基于RankingService
  addRankingEntry(entry: RankingEntry): void;
  updateRankingEntry(characterId: string, updates: Partial<RankingEntry>): boolean;
  removeRankingEntry(characterId: string): boolean;
  getRankingEntry(characterId: string): RankingEntry | null;

  // 排名查询 - 基于RankingService
  getTopRankings(limit: number): RankingEntry[];
  getRankingsByRange(startRank: number, endRank: number): RankingEntry[];
  getCharacterRank(characterId: string): number;

  // 排名统计 - 基于RankingService
  getTotalEntries(): number;
  getAverageScore(): number;
  getScoreRange(): { min: number; max: number };

  // 数据转换 - 基于RankingService
  toClientRankingList(limit?: number, offset?: number): any[];
  getRankingInfo(): any;
}

// 定义Document类型
export type CharacterRankingDocument = CharacterRanking & Document & CharacterRankingMethods;
export type GlobalRankingDocument = GlobalRanking & Document & GlobalRankingMethods;

// 创建索引
GlobalRankingSchema.index({ rankType: 1, season: 1 }, { unique: true }); // 复合唯一索引
GlobalRankingSchema.index({ 'rankings.characterId': 1 });
GlobalRankingSchema.index({ 'rankings.rank': 1 });
GlobalRankingSchema.index({ lastUpdateTime: 1 });

CharacterRankingSchema.index({ uid: 1 });
CharacterRankingSchema.index({ 'rewardHistory.rankType': 1 });
CharacterRankingSchema.index({ 'rewardHistory.status': 1 });
CharacterRankingSchema.index({ lastUpdateTime: 1 });
