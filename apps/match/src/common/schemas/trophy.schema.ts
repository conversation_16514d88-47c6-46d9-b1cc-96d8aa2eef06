import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 杯赛副本数据
 * 基于old项目trophyCopy.js的copyData结构
 */
@Schema({ _id: false })
export class TrophyCopyData {
  @Prop({ required: true })
  teamId: number;                        // 球队副本ID 配置的球队副本ID

  @Prop({ default: 0 })
  num: number;                           // 当前挑战次数

  @Prop({ default: 0 })
  alreadyPurchaseNum: number;            // 当前已购买次数
}

/**
 * 单个杯赛数据结构
 * 基于old项目trophyCopy.js的oneTrophyObj结构
 */
@Schema({ _id: false })
export class OneTrophy {
  @Prop({ required: true })
  id: number;                            // 杯赛副本ID，不需要自己生成,直接使用配置的ID

  @Prop({ default: 0 })
  num: number;                           // 当前挑战次数

  @Prop({ default: 0 })
  alreadyPurchaseNum: number;            // 当前已购买次数

  @Prop({ type: [TrophyCopyData], default: [] })
  copyData: TrophyCopyData[];            // 副本数据
}

/**
 * 杯赛奖励信息
 * 基于old项目trophyCopy.js的奖励处理逻辑
 */
@Schema({ _id: false })
export class TrophyRewardInfo {
  @Prop({ type: [Object], default: [] })
  itemUidList: any[];                    // 物品奖励列表

  @Prop({ type: [Object], default: [] })
  heroUidList: any[];                    // 球员奖励列表

  @Prop({ default: 0 })
  code: number;                          // 奖励处理结果代码
}

/**
 * 杯赛战斗结果
 * 基于old项目trophyCopy.js的战斗结果结构
 */
@Schema({ _id: false })
export class TrophyBattleResult {
  @Prop({ required: true })
  trophyId: number;                      // 杯赛ID

  @Prop({ required: true })
  teamId: number;                        // 队伍ID

  @Prop({ default: 0 })
  result: number;                        // 战斗结果 (0失败 1胜利)

  @Prop({ default: 0 })
  selfScore: number;                     // 己方比分

  @Prop({ default: 0 })
  enemyScore: number;                    // 敌方比分

  @Prop({ default: Date.now })
  battleTime: Date;                      // 战斗时间

  @Prop({ type: TrophyRewardInfo })
  rewardInfo: TrophyRewardInfo;          // 奖励信息
}

/**
 * 杯赛主文档
 * 基于old项目trophyCopy.js的主要数据结构
 */
@Schema({
  collection: 'trophy_copies',
  timestamps: true,
  versionKey: false,
})
export class TrophyCopy extends Document {
  @Prop({ required: true, unique: true })
  uid: string;                           // 玩家UID

  @Prop({ type: [OneTrophy], default: [] })
  allTrophyCopys: OneTrophy[];           // 所有杯赛数据 trophyId => oneTrophyObj

  @Prop({ default: 0 })
  lastUpdateTimes: number;               // 最后更新时间戳

  @Prop({ type: [TrophyBattleResult], default: [] })
  battleHistory: TrophyBattleResult[];   // 战斗历史记录

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const TrophyCopySchema = SchemaFactory.createForClass(TrophyCopy);

// 定义方法接口 - 基于TrophyService的真实业务逻辑
export interface TrophyCopyMethods {
  // 杯赛管理 - 基于TrophyService
  getTrophy(trophyId: number): OneTrophy | null;
  addTrophy(trophy: OneTrophy): void;
  updateTrophy(trophyId: number, updates: Partial<OneTrophy>): boolean;

  // 副本管理 - 基于TrophyService
  getCopyData(trophyId: number, teamId: number): TrophyCopyData | null;
  updateCopyData(trophyId: number, teamId: number, updates: Partial<TrophyCopyData>): boolean;

  // 挑战次数管理 - 基于TrophyService
  canChallenge(trophyId: number, teamId?: number): boolean;
  consumeChallenge(trophyId: number, teamId?: number): boolean;
  addChallengeTimes(trophyId: number, times: number, teamId?: number): boolean;
  resetDailyTimes(): void;

  // 购买次数管理 - 基于TrophyService
  canPurchase(trophyId: number, teamId?: number): boolean;
  purchaseChallengeTimes(trophyId: number, times: number, teamId?: number): boolean;
  getPurchaseInfo(trophyId: number, teamId?: number): { purchased: number; maxPurchase: number };

  // 战斗记录管理 - 基于TrophyService
  addBattleResult(result: TrophyBattleResult): void;
  getBattleHistory(trophyId?: number, limit?: number): TrophyBattleResult[];
  getLastBattleResult(trophyId?: number): TrophyBattleResult | null;

  // 统计分析 - 基于TrophyService
  getTrophyStats(trophyId: number): any;
  getOverallStats(): any;
  getWinRate(trophyId: number): number;
  getTotalBattles(trophyId: number): number;

  // 数据验证 - 基于TrophyService
  needsDailyReset(): boolean;
  validateTrophyData(): boolean;

  // 数据转换 - 基于TrophyService
  toClientTrophyData(): any[];
  getTrophySummary(): any;
}

// 定义Document类型
export type TrophyCopyDocument = TrophyCopy & Document & TrophyCopyMethods;

// 创建索引
TrophyCopySchema.index({ uid: 1 });
TrophyCopySchema.index({ 'allTrophyCopys.id': 1 });
TrophyCopySchema.index({ lastUpdateTimes: 1 });
TrophyCopySchema.index({ lastUpdateTime: 1 });
