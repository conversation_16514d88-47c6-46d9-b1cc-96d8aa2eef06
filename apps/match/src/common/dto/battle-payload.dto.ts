/**
 * Battle模块的Payload DTO定义
 * 
 * 为battle.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsObject, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { BattleTeamDataDto } from './battle.dto';

// ==================== 1. PVE战斗相关 ====================

/**
 * PVE战斗Payload DTO
 * @MessagePattern('battle.pveBattle')
 * 扩展PveBattleDto，合并BasePayloadDto内容
 */
export class PveBattlePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '玩家战斗数据', type: BattleTeamDataDto })
  @Expose()
  @IsObject({ message: '玩家战斗数据必须是对象' })
  @ValidateNested({ message: '玩家战斗数据格式不正确' })
  @Type(() => BattleTeamDataDto)
  characterBattleData: BattleTeamDataDto;

  @ApiProperty({ description: '敌方配置数据', type: BattleTeamDataDto })
  @Expose()
  @IsObject({ message: '敌方配置数据必须是对象' })
  @ValidateNested({ message: '敌方配置数据格式不正确' })
  @Type(() => BattleTeamDataDto)
  enemyBattleData: BattleTeamDataDto;

  @ApiProperty({ description: '战斗类型', example: 'league' })
  @Expose()
  @IsString({ message: '战斗类型必须是字符串' })
  battleType: string;

  @ApiPropertyOptional({ description: '联赛ID（可选）', example: 1001 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '联赛ID必须是数字' })
  leagueId?: number;

  @ApiPropertyOptional({ description: '副本ID（可选）', example: 2001 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '副本ID必须是数字' })
  teamCopyId?: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 2. PVP战斗相关 ====================

/**
 * PVP战斗Payload DTO
 * @MessagePattern('battle.pvpBattle')
 * 扩展PvpBattleDto，合并BasePayloadDto内容
 */
export class PvpBattlePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '主队玩家ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '主队玩家ID必须是字符串' })
  homeCharacterId: string;

  @ApiProperty({ description: '客队玩家ID', example: 'char_67890' })
  @Expose()
  @IsString({ message: '客队玩家ID必须是字符串' })
  awayCharacterId: string;

  @ApiProperty({ description: '主队战斗数据', type: BattleTeamDataDto })
  @Expose()
  @IsObject({ message: '主队战斗数据必须是对象' })
  @ValidateNested({ message: '主队战斗数据格式不正确' })
  @Type(() => BattleTeamDataDto)
  homeBattleData: BattleTeamDataDto;

  @ApiProperty({ description: '客队战斗数据', type: BattleTeamDataDto })
  @Expose()
  @IsObject({ message: '客队战斗数据必须是对象' })
  @ValidateNested({ message: '客队战斗数据格式不正确' })
  @Type(() => BattleTeamDataDto)
  awayBattleData: BattleTeamDataDto;

  @ApiProperty({ description: '战斗类型', example: 'league' })
  @Expose()
  @IsString({ message: '战斗类型必须是字符串' })
  battleType: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 3. 战斗回放相关 ====================

/**
 * 获取战斗回放Payload DTO
 * @MessagePattern('battle.getBattleReplay')
 * 扩展GetBattleReplayDto，合并BasePayloadDto内容
 */
export class GetBattleReplayPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '房间唯一标识', example: 'room_abc123' })
  @Expose()
  @IsString({ message: '房间唯一标识必须是字符串' })
  roomUid: string;

  @ApiPropertyOptional({ description: '角色ID（可选）', example: 'char_12345' })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色ID必须是字符串' })
  characterId?: string;
}

// ==================== 4. 战斗房间管理相关 ====================

/**
 * 删除战斗房间Payload DTO
 * @MessagePattern('battle.deleteBattleRoom')
 */
export class DeleteBattleRoomPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '房间唯一标识', example: 'room_abc123' })
  @Expose()
  @IsString({ message: '房间唯一标识必须是字符串' })
  roomUid: string;
}

/**
 * 获取战斗统计Payload DTO
 * @MessagePattern('battle.getStatistics')
 */
export class GetStatisticsPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}

/**
 * 清理过期房间Payload DTO
 * @MessagePattern('battle.cleanExpiredRooms')
 */
export class CleanExpiredRoomsPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}
