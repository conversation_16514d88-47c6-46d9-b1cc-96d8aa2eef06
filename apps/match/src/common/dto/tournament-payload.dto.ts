/**
 * Tournament模块的Payload DTO定义
 * 
 * 为tournament.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsArray } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 世界杯相关 ====================

/**
 * 获取世界杯信息Payload DTO
 * @MessagePattern('tournament.getWorldCupInfo')
 * 扩展GetWorldCupInfoDto，合并BasePayloadDto内容
 */
export class GetWorldCupInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

/**
 * 参加世界杯Payload DTO
 * @MessagePattern('tournament.joinWorldCup')
 * 扩展JoinWorldCupDto，合并BasePayloadDto内容
 */
export class JoinWorldCupPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '世界杯副本ID(1-7)', example: 1 })
  @Expose()
  @IsNumber({}, { message: '世界杯副本ID必须是数字' })
  worldCupId: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

/**
 * 世界杯战斗Payload DTO
 * @MessagePattern('tournament.worldCupBattle')
 * 扩展WorldCupBattleDto，合并BasePayloadDto内容
 */
export class WorldCupBattlePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

/**
 * 购买世界杯次数Payload DTO
 * @MessagePattern('tournament.buyWorldCupTimes')
 * 扩展BuyWorldCupTimesDto，合并BasePayloadDto内容
 */
export class BuyWorldCupTimesPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

/**
 * 领取世界杯奖励Payload DTO
 * @MessagePattern('tournament.getWorldCupReward')
 * 扩展GetWorldCupRewardDto，合并BasePayloadDto内容
 */
export class GetWorldCupRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 2. 区域杯赛相关 ====================

/**
 * 参加区域杯赛Payload DTO
 * @MessagePattern('tournament.joinRegionalCup')
 * 扩展JoinRegionalCupDto，合并BasePayloadDto内容
 */
export class JoinRegionalCupPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '杯赛类型', example: 'middleEast', enum: ['middleEast', 'gulf', 'mls'] })
  @Expose()
  @IsString({ message: '杯赛类型必须是字符串' })
  cupType: string;

  @ApiProperty({ description: '选择的队伍ID列表', type: [Number], example: [1, 2, 3] })
  @Expose()
  @IsArray({ message: '队伍ID列表必须是数组' })
  @IsNumber({}, { each: true, message: '队伍ID必须是数字' })
  teamIdList: number[];

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

/**
 * 区域杯赛战斗Payload DTO
 * @MessagePattern('tournament.regionalCupBattle')
 * 扩展RegionalCupBattleDto，合并BasePayloadDto内容
 */
export class RegionalCupBattlePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '杯赛类型', example: 'middleEast', enum: ['middleEast', 'gulf', 'mls'] })
  @Expose()
  @IsString({ message: '杯赛类型必须是字符串' })
  cupType: string;

  @ApiProperty({ description: '对手队伍ID', example: 101 })
  @Expose()
  @IsNumber({}, { message: '对手队伍ID必须是数字' })
  enemyTeamId: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 3. 管理接口相关 ====================

/**
 * 获取锦标赛统计信息Payload DTO
 * @MessagePattern('tournament.getStatistics')
 */
export class GetStatisticsPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}

/**
 * 重置每日锦标赛数据Payload DTO
 * @MessagePattern('tournament.resetDailyData')
 */
export class ResetDailyDataPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '角色ID（可选，不指定则重置所有）', example: 'char_12345' })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色ID必须是字符串' })
  characterId?: string;
}
