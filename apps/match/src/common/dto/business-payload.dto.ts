/**
 * Business模块的Payload DTO定义
 * 
 * 为business.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 商业赛信息相关 ====================

/**
 * 获取商业赛信息Payload DTO
 * @MessagePattern('business.getBusinessMatchInfo')
 * 扩展GetBusinessMatchInfoDto，合并BasePayloadDto内容
 */
export class GetBusinessMatchInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 2. 商业赛搜索相关 ====================

/**
 * 商业赛搜索Payload DTO
 * @MessagePattern('business.businessSearch')
 * 扩展BusinessSearchDto，合并BasePayloadDto内容
 */
export class BusinessSearchPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '搜索的对手名称', example: 'PlayerName' })
  @Expose()
  @IsString({ message: '对手名称必须是字符串' })
  name: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 3. 商业赛匹配相关 ====================

/**
 * 商业赛匹配Payload DTO
 * @MessagePattern('business.businessMatch')
 * 扩展BusinessMatchDto，合并BasePayloadDto内容
 */
export class BusinessMatchPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '对手UID', example: 'enemy_67890' })
  @Expose()
  @IsString({ message: '对手UID必须是字符串' })
  enemyUid: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 4. 购买商业赛次数相关 ====================

/**
 * 购买商业赛次数Payload DTO
 * @MessagePattern('business.buyBusinessMatch')
 * 扩展BuyBusinessMatchDto，合并BasePayloadDto内容
 */
export class BuyBusinessMatchPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '购买次数', example: 5 })
  @Expose()
  @IsNumber({}, { message: '购买次数必须是数字' })
  num: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 5. 统计信息相关 ====================

/**
 * 获取商业赛统计信息Payload DTO
 * @MessagePattern('business.getStatistics')
 */
export class GetStatisticsPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}
