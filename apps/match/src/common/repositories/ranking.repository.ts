import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { GlobalRanking, CharacterRanking, RankingEntry, RankingReward } from '../schemas/ranking.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 排名数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 全球排名管理
 * - 玩家排名数据管理
 * - 排名奖励系统
 * - 排名统计分析
 * - 过期数据清理
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 * - 双模型管理优化
 */
@Injectable()
export class RankingRepository extends BaseRepository<GlobalRanking> {
  private readonly characterRankingRepository: BaseRepository<CharacterRanking>;

  constructor(
    @InjectModel(GlobalRanking.name) globalRankingModel: Model<GlobalRanking>,
    @InjectModel(CharacterRanking.name) characterRankingModel: Model<CharacterRanking>
  ) {
    super(globalRankingModel, 'RankingRepository');
    // 创建角色排名的BaseRepository实例
    this.characterRankingRepository = new BaseRepository(characterRankingModel, 'CharacterRankingRepository');
  }

  // ==================== 全球排名相关 ====================

  /**
   * 根据排名类型查找全球排名
   * 使用BaseRepository的findOne方法优化性能
   */
  async findGlobalRanking(rankType: string, season?: number): Promise<XResult<GlobalRanking | null>> {
    const query: any = { rankType };
    if (season !== undefined) {
      query.season = season;
    }
    return this.findOne(query);
  }

  /**
   * 根据排名类型查找全球排名（Lean查询优化版本）
   */
  async findGlobalRankingLean(rankType: string, season?: number): Promise<XResult<any | null>> {
    const query: any = { rankType };
    if (season !== undefined) {
      query.season = season;
    }
    return this.findOneLean(query);
  }

  /**
   * 创建或更新全球排名
   * 使用BaseRepository的upsertOne方法优化性能
   */
  async upsertGlobalRanking(rankType: string, rankings: RankingEntry[], season: number = 0): Promise<XResult<GlobalRanking>> {
    const now = new Date();
    const nextUpdate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后

    return this.upsertOne(
      { rankType, season },
      {
        rankings,
        lastUpdateTime: now,
        nextUpdateTime: nextUpdate,
      }
    );
  }

  /**
   * 获取全球排名列表（分页）
   * 使用BaseRepository的findOne方法优化性能
   */
  async getGlobalRankingList(rankType: string, limit: number = 100, offset: number = 0, season?: number): Promise<XResult<RankingEntry[]>> {
    const query: any = { rankType };
    if (season !== undefined) {
      query.season = season;
    }

    const globalRankingResult = await this.findGlobalRankingLean(rankType, season);
    if (XResultUtils.isFailure(globalRankingResult) || !globalRankingResult.data) {
      return XResultUtils.ok([]);
    }

    const rankings = globalRankingResult.data.rankings?.slice(offset, offset + limit) || [];
    return XResultUtils.ok(rankings);
  }

  /**
   * 获取玩家在全球排名中的位置
   * 使用BaseRepository的findOne方法优化性能
   */
  async getCharacterGlobalRank(rankType: string, characterId: string, season?: number): Promise<XResult<number>> {
    const globalRankingResult = await this.findGlobalRankingLean(rankType, season);
    if (XResultUtils.isFailure(globalRankingResult) || !globalRankingResult.data) {
      return XResultUtils.ok(0);
    }

    const globalRanking = globalRankingResult.data;
    const characterEntry = globalRanking.rankings?.find(entry => entry.characterId === characterId);
    return XResultUtils.ok(characterEntry ? characterEntry.rank : 0);
  }

  // ==================== 玩家排名相关 ====================

  /**
   * 根据玩家UID查找排名数据
   * 使用BaseRepository的findOne方法优化性能
   */
  async findCharacterRanking(uid: string): Promise<XResult<CharacterRanking | null>> {
    return this.characterRankingRepository.findOne({ uid });
  }

  /**
   * 根据玩家UID查找排名数据（Lean查询优化版本）
   */
  async findCharacterRankingLean(uid: string): Promise<XResult<any | null>> {
    return this.characterRankingRepository.findOneLean({ uid });
  }

  /**
   * 创建或更新玩家排名数据
   * 使用BaseRepository的upsertOne方法优化性能
   */
  async upsertCharacterRanking(uid: string, characterRankingData: Partial<CharacterRanking>): Promise<XResult<CharacterRanking>> {
    return this.characterRankingRepository.upsertOne(
      { uid },
      { ...characterRankingData, lastUpdateTime: new Date() }
    );
  }

  /**
   * 添加排名奖励记录
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async addRankingReward(uid: string, reward: RankingReward): Promise<XResult<CharacterRanking | null>> {
    return this.characterRankingRepository.withTransaction(async (session) => {
      const characterRankingResult = await this.findCharacterRanking(uid);

      if (XResultUtils.isFailure(characterRankingResult) || !characterRankingResult.data) {
        // 创建新的玩家排名数据
        return await this.upsertCharacterRanking(uid, {
          uid,
          rewardHistory: [reward],
          currentRanks: {},
          bestRanks: {},
        });
      }

      const characterRanking = characterRankingResult.data;

      // 限制奖励历史记录数量为100条
      if (characterRanking.rewardHistory.length >= 100) {
        const removeCount = characterRanking.rewardHistory.length - 100 + 1;
        characterRanking.rewardHistory.splice(0, removeCount);
      }

      // 添加新奖励记录
      characterRanking.rewardHistory.push(reward);

      // 按时间排序（最新的在前）
      characterRanking.rewardHistory.sort((a, b) => {
        return new Date(b.rewardTime).getTime() - new Date(a.rewardTime).getTime();
      });

      return await this.upsertCharacterRanking(uid, { rewardHistory: characterRanking.rewardHistory });
    });
  }

  /**
   * 更新玩家当前排名
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async updateCharacterCurrentRanks(uid: string, ranks: any): Promise<XResult<CharacterRanking | null>> {
    return this.characterRankingRepository.withTransaction(async (session) => {
      const characterRankingResult = await this.findCharacterRankingLean(uid);

      const characterRanking = XResultUtils.isSuccess(characterRankingResult) ? characterRankingResult.data : null;
      const currentRanks = characterRanking ? { ...characterRanking.currentRanks, ...ranks } : ranks;
      const bestRanks = characterRanking ? { ...characterRanking.bestRanks } : {};

      // 更新历史最佳排名
      for (const [rankType, rank] of Object.entries(ranks)) {
        if (typeof rank === 'number' && rank > 0) {
          if (!bestRanks[rankType] || rank < bestRanks[rankType]) {
            bestRanks[rankType] = rank;
          }
        }
      }

      return await this.upsertCharacterRanking(uid, { currentRanks, bestRanks });
    });
  }

  /**
   * 获取未领取的排名奖励
   * 使用BaseRepository的findOne方法优化性能
   */
  async getUnclaimedRewards(uid: string): Promise<XResult<RankingReward[]>> {
    const characterRankingResult = await this.findCharacterRankingLean(uid);
    if (XResultUtils.isFailure(characterRankingResult) || !characterRankingResult.data) {
      return XResultUtils.ok([]);
    }

    const characterRanking = characterRankingResult.data;
    const unclaimedRewards = characterRanking.rewardHistory?.filter(reward => reward.status === 0) || [];
    return XResultUtils.ok(unclaimedRewards);
  }

  /**
   * 标记奖励为已领取
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async markRewardAsClaimed(uid: string, rankType: string, season: number): Promise<XResult<boolean>> {
    return this.characterRankingRepository.withTransaction(async (session) => {
      const characterRankingResult = await this.findCharacterRanking(uid);
      if (XResultUtils.isFailure(characterRankingResult) || !characterRankingResult.data) {
        return XResultUtils.ok(false);
      }

      const characterRanking = characterRankingResult.data;
      const reward = characterRanking.rewardHistory.find(
        r => r.rankType === rankType && r.season === season && r.status === 0
      );

      if (!reward) {
        return XResultUtils.ok(false);
      }

      reward.status = 1;
      const updateResult = await this.upsertCharacterRanking(uid, { rewardHistory: characterRanking.rewardHistory });
      return XResultUtils.ok(XResultUtils.isSuccess(updateResult));
    });
  }

  // ==================== 统计和管理 ====================

  /**
   * 获取排名统计信息
   * 使用BaseRepository的count方法和并行查询优化性能
   */
  async getStatistics(): Promise<XResult<any>> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // 并行执行所有统计查询
    const [globalResult, characterResult, activeResult] = await Promise.all([
      this.count({}),
      this.characterRankingRepository.count({}),
      this.characterRankingRepository.count({
        lastUpdateTime: { $gte: oneDayAgo }
      })
    ]);

    // 检查查询结果
    if (XResultUtils.isFailure(globalResult) ||
        XResultUtils.isFailure(characterResult) ||
        XResultUtils.isFailure(activeResult)) {
      return XResultUtils.error('获取排名统计信息失败', 'STATISTICS_QUERY_FAILED');
    }

    return XResultUtils.ok({
      totalGlobalRankings: globalResult.data,
      totalCharacterRankings: characterResult.data,
      activeToday: activeResult.data,
      timestamp: new Date(),
    });
  }

  /**
   * 删除过期的排名数据
   * 使用BaseRepository的deleteMany方法优化性能
   */
  async cleanExpiredRankings(daysToKeep: number = 30): Promise<XResult<number>> {
    const expireTime = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);

    const result = await this.deleteMany({
      lastUpdateTime: { $lt: expireTime }
    });

    if (XResultUtils.isSuccess(result)) {
      const deletedCount = result.data.deletedCount || 0;
      this.logger.log(`清理过期排名数据: ${deletedCount}个`);
      return XResultUtils.ok(deletedCount);
    }

    return XResultUtils.ok(0);
  }

  /**
   * 批量查询玩家排名数据
   * 使用BaseRepository的findMany方法优化性能
   */
  async findCharacterRankingsByIds(ids: string[]): Promise<XResult<CharacterRanking[]>> {
    return this.characterRankingRepository.findMany({ uid: { $in: ids } });
  }

  /**
   * 批量查询玩家排名数据（Lean查询优化版本）
   */
  async findCharacterRankingsByIdsLean(ids: string[]): Promise<XResult<any[]>> {
    return this.characterRankingRepository.findManyLean({ uid: { $in: ids } }, {
      select: ['uid', 'currentRanks', 'bestRanks', 'lastUpdateTime']
    });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加排名特定的验证规则
   */
  protected validateData(data: Partial<GlobalRanking>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.rankType) {
        return XResultUtils.error('排名类型不能为空', 'RANK_TYPE_REQUIRED');
      }

      if (!data.rankings || !Array.isArray(data.rankings)) {
        return XResultUtils.error('排名数据不能为空', 'RANKINGS_REQUIRED');
      }
    }

    if (data.season !== undefined && data.season < 0) {
      return XResultUtils.error('赛季编号不能为负数', 'INVALID_SEASON');
    }

    if (data.rankings && Array.isArray(data.rankings)) {
      if (data.rankings.length > 10000) {
        return XResultUtils.error('排名数据不能超过10000条', 'TOO_MANY_RANKINGS');
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对排名数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findGlobalRanking': 600,          // 全球排名缓存10分钟
      'findGlobalRankingLean': 300,      // 全球排名简介缓存5分钟
      'getGlobalRankingList': 300,       // 排名列表缓存5分钟
      'getCharacterGlobalRank': 180,     // 玩家排名缓存3分钟
      'findCharacterRanking': 300,       // 玩家排名数据缓存5分钟
      'getUnclaimedRewards': 120,        // 未领取奖励缓存2分钟
      'getStatistics': 60,               // 统计信息缓存1分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
