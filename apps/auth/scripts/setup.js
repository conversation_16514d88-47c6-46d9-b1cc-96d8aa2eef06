/**
 * 数据库设置脚本
 * 完整的认证系统初始化流程
 */

const dotenv = require('dotenv');
const database = require('./utils/database');
const config = require('./utils/config');
const Logger = require('./utils/logger');
const { initializeData } = require('./init-data');
const { seedPermissions } = require('./seed-permissions');
const { seedRoles } = require('./seed-roles');

// 加载环境变量
dotenv.config();

const logger = new Logger('Setup');

/**
 * 检查系统要求
 */
async function checkSystemRequirements() {
  logger.subtitle('检查系统要求...');
  
  const requirements = [];
  
  // 检查Node.js版本
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion < 16) {
    requirements.push(`Node.js版本过低: ${nodeVersion} (需要 >= 16.0.0)`);
  } else {
    logger.log(`✓ Node.js版本: ${nodeVersion}`);
  }
  
  // 检查必需的环境变量
  const requiredEnvVars = [
    'MONGODB_URI',
    'REDIS_HOST',
    'JWT_SECRET'
  ];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      requirements.push(`缺少环境变量: ${envVar}`);
    } else {
      logger.log(`✓ 环境变量 ${envVar}: 已设置`);
    }
  }
  
  if (requirements.length > 0) {
    logger.error('系统要求检查失败:');
    requirements.forEach(req => logger.error(`  - ${req}`));
    throw new Error('系统要求不满足');
  }
  
  logger.success('系统要求检查通过');
}

/**
 * 检查数据库连接
 */
async function checkDatabaseConnections() {
  logger.subtitle('检查数据库连接...');
  
  try {
    await database.connect();
    const health = await database.checkHealth();
    
    if (!health.overall) {
      const issues = [];
      if (!health.mongodb) issues.push('MongoDB连接失败');
      if (!health.redis) issues.push('Redis连接失败');
      throw new Error(`数据库连接问题: ${issues.join(', ')}`);
    }
    
    logger.success('所有数据库连接正常');
    
    // 获取数据库统计信息
    const stats = await database.getStats();
    logger.info('数据库统计信息:');
    if (stats.mongodb.collections !== undefined) {
      logger.log(`  MongoDB集合数: ${stats.mongodb.collections}`);
    }
    if (stats.redis.connectedClients !== undefined) {
      logger.log(`  Redis连接数: ${stats.redis.connectedClients}`);
    }
    
  } catch (error) {
    logger.error('数据库连接检查失败:', error);
    throw error;
  } finally {
    await database.disconnect();
  }
}

/**
 * 清理现有数据（可选）
 */
async function cleanupExistingData(force = false) {
  if (!force && !config.get('test.cleanupAfterTest', false)) {
    logger.info('跳过数据清理（未启用强制清理）');
    return;
  }
  
  logger.subtitle('清理现有数据...');
  
  try {
    await database.connect();
    await database.cleanupTestData();
    logger.success('数据清理完成');
  } catch (error) {
    logger.warn('数据清理失败:', error.message);
  } finally {
    await database.disconnect();
  }
}

/**
 * 验证安装结果
 */
async function validateInstallation() {
  logger.subtitle('验证安装结果...');
  
  try {
    await database.connect();
    
    const mongoose = require('mongoose');
    const db = mongoose.connection.db;
    
    // 检查权限数量
    const permissionsCount = await db.collection('permissions').countDocuments();
    logger.log(`权限数量: ${permissionsCount}`);
    
    // 检查角色数量
    const rolesCount = await db.collection('roles').countDocuments();
    logger.log(`角色数量: ${rolesCount}`);
    
    // 检查用户数量
    const usersCount = await db.collection('users').countDocuments();
    logger.log(`用户数量: ${usersCount}`);
    
    // 检查管理员用户
    const adminUser = await db.collection('users').findOne({ username: 'admin' });
    if (adminUser) {
      logger.log(`✓ 管理员用户已创建: ${adminUser.email}`);
    } else {
      throw new Error('管理员用户未找到');
    }
    
    // 检查超级管理员角色
    const superAdminRole = await db.collection('roles').findOne({ name: 'super_admin' });
    if (superAdminRole) {
      logger.log(`✓ 超级管理员角色已创建: ${superAdminRole.displayName}`);
    } else {
      throw new Error('超级管理员角色未找到');
    }
    
    logger.success('安装验证通过');
    
  } catch (error) {
    logger.error('安装验证失败:', error);
    throw error;
  } finally {
    await database.disconnect();
  }
}

/**
 * 显示安装完成信息
 */
function showCompletionInfo() {
  logger.separator('=', 60);
  logger.complete('🎉 足球经理认证系统安装完成！');
  logger.separator('=', 60);
  
  logger.info('🎯 系统功能:');
  logger.log('   • 完整的用户认证和授权系统');
  logger.log('   • 基于角色的权限控制 (RBAC)');
  logger.log('   • 安全审计和风险评估');
  logger.log('   • 会话管理和JWT令牌');
  logger.log('   • 足球经理游戏专用权限和角色');
  logger.log('   • 多因子认证和设备管理');
  logger.log('   • 暴力破解防护和异常检测');
  logger.log('');
  
  logger.info('🔑 默认管理员账户:');
  logger.log('   用户名: admin');
  logger.log('   邮箱: <EMAIL>');
  logger.log('   密码: Admin123!@#');
  logger.log('');
  
  logger.info('📚 可用的游戏角色:');
  logger.log('   • game_master - 游戏管理员');
  logger.log('   • league_admin - 联赛管理员');
  logger.log('   • team_manager - 球队经理');
  logger.log('   • assistant_coach - 助理教练');
  logger.log('   • scout - 球探');
  logger.log('   • youth_coach - 青训教练');
  logger.log('   • financial_director - 财务总监');
  logger.log('   • fan_club_leader - 球迷会会长');
  logger.log('   • sports_journalist - 体育记者');
  logger.log('   • casual_player - 休闲玩家');
  logger.log('   • trial_user - 试用用户');
  logger.log('   • vip_member - VIP会员');
  logger.log('');
  
  logger.info('🚀 下一步操作:');
  logger.log('   1. 启动认证服务: npm run start:dev');
  logger.log('   2. 运行测试: node scripts-js/test-runner.js');
  logger.log('   3. 查看API文档: http://localhost:3001/docs');
  logger.log('   4. 监控系统状态: http://localhost:3001/health');
  logger.log('');
  
  logger.info('🔧 维护脚本:');
  logger.log('   • 清理会话: node scripts-js/cleanup-sessions.js');
  logger.log('   • 清理日志: node scripts-js/cleanup-audit-logs.js');
  logger.log('   • 性能测试: node scripts-js/performance-test.js');
  logger.log('   • 安全测试: node scripts-js/security-test.js');
  
  logger.separator('=', 60);
}

/**
 * 主设置函数
 */
async function setupDatabase() {
  const startTime = Date.now();
  
  try {
    logger.start('🚀 开始设置足球经理认证系统数据库...');
    logger.separator();
    
    // 1. 检查系统要求
    await checkSystemRequirements();
    
    // 2. 检查数据库连接
    await checkDatabaseConnections();
    
    // 3. 清理现有数据（可选）
    await cleanupExistingData(process.argv.includes('--force-clean'));
    
    // 4. 初始化基础数据
    logger.subtitle('📋 步骤 1: 初始化基础数据...');
    await initializeData();
    
    // 5. 创建游戏权限
    logger.subtitle('🎮 步骤 2: 创建游戏权限...');
    await seedPermissions();
    
    // 6. 创建游戏角色
    logger.subtitle('👥 步骤 3: 创建游戏角色...');
    await seedRoles();
    
    // 7. 验证安装结果
    await validateInstallation();
    
    const duration = Date.now() - startTime;
    logger.success(`✅ 数据库设置完成！耗时: ${(duration / 1000).toFixed(2)}秒`);
    
    // 显示完成信息
    showCompletionInfo();
    
  } catch (error) {
    logger.error('❌ 数据库设置失败:', error);
    logger.error('请检查错误信息并重试');
    process.exit(1);
  }
}

// 运行设置
if (require.main === module) {
  setupDatabase().catch((error) => {
    console.error('设置失败:', error);
    process.exit(1);
  });
}

module.exports = { setupDatabase };
