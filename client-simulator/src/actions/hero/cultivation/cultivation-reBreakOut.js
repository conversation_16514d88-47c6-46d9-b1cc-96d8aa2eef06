/**
 * 重新突破 对应old项目: reBreakOutHero
 * 
 * 微服务: hero
 * 模块: cultivation
 * Controller: cultivation
 * Pattern: cultivation.reBreakOut
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.496Z
 */

const BaseAction = require('../../../core/base-action');

class CultivationreBreakOutAction extends BaseAction {
  static metadata = {
    name: '重新突破 对应old项目: reBreakOutHero',
    description: '重新突破 对应old项目: reBreakOutHero',
    category: 'hero',
    serviceName: 'hero',
    module: 'cultivation',
    actionName: 'cultivation.reBreakOut',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '重新突破 对应old项目: reBreakOutHero成功'
      };
    } else {
      throw new Error(`重新突破 对应old项目: reBreakOutHero失败: ${response.message}`);
    }
  }
}

module.exports = CultivationreBreakOutAction;