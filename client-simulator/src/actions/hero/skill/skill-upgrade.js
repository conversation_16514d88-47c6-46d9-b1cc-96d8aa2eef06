/**
 * 升级球员技能
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.upgrade
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.597Z
 */

const BaseAction = require('../../../core/base-action');

class SkillupgradeAction extends BaseAction {
  static metadata = {
    name: '升级球员技能',
    description: '升级球员技能',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.upgrade',
    prerequisites: ["login","character"],
    params: {
      "upgradeDto": {
            "type": "object",
            "required": true,
            "description": "upgradeDto参数",
            "properties": {
                  "skillId": {
                        "type": "string",
                        "required": true,
                        "description": "skillId参数"
                  },
                  "targetLevel": {
                        "type": "number",
                        "required": true,
                        "description": "targetLevel参数"
                  },
                  "useItems": {
                        "type": "boolean",
                        "required": false,
                        "description": "useItems参数"
                  },
                  "items": {
                        "type": "array",
                        "required": false,
                        "description": "items参数"
                  }
            },
            "example": {
                  "skillId": "示例skillId",
                  "targetLevel": 1,
                  "useItems": true,
                  "items": []
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { upgradeDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      upgradeDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '升级球员技能成功'
      };
    } else {
      throw new Error(`升级球员技能失败: ${response.message}`);
    }
  }
}

module.exports = SkillupgradeAction;