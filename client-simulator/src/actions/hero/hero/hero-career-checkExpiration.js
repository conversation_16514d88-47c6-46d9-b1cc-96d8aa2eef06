/**
 * 检查合约到期
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.career.checkExpiration
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.573Z
 */

const BaseAction = require('../../../core/base-action');

class HerocareercheckExpirationAction extends BaseAction {
  static metadata = {
    name: '检查合约到期',
    description: '检查合约到期',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.career.checkExpiration',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '检查合约到期成功'
      };
    } else {
      throw new Error(`检查合约到期失败: ${response.message}`);
    }
  }
}

module.exports = HerocareercheckExpirationAction;