/**
 * 完成新手引导
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.progress.finishGuide
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.320Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterprogressfinishGuideAction extends BaseAction {
  static metadata = {
    name: '完成新手引导',
    description: '完成新手引导',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.progress.finishGuide',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '完成新手引导成功'
      };
    } else {
      throw new Error(`完成新手引导失败: ${response.message}`);
    }
  }
}

module.exports = CharacterprogressfinishGuideAction;