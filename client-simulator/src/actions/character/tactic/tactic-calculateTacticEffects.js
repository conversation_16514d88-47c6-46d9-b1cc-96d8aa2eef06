/**
 * 计算战术效果 对应old项目: calcTacticEffects方法
 * 
 * 微服务: character
 * 模块: tactic
 * Controller: tactic
 * Pattern: tactic.calculateTacticEffects
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.410Z
 */

const BaseAction = require('../../../core/base-action');

class TacticcalculateTacticEffectsAction extends BaseAction {
  static metadata = {
    name: '计算战术效果 对应old项目: calcTacticEffects方法',
    description: '计算战术效果 对应old项目: calcTacticEffects方法',
    category: 'character',
    serviceName: 'character',
    module: 'tactic',
    actionName: 'tactic.calculateTacticEffects',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "tacticKey": {
            "type": "string",
            "required": true,
            "description": "tacticKey参数"
      },
      "level": {
            "type": "number",
            "required": true,
            "description": "level参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, tacticKey, level, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      tacticKey,
      level,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '计算战术效果 对应old项目: calcTacticEffects方法成功'
      };
    } else {
      throw new Error(`计算战术效果 对应old项目: calcTacticEffects方法失败: ${response.message}`);
    }
  }
}

module.exports = TacticcalculateTacticEffectsAction;