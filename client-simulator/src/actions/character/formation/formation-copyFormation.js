/**
 * 复制阵容 对应old项目: copyTeamFormation方法，优化API命名
 * 
 * 微服务: character
 * 模块: formation
 * Controller: formation
 * Pattern: formation.copyFormation
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.353Z
 */

const BaseAction = require('../../../core/base-action');

class FormationcopyFormationAction extends BaseAction {
  static metadata = {
    name: '复制阵容 对应old项目: copyTeamFormation方法，优化API命名',
    description: '复制阵容 对应old项目: copyTeamFormation方法，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'formation',
    actionName: 'formation.copyFormation',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "sourceFormationId": {
            "type": "string",
            "required": true,
            "description": "sourceFormationId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, sourceFormationId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      sourceFormationId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '复制阵容 对应old项目: copyTeamFormation方法，优化API命名成功'
      };
    } else {
      throw new Error(`复制阵容 对应old项目: copyTeamFormation方法，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = FormationcopyFormationAction;