/**
 * 获取当前会话信息 - 微服务调用
 * 
 * 微服务: auth
 * 模块: auth
 * Controller: character-auth
 * Pattern: character-auth.getCurrentSession
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.294Z
 */

const BaseAction = require('../../core/base-action');

class CharacterAuthgetCurrentSessionAction extends BaseAction {
  static metadata = {
    name: '获取当前会话信息 - 微服务调用',
    description: '获取当前会话信息 - 微服务调用',
    category: 'auth',
    serviceName: 'auth',
    module: 'auth',
    actionName: 'character-auth.getCurrentSession',
    prerequisites: ["login"],
    params: {
      "characterToken": {
            "type": "string",
            "required": true,
            "description": "characterToken参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterToken } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterToken
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取当前会话信息 - 微服务调用成功'
      };
    } else {
      throw new Error(`获取当前会话信息 - 微服务调用失败: ${response.message}`);
    }
  }
}

module.exports = CharacterAuthgetCurrentSessionAction;