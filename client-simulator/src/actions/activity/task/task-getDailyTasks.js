/**
 * 获取每日任务
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.getDailyTasks
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.271Z
 */

const BaseAction = require('../../../core/base-action');

class TaskgetDailyTasksAction extends BaseAction {
  static metadata = {
    name: '获取每日任务',
    description: '获取每日任务',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.getDailyTasks',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取每日任务成功'
      };
    } else {
      throw new Error(`获取每日任务失败: ${response.message}`);
    }
  }
}

module.exports = TaskgetDailyTasksAction;