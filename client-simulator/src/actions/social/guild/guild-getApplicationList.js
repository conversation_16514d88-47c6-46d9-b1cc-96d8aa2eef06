/**
 * 获取申请列表
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.getApplicationList
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.788Z
 */

const BaseAction = require('../../../core/base-action');

class GuildgetApplicationListAction extends BaseAction {
  static metadata = {
    name: '获取申请列表',
    description: '获取申请列表',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.getApplicationList',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "guildId": {
            "type": "string",
            "required": true,
            "description": "guildId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, guildId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      guildId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取申请列表成功'
      };
    } else {
      throw new Error(`获取申请列表失败: ${response.message}`);
    }
  }
}

module.exports = GuildgetApplicationListAction;