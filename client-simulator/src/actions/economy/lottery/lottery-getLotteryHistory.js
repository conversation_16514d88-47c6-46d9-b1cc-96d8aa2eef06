/**
 * 获取抽奖历史记录
 * 
 * 微服务: economy
 * 模块: lottery
 * Controller: lottery
 * Pattern: lottery.getLotteryHistory
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.438Z
 */

const BaseAction = require('../../../core/base-action');

class LotterygetLotteryHistoryAction extends BaseAction {
  static metadata = {
    name: '获取抽奖历史记录',
    description: '获取抽奖历史记录',
    category: 'economy',
    serviceName: 'economy',
    module: 'lottery',
    actionName: 'lottery.getLotteryHistory',
    prerequisites: ["login","character"],
    params: {},
    timeout: 10000
  };

  async perform(client, params) {
    // 无参数
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取抽奖历史记录成功'
      };
    } else {
      throw new Error(`获取抽奖历史记录失败: ${response.message}`);
    }
  }
}

module.exports = LotterygetLotteryHistoryAction;