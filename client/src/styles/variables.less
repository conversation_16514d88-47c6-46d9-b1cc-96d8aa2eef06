// 设计变量定义

// 颜色系统 - MUD风格配色
@primary-color: #52c41a;           // 主色：绿色（经典终端绿）
@primary-color-hover: #73d13d;     // 主色悬停
@primary-color-active: #389e0d;    // 主色激活

@success-color: #52c41a;           // 成功色
@warning-color: #faad14;           // 警告色
@error-color: #ff4d4f;             // 错误色
@info-color: #1890ff;              // 信息色

@highlight-color: #fadb14;         // 高亮色：黄色

// 背景色系统
@bg-color: #001529;                // 主背景：深蓝色
@card-bg: #002140;                 // 卡片背景：稍浅的深蓝
@hover-bg: rgba(82, 196, 26, 0.1); // 悬停背景

// 文字色系统
@text-color: #ffffff;              // 主文字色：白色
@text-color-secondary: #a0a0a0;    // 次要文字色：灰色
@text-color-placeholder: #666666;  // 占位符文字色
@text-color-disabled: #4a4a4a;     // 禁用文字色

// 边框色系统
@border-color: #434343;            // 主边框色
@border-color-light: #595959;      // 浅边框色
@border-color-dark: #303030;       // 深边框色

// 字体系统
@font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
@font-family-mono: 'Courier New', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

// 字体大小
@font-size-xs: 10px;
@font-size-sm: 12px;
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-xl: 18px;
@font-size-xxl: 20px;

// 行高
@line-height-base: 1.6;
@line-height-sm: 1.4;
@line-height-lg: 1.8;

// 间距系统
@padding-xs: 4px;
@padding-sm: 8px;
@padding-base: 12px;
@padding-lg: 16px;
@padding-xl: 24px;

@margin-xs: 4px;
@margin-sm: 8px;
@margin-base: 12px;
@margin-lg: 16px;
@margin-xl: 24px;

// 圆角
@border-radius-xs: 2px;
@border-radius-sm: 4px;
@border-radius-base: 6px;
@border-radius-lg: 8px;

// 阴影
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
@box-shadow-card: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
@box-shadow-elevated: 0 4px 12px rgba(0, 0, 0, 0.15);

// 过渡动画
@transition-duration: 0.3s;
@transition-timing: ease-in-out;

// Z-index层级
@z-index-dropdown: 1000;
@z-index-sticky: 1020;
@z-index-fixed: 1030;
@z-index-modal-backdrop: 1040;
@z-index-modal: 1050;
@z-index-popover: 1060;
@z-index-tooltip: 1070;

// 断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 容器最大宽度
@container-max-width-sm: 540px;
@container-max-width-md: 720px;
@container-max-width-lg: 960px;
@container-max-width-xl: 1140px;
@container-max-width-xxl: 1320px;

// 游戏特定变量
@hero-card-width: 280px;
@hero-card-height: 200px;
@formation-field-width: 600px;
@formation-field-height: 400px;

// 状态颜色
@status-online: @success-color;
@status-offline: #8c8c8c;
@status-busy: @warning-color;
@status-away: @info-color;

// 稀有度颜色（用于球员、道具等）
@rarity-common: #ffffff;
@rarity-uncommon: #1eff00;
@rarity-rare: #0070dd;
@rarity-epic: #a335ee;
@rarity-legendary: #ff8000;
@rarity-mythic: #e6cc80;

// 位置颜色（足球位置）
@position-gk: #ff6b6b;      // 门将：红色
@position-def: #4ecdc4;     // 后卫：青色
@position-mid: #45b7d1;     // 中场：蓝色
@position-att: #f9ca24;     // 前锋：黄色

// 表格相关
@table-header-bg: @card-bg;
@table-row-hover-bg: rgba(82, 196, 26, 0.05);
@table-border-color: @border-color;

// 表单相关
@input-bg: @bg-color;
@input-border-color: @border-color;
@input-focus-border-color: @primary-color;
@input-placeholder-color: @text-color-placeholder;

// 按钮相关
@btn-height-sm: 24px;
@btn-height-base: 32px;
@btn-height-lg: 40px;
@btn-padding-horizontal-sm: 8px;
@btn-padding-horizontal-base: 15px;
@btn-padding-horizontal-lg: 20px;
