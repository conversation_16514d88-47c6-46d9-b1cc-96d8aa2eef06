// 导入变量
@import './variables.less';

// 全局样式重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: @font-family-base;
  background-color: @bg-color;
  color: @text-color;
  line-height: 1.6;
}

#app {
  height: 100%;
  min-height: 100vh;
}

// 基础样式
h1, h2, h3, h4, h5, h6 {
  color: @text-color;
  margin: 0;
}

p {
  color: @text-color-secondary;
  margin: 0 0 @margin-base 0;
}

a {
  color: @primary-color;
  text-decoration: none;
}

a:hover {
  color: @primary-color-hover;
}
