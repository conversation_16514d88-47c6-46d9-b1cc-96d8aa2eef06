/**
 * Schema方法Result模式适配装饰器
 * 
 * 核心理念：
 * - 通过装饰器模式自动包装现有的schema方法
 * - 保持原有方法的简洁性，同时提供Result模式的错误处理
 * - 支持同步和异步方法的自动适配
 * 
 * 使用方式：
 * 1. 在schema定义后调用 SchemaResultAdapter.wrapMethods(schema)
 * 2. 原有方法保持不变，新增带Result后缀的方法
 * 3. Service层可以选择使用原方法或Result方法
 */

import { Schema } from 'mongoose';
import { XResult, XResultUtils } from '../types/result.type';

/**
 * Schema方法Result适配器
 */
export class SchemaResultAdapter {
  /**
   * 为Schema的所有实例方法创建Result版本
   * @param schema Mongoose Schema实例
   * @param options 配置选项
   */
  static wrapMethods<T extends Schema>(
    schema: T, 
    options: SchemaResultWrapOptions = {}
  ): void {
    const { 
      methodSuffix = 'Result',
      excludeMethods = ['toObject', 'toJSON', 'toString', 'valueOf'],
      includePrivateMethods = false 
    } = options;

    // 获取所有实例方法
    const methods = this.getInstanceMethods(schema, includePrivateMethods);
    
    methods.forEach(methodName => {
      if (excludeMethods.includes(methodName)) return;
      
      const originalMethod = schema.methods[methodName];
      if (typeof originalMethod !== 'function') return;

      // 创建Result版本的方法名
      const resultMethodName = `${methodName}${methodSuffix}`;
      
      // 包装原方法
      schema.methods[resultMethodName] = this.wrapMethod(originalMethod, methodName);
    });
  }

  /**
   * 包装单个方法为Result模式
   */
  private static wrapMethod(originalMethod: Function, methodName: string): Function {
    return function(this: any, ...args: any[]): XResult<any> {
      try {
        const result = originalMethod.apply(this, args);
        
        // 处理Promise返回值
        if (result && typeof result.then === 'function') {
          return result
            .then((data: any) => XResultUtils.ok(data))
            .catch((error: any) => XResultUtils.error(
              `方法 ${methodName} 执行失败: ${error.message}`,
              `SCHEMA_METHOD_ERROR_${methodName.toUpperCase()}`,
              error
            ));
        }
        
        // 处理同步返回值
        return XResultUtils.ok(result);
        
      } catch (error: any) {
        return XResultUtils.error(
          `方法 ${methodName} 执行失败: ${error.message}`,
          `SCHEMA_METHOD_ERROR_${methodName.toUpperCase()}`,
          error
        );
      }
    };
  }

  /**
   * 获取Schema的所有实例方法
   */
  private static getInstanceMethods(schema: Schema, includePrivate: boolean): string[] {
    const methods: string[] = [];
    
    // 从schema.methods获取
    if (schema.methods) {
      Object.getOwnPropertyNames(schema.methods).forEach(name => {
        if (typeof schema.methods[name] === 'function') {
          if (includePrivate || !name.startsWith('_')) {
            methods.push(name);
          }
        }
      });
    }
    
    return methods;
  }

  /**
   * 为特定方法创建Result版本
   * @param schema Schema实例
   * @param methodName 方法名
   * @param resultMethodName Result方法名（可选）
   */
  static wrapSpecificMethod<T extends Schema>(
    schema: T,
    methodName: string,
    resultMethodName?: string
  ): void {
    const targetMethodName = resultMethodName || `${methodName}Result`;
    const originalMethod = schema.methods[methodName];
    
    if (typeof originalMethod !== 'function') {
      throw new Error(`方法 ${methodName} 不存在或不是函数`);
    }
    
    schema.methods[targetMethodName] = this.wrapMethod(originalMethod, methodName);
  }
}

/**
 * Schema Result包装选项
 */
export interface SchemaResultWrapOptions {
  /** Result方法后缀，默认为 'Result' */
  methodSuffix?: string;
  /** 排除的方法列表 */
  excludeMethods?: string[];
  /** 是否包含私有方法（以_开头） */
  includePrivateMethods?: boolean;
}

/**
 * 方法装饰器 - 用于单个方法的Result适配
 */
export function ResultMethod(options: { 
  errorCode?: string; 
  errorMessage?: string;
} = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]): XResult<any> {
      try {
        const result = originalMethod.apply(this, args);
        
        if (result && typeof result.then === 'function') {
          return result
            .then((data: any) => XResultUtils.ok(data))
            .catch((error: any) => XResultUtils.error(
              options.errorMessage || `方法 ${propertyKey} 执行失败: ${error.message}`,
              options.errorCode || `SCHEMA_METHOD_ERROR_${propertyKey.toUpperCase()}`,
              error
            ));
        }
        
        return XResultUtils.ok(result);
        
      } catch (error: any) {
        return XResultUtils.error(
          options.errorMessage || `方法 ${propertyKey} 执行失败: ${error.message}`,
          options.errorCode || `SCHEMA_METHOD_ERROR_${propertyKey.toUpperCase()}`,
          error
        );
      }
    };
    
    return descriptor;
  };
}

/**
 * 批量Result方法生成器
 */
export class BatchResultMethodGenerator {
  /**
   * 为多个Schema批量生成Result方法
   */
  static generateForSchemas(schemas: { name: string; schema: Schema }[]): void {
    schemas.forEach(({ name, schema }) => {
      try {
        SchemaResultAdapter.wrapMethods(schema, {
          excludeMethods: ['toObject', 'toJSON', 'toString', 'valueOf', 'save', 'remove']
        });
        console.log(`✅ ${name} Schema Result方法生成完成`);
      } catch (error) {
        console.error(`❌ ${name} Schema Result方法生成失败:`, error);
      }
    });
  }
}
