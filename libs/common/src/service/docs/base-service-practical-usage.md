# BaseService实用业务验证方法使用指南

## 🎯 设计理念

Controller层的验证管道已经处理了基础参数验证（非空、类型、格式等），BaseService应该专注于**业务逻辑验证**，提供简单实用的验证方法。

## 📋 实用验证方法列表

### 1. **validateUniqueness** - 唯一性验证（最常用）
```typescript
// 验证角色名唯一性
const nameCheck = await this.validateUniqueness(
  createDto.name,
  '角色名称',
  (name) => this.characterRepository.existsByName(name, serverId)
);
if (XResultUtils.isFailure(nameCheck)) return nameCheck;
```

### 2. **validateOwnership** - 资源所有权验证
```typescript
// 验证角色所有权
const ownershipCheck = this.validateOwnership(
  userId,
  character.userId,
  '角色'
);
if (XResultUtils.isFailure(ownershipCheck)) return ownershipCheck;
```

### 3. **validateSufficientResource** - 资源充足性验证
```typescript
// 验证金币是否充足
const goldCheck = this.validateSufficientResource(
  character.gold,
  upgradeConfig.goldCost,
  '金币'
);
if (XResultUtils.isFailure(goldCheck)) return goldCheck;
```

### 4. **validateBusinessStatus** - 业务状态验证
```typescript
// 验证角色是否可以进行训练
const statusCheck = this.validateBusinessStatus(
  character.status,
  ['IDLE', 'RESTING'],
  '开始训练'
);
if (XResultUtils.isFailure(statusCheck)) return statusCheck;
```

### 5. **validateTimeLimit** - 时间限制验证
```typescript
// 验证签到冷却时间
const checkinCheck = this.validateTimeLimit(
  character.lastCheckinTime,
  24 * 60 * 60 * 1000, // 24小时
  '每日签到'
);
if (XResultUtils.isFailure(checkinCheck)) return checkinCheck;
```

### 6. **validateCountLimit** - 数量限制验证
```typescript
// 验证每日任务完成次数
const dailyTaskCheck = this.validateCountLimit(
  character.dailyTaskCount,
  10,
  '每日任务'
);
if (XResultUtils.isFailure(dailyTaskCheck)) return dailyTaskCheck;
```

### 7. **validateLevelRequirement** - 等级要求验证
```typescript
// 验证角色等级
const levelCheck = this.validateLevelRequirement(
  character.level,
  10,
  '角色等级'
);
if (XResultUtils.isFailure(levelCheck)) return levelCheck;
```

### 8. **validateBatch** - 批量验证
```typescript
// 批量验证多个条件
const validationResult = await this.validateBatch([
  () => this.validateUniqueness(name, '角色名', checkName),
  () => this.validateSufficientResource(gold, cost, '金币'),
  () => this.validateLevelRequirement(level, 10, '角色等级')
]);
if (XResultUtils.isFailure(validationResult)) return validationResult;
```

## 🚀 createCharacter函数实用示例

```typescript
/**
 * 创建新角色 - 使用实用业务验证方法
 */
async createCharacter(createDto: CreateCharacterDto): Promise<XResult<CharacterDocument>> {
  return this.executeBusinessOperation(
    'createCharacter',
    async () => {
      // ==================== 业务验证 ====================
      
      // 1. 验证角色名唯一性（最常用的验证）
      const nameUniquenessResult = await this.validateUniqueness(
        createDto.name,
        '角色名称',
        async (name) => {
          return await this.characterRepository.existsByName(name, createDto.serverId);
        }
      );
      if (XResultUtils.isFailure(nameUniquenessResult)) {
        return nameUniquenessResult;
      }

      // 2. 验证角色ID唯一性
      const characterIdUniquenessResult = await this.validateUniqueness(
        createDto.characterId,
        '角色ID',
        async (id) => {
          return await this.characterRepository.existsByCharacterId(id);
        }
      );
      if (XResultUtils.isFailure(characterIdUniquenessResult)) {
        return characterIdUniquenessResult;
      }

      // 3. 验证服务器角色数量限制（可选）
      const serverLimitResult = await this.validateCountLimit(
        await this.characterRepository.countByServerId(createDto.serverId),
        GAME_CONSTANTS.SERVER.MAX_CHARACTERS,
        '服务器角色数量'
      );
      if (XResultUtils.isFailure(serverLimitResult)) {
        return serverLimitResult;
      }

      // ==================== 业务逻辑执行 ====================

      // 创建角色数据
      const characterData = {
        characterId: createDto.characterId,
        userId: createDto.userId,
        serverId: createDto.serverId,
        openId: createDto.openId,
        name: createDto.name,
        avatar: createDto.avatar || '',
        faceIcon: createDto.faceIcon || GAME_CONSTANTS.CHARACTER.DEFAULT_FACE_ICON,
        cash: GAME_CONSTANTS.CHARACTER.INITIAL_CASH,
        gold: GAME_CONSTANTS.CHARACTER.INITIAL_GOLD,
        energy: GAME_CONSTANTS.CHARACTER.INITIAL_ENERGY,
        loginInfo: {
          createTime: Date.now(),
          loginTime: Date.now(),
        },
      };

      // 安全执行数据库操作
      const createResult = await this.safeExecute(
        () => this.characterRepository.create(characterData),
        '角色创建失败',
        'CHARACTER_CREATE_FAILED'
      );

      if (XResultUtils.isFailure(createResult)) {
        return createResult;
      }

      // 记录成功日志
      this.logger.log(`角色创建成功: ${createDto.characterId}, 用户: ${createDto.userId}`);

      return createResult;
    },
    {
      reason: `创建角色: ${createDto.name}`,
      metadata: {
        userId: createDto.userId,
        serverId: createDto.serverId,
        characterId: createDto.characterId
      }
    }
  );
}
```

## 🎯 其他业务方法示例

### 角色升级方法
```typescript
async levelUpCharacter(characterId: string, userId: string): Promise<XResult<CharacterDocument>> {
  return this.executeBusinessOperation('levelUpCharacter', async () => {
    // 获取角色
    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
    }

    // 批量验证
    const validationResult = await this.validateBatch([
      // 验证所有权
      () => this.validateOwnership(userId, character.userId, '角色'),
      
      // 验证经验值充足
      () => this.validateSufficientResource(
        character.experience,
        this.getRequiredExperience(character.level + 1),
        '经验值'
      ),
      
      // 验证等级上限
      () => this.validateLevelRequirement(
        GAME_CONSTANTS.CHARACTER.MAX_LEVEL,
        character.level + 1,
        '等级上限'
      )
    ]);

    if (XResultUtils.isFailure(validationResult)) {
      return validationResult;
    }

    // 执行升级逻辑...
    return await this.safeExecute(
      () => this.performLevelUp(character),
      '角色升级失败'
    );
  });
}
```

### 购买物品方法
```typescript
async buyItem(characterId: string, itemId: string, quantity: number): Promise<XResult<any>> {
  return this.executeBusinessOperation('buyItem', async () => {
    const character = await this.characterRepository.findByCharacterId(characterId);
    const itemConfig = await this.gameConfig.getItemConfig(itemId);

    // 批量验证
    const validationResult = await this.validateBatch([
      // 验证金币充足
      () => this.validateSufficientResource(
        character.gold,
        itemConfig.price * quantity,
        '金币'
      ),
      
      // 验证背包容量
      () => this.validateCountLimit(
        character.inventory.length,
        GAME_CONSTANTS.INVENTORY.MAX_CAPACITY,
        '背包容量'
      ),
      
      // 验证购买限制
      () => this.validateCountLimit(
        character.dailyPurchases[itemId] || 0,
        itemConfig.dailyLimit,
        '每日购买限制'
      )
    ]);

    if (XResultUtils.isFailure(validationResult)) {
      return validationResult;
    }

    // 执行购买逻辑...
    return await this.safeExecute(
      () => this.performPurchase(character, itemConfig, quantity),
      '购买失败'
    );
  });
}
```

## ✅ 优势总结

1. **简单实用** - 每个方法都有明确的用途，参数简单
2. **业务导向** - 专注于业务逻辑验证，不重复Controller层的工作
3. **统一错误处理** - 所有验证都返回统一的XResult格式
4. **易于组合** - 可以通过validateBatch批量验证
5. **可读性强** - 代码意图清晰，易于维护

这些方法真正解决了游戏业务开发中的常见验证需求，比原来的通用方法更加实用！
