import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PortManager } from './port-manager';

/**
 * 端口管理器模块
 * 
 * 提供全局的端口管理功能，包括：
 * 1. 动态端口计算
 * 2. 端口可用性验证
 * 3. 端口配置管理
 */
@Global()
@Module({
  providers: [
    PortManager,
    {
      provide: 'PORT_CONFIG',
      useFactory: (configService: ConfigService) => ({
        strategy: configService.get('PORT_ALLOCATION_STRATEGY', 'dynamic'),
        rangeSize: parseInt(configService.get('PORT_RANGE_SIZE', '10')),
        validateOnStartup: configService.get('VALIDATE_PORTS_ON_STARTUP', 'true') === 'true',
        autoRetryOnConflict: configService.get('AUTO_RETRY_PORT_ON_CONFLICT', 'false') === 'true',
      }),
      inject: [ConfigService],
    },
  ],
  exports: [PortManager, 'PORT_CONFIG'],
})
export class PortManagerModule {}
