/**
 * 增强版BaseRepository
 * 
 * 核心功能：
 * - 继承原有BaseRepository的所有功能
 * - 提供Schema实例方法的便捷调用
 * - 自动适配Result模式
 * - 支持批量操作和条件查询
 * 
 * 使用场景：
 * - 需要频繁使用Schema实例方法的Repository
 * - 需要复杂业务逻辑验证的场景
 * - 需要统一错误处理的场景
 */

import { Document, Model, FilterQuery, UpdateQuery } from 'mongoose';
import { BaseRepository } from './base-repository';
import { XResult, XResultUtils } from '../types/result.type';

/**
 * 增强版BaseRepository
 * 提供Schema实例方法的便捷调用和Result模式适配
 */
export abstract class EnhancedBaseRepository<T extends Document> extends BaseRepository<T> {
  
  constructor(model: Model<T>) {
    super(model);
  }

  /**
   * 在文档上调用指定的实例方法（Result版本）
   * @param id 文档ID
   * @param methodName 方法名
   * @param args 方法参数
   * @returns XResult<any>
   */
  async callDocumentMethodResult(
    id: string, 
    methodName: string, 
    ...args: any[]
  ): Promise<XResult<any>> {
    try {
      const doc = await this.model.findById(id);
      if (!doc) {
        return XResultUtils.error('文档不存在', 'DOCUMENT_NOT_FOUND');
      }

      const resultMethodName = `${methodName}Result`;
      const method = (doc as any)[resultMethodName];
      
      if (typeof method !== 'function') {
        return XResultUtils.error(
          `方法 ${resultMethodName} 不存在`, 
          'METHOD_NOT_FOUND'
        );
      }

      const result = method.apply(doc, args);
      return result;
      
    } catch (error: any) {
      return XResultUtils.error(
        `调用文档方法失败: ${error.message}`,
        'DOCUMENT_METHOD_CALL_ERROR',
        error
      );
    }
  }

  /**
   * 在文档上调用指定的实例方法（原版本）
   * @param id 文档ID
   * @param methodName 方法名
   * @param args 方法参数
   * @returns XResult<any>
   */
  async callDocumentMethod(
    id: string, 
    methodName: string, 
    ...args: any[]
  ): Promise<XResult<any>> {
    try {
      const doc = await this.model.findById(id);
      if (!doc) {
        return XResultUtils.error('文档不存在', 'DOCUMENT_NOT_FOUND');
      }

      const method = (doc as any)[methodName];
      
      if (typeof method !== 'function') {
        return XResultUtils.error(
          `方法 ${methodName} 不存在`, 
          'METHOD_NOT_FOUND'
        );
      }

      const result = method.apply(doc, args);
      return XResultUtils.ok(result);
      
    } catch (error: any) {
      return XResultUtils.error(
        `调用文档方法失败: ${error.message}`,
        'DOCUMENT_METHOD_CALL_ERROR',
        error
      );
    }
  }

  /**
   * 批量调用文档方法
   * @param filter 查询条件
   * @param methodName 方法名
   * @param args 方法参数
   * @returns XResult<any[]>
   */
  async callDocumentMethodBatch(
    filter: FilterQuery<T>,
    methodName: string,
    ...args: any[]
  ): Promise<XResult<any[]>> {
    try {
      const docs = await this.model.find(filter);
      const results: any[] = [];
      
      for (const doc of docs) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc, args);
            results.push({
              id: doc._id,
              success: true,
              data: result
            });
          } catch (error: any) {
            results.push({
              id: doc._id,
              success: false,
              error: error.message
            });
          }
        }
      }
      
      return XResultUtils.ok(results);
      
    } catch (error: any) {
      return XResultUtils.error(
        `批量调用文档方法失败: ${error.message}`,
        'BATCH_DOCUMENT_METHOD_CALL_ERROR',
        error
      );
    }
  }

  /**
   * 条件调用文档方法
   * 只对满足条件的文档调用方法
   * @param filter 查询条件
   * @param conditionMethod 条件检查方法名
   * @param targetMethod 目标方法名
   * @param args 方法参数
   * @returns XResult<any[]>
   */
  async callDocumentMethodConditional(
    filter: FilterQuery<T>,
    conditionMethod: string,
    targetMethod: string,
    ...args: any[]
  ): Promise<XResult<any[]>> {
    try {
      const docs = await this.model.find(filter);
      const results: any[] = [];
      
      for (const doc of docs) {
        const conditionFn = (doc as any)[conditionMethod];
        const targetFn = (doc as any)[targetMethod];
        
        if (typeof conditionFn === 'function' && typeof targetFn === 'function') {
          try {
            const canExecute = conditionFn.apply(doc);
            if (canExecute) {
              const result = targetFn.apply(doc, args);
              results.push({
                id: doc._id,
                executed: true,
                data: result
              });
            } else {
              results.push({
                id: doc._id,
                executed: false,
                reason: 'Condition not met'
              });
            }
          } catch (error: any) {
            results.push({
              id: doc._id,
              executed: false,
              error: error.message
            });
          }
        }
      }
      
      return XResultUtils.ok(results);
      
    } catch (error: any) {
      return XResultUtils.error(
        `条件调用文档方法失败: ${error.message}`,
        'CONDITIONAL_DOCUMENT_METHOD_CALL_ERROR',
        error
      );
    }
  }

  /**
   * 验证文档数据
   * 调用文档的验证方法
   * @param id 文档ID
   * @returns XResult<ValidationResult>
   */
  async validateDocument(id: string): Promise<XResult<ValidationResult>> {
    try {
      const doc = await this.model.findById(id);
      if (!doc) {
        return XResultUtils.error('文档不存在', 'DOCUMENT_NOT_FOUND');
      }

      // 尝试调用各种验证方法
      const validationMethods = ['validateData', 'validate', 'isValid'];
      
      for (const methodName of validationMethods) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc);
            return XResultUtils.ok({
              isValid: result.isValid !== false,
              errors: result.errors || [],
              method: methodName
            });
          } catch (error: any) {
            // 继续尝试下一个方法
            continue;
          }
        }
      }
      
      return XResultUtils.error('未找到可用的验证方法', 'NO_VALIDATION_METHOD');
      
    } catch (error: any) {
      return XResultUtils.error(
        `文档验证失败: ${error.message}`,
        'DOCUMENT_VALIDATION_ERROR',
        error
      );
    }
  }

  /**
   * 获取文档统计信息
   * 调用文档的统计方法
   * @param id 文档ID
   * @returns XResult<any>
   */
  async getDocumentStats(id: string): Promise<XResult<any>> {
    try {
      const doc = await this.model.findById(id);
      if (!doc) {
        return XResultUtils.error('文档不存在', 'DOCUMENT_NOT_FOUND');
      }

      // 尝试调用各种统计方法
      const statsMethods = ['getStats', 'getStatistics', 'getSummary'];
      
      for (const methodName of statsMethods) {
        const method = (doc as any)[methodName];
        if (typeof method === 'function') {
          try {
            const result = method.apply(doc);
            return XResultUtils.ok(result);
          } catch (error: any) {
            // 继续尝试下一个方法
            continue;
          }
        }
      }
      
      return XResultUtils.error('未找到可用的统计方法', 'NO_STATS_METHOD');
      
    } catch (error: any) {
      return XResultUtils.error(
        `获取文档统计失败: ${error.message}`,
        'DOCUMENT_STATS_ERROR',
        error
      );
    }
  }
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  method: string;
}

/**
 * 文档方法调用结果接口
 */
export interface DocumentMethodResult {
  id: string;
  success?: boolean;
  executed?: boolean;
  data?: any;
  error?: string;
  reason?: string;
}
