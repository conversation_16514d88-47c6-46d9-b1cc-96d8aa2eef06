// Auto-generated from BagBookMark.json
// Generated at: 2025-07-20T12:55:59.114Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface BagBookMarkDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 所有, 消耗品
  defaultUnlockCount: number; // 数量 例: 320, 100 (原: DefaultUnlockCount)
  isShowAll: number; // 是否 例: 1, 0 (原: IsShowAll)
  itemType: number[]; // 类型 例: 0,0,0,0,0,0,0,0,0,0, 1,4,5,0,0,0,0,0,0,0 (原: ItemType)
  maxUnlockCount: number; // 数量 例: 320, 100 (原: MaxUnlockCount)
  price: number; // 价格 例: 0 (原: Price)
  type: number; // 类型 例: 4, 1 (原: Type)
  type1: number; // 类型 例: 0 (原: type_1)
}

// 字段映射：新字段名 -> 原始字段名
export const BagBookMarkFieldMappings = {
  defaultUnlockCount: 'DefaultUnlockCount',
  isShowAll: 'IsShowAll',
  itemType: 'ItemType',
  maxUnlockCount: 'MaxUnlockCount',
  price: 'Price',
  type: 'Type',
  type1: 'type_1',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const BagBookMarkReverseFieldMappings = {
  'DefaultUnlockCount': 'defaultUnlockCount',
  'IsShowAll': 'isShowAll',
  'ItemType': 'itemType',
  'MaxUnlockCount': 'maxUnlockCount',
  'Price': 'price',
  'Type': 'type',
  'type_1': 'type1',
} as const;

export const BagBookMarkMeta = {
  tableName: 'BagBookMark',
  dataFileName: 'BagBookMark.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 9,
  requiredFields: ['id', 'name', 'defaultUnlockCount', 'isShowAll', 'itemType', 'maxUnlockCount', 'price', 'type', 'type1'],
  optionalFields: [],
  renamedFieldsCount: 7,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: BagBookMarkFieldMappings,
  reverseFieldMappings: BagBookMarkReverseFieldMappings,
} as const;

export type BagBookMarkConfigMeta = typeof BagBookMarkMeta;
export type BagBookMarkFieldMapping = typeof BagBookMarkFieldMappings;
export type BagBookMarkReverseFieldMapping = typeof BagBookMarkReverseFieldMappings;
