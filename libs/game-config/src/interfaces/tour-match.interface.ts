// Auto-generated from TourMatch.json
// Generated at: 2025-07-20T12:56:06.955Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface TourMatchDefinition {
  id: number; // 唯一标识符 例: 1, 2
  color1: number; // 数值 例: 40021, 40022 (原: Color1)
  color2: number; // 数值 例: 0, 40026 (原: Color2)
  leagueMax: number; // 数值 例: 91200, 92400 (原: LeagueMax)
  leagueMin: number; // 数值 例: 90100, 91300 (原: LeagueMin)
}

// 字段映射：新字段名 -> 原始字段名
export const TourMatchFieldMappings = {
  color1: 'Color1',
  color2: 'Color2',
  leagueMax: 'LeagueMax',
  leagueMin: 'LeagueMin',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const TourMatchReverseFieldMappings = {
  'Color1': 'color1',
  'Color2': 'color2',
  'LeagueMax': 'leagueMax',
  'LeagueMin': 'leagueMin',
} as const;

export const TourMatchMeta = {
  tableName: 'TourMatch',
  dataFileName: 'TourMatch.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 5,
  requiredFields: ['id', 'color1', 'color2', 'leagueMax', 'leagueMin'],
  optionalFields: [],
  renamedFieldsCount: 4,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: TourMatchFieldMappings,
  reverseFieldMappings: TourMatchReverseFieldMappings,
} as const;

export type TourMatchConfigMeta = typeof TourMatchMeta;
export type TourMatchFieldMapping = typeof TourMatchFieldMappings;
export type TourMatchReverseFieldMapping = typeof TourMatchReverseFieldMappings;
