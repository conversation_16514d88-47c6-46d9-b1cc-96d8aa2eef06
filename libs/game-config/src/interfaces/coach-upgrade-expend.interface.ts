// Auto-generated from CoachUpgradeExpend.json
// Generated at: 2025-07-20T12:55:59.600Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface CoachUpgradeExpendDefinition {
  id: number; // 唯一标识符 例: 1001, 1002
  honor: number; // 数值 例: 100, 120 (原: Honor)
  levelMax: number; // 等级 例: 5, 10 (原: LevelMax)
  levelMin: number; // 等级 例: 1, 6 (原: LevelMin)
  money: number; // 数值 例: 2000000, 3000000 (原: Money)
  quality: number; // 品质 例: 1, 2 (原: Quality)
}

// 字段映射：新字段名 -> 原始字段名
export const CoachUpgradeExpendFieldMappings = {
  honor: 'Honor',
  levelMax: 'LevelMax',
  levelMin: 'LevelMin',
  money: 'Money',
  quality: 'Quality',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const CoachUpgradeExpendReverseFieldMappings = {
  'Honor': 'honor',
  'LevelMax': 'levelMax',
  'LevelMin': 'levelMin',
  'Money': 'money',
  'Quality': 'quality',
} as const;

export const CoachUpgradeExpendMeta = {
  tableName: 'CoachUpgradeExpend',
  dataFileName: 'CoachUpgradeExpend.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 6,
  requiredFields: ['id', 'honor', 'levelMax', 'levelMin', 'money', 'quality'],
  optionalFields: [],
  renamedFieldsCount: 5,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: CoachUpgradeExpendFieldMappings,
  reverseFieldMappings: CoachUpgradeExpendReverseFieldMappings,
} as const;

export type CoachUpgradeExpendConfigMeta = typeof CoachUpgradeExpendMeta;
export type CoachUpgradeExpendFieldMapping = typeof CoachUpgradeExpendFieldMappings;
export type CoachUpgradeExpendReverseFieldMapping = typeof CoachUpgradeExpendReverseFieldMappings;
