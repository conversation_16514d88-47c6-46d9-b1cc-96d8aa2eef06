// Auto-generated from MLS.json
// Generated at: 2025-07-20T12:56:05.189Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface MLSDefinition {
  id: number; // 唯一标识符 例: 1, 2
  text: string; // 字符串 例: 赢下三场比赛且我的总进球数大于11, 赢下三场比赛且我的总失球数小于4
}

export const MLSMeta = {
  tableName: 'MLS',
  dataFileName: 'MLS.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 2,
  requiredFields: ['id', 'text'],
  optionalFields: [],
  renamedFieldsCount: 0,
  hasFieldMappings: false,
  isTableRenamed: false,
} as const;

export type MLSConfigMeta = typeof MLSMeta;
