// Auto-generated from BigFiveCupReward.json
// Generated at: 2025-07-20T12:55:59.335Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface BigFiveCupRewardDefinition {
  id: number; // 唯一标识符 例: 1, 2
  roundName: string; // 名称 例: 预选赛, 1/32决赛（64强） (原: RoundName)
  name: string; // 名称 例: 意甲挑战赛, 德甲挑战赛
  num1: number; // 数值 例: 2, 1 (原: Num1)
  num2: number; // 数值 例: 35, 50 (原: Num2)
  num3: number; // 数值 例: 500000, 800000 (原: Num3)
  num4: number; // 数值 例: 15, 18 (原: Num4)
  num5: number; // 数值 例: 0 (原: Num5)
  reward1: number; // 奖励 例: 90001, 90109 (原: Reward1)
  reward2: number; // 奖励 例: 5 (原: Reward2)
  reward3: number; // 奖励 例: 1 (原: Reward3)
  reward4: number; // 奖励 例: 15 (原: Reward4)
  reward5: number; // 奖励 例: 0 (原: Reward5)
  rewardType1: number; // 类型 例: 0 (原: RewardType1)
  rewardType2: number; // 类型 例: 0 (原: RewardType2)
  rewardType3: number; // 类型 例: 0 (原: RewardType3)
  rewardType4: number; // 类型 例: 0 (原: RewardType4)
  rewardType5: number; // 类型 例: 0 (原: RewardType5)
  round: number; // 数值 例: 2, 3 (原: Round)
  weekDay: number; // 数值 例: 1, 2 (原: WeekDay)
}

// 字段映射：新字段名 -> 原始字段名
export const BigFiveCupRewardFieldMappings = {
  roundName: 'RoundName',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  num4: 'Num4',
  num5: 'Num5',
  reward1: 'Reward1',
  reward2: 'Reward2',
  reward3: 'Reward3',
  reward4: 'Reward4',
  reward5: 'Reward5',
  rewardType1: 'RewardType1',
  rewardType2: 'RewardType2',
  rewardType3: 'RewardType3',
  rewardType4: 'RewardType4',
  rewardType5: 'RewardType5',
  round: 'Round',
  weekDay: 'WeekDay',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const BigFiveCupRewardReverseFieldMappings = {
  'RoundName': 'roundName',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Num4': 'num4',
  'Num5': 'num5',
  'Reward1': 'reward1',
  'Reward2': 'reward2',
  'Reward3': 'reward3',
  'Reward4': 'reward4',
  'Reward5': 'reward5',
  'RewardType1': 'rewardType1',
  'RewardType2': 'rewardType2',
  'RewardType3': 'rewardType3',
  'RewardType4': 'rewardType4',
  'RewardType5': 'rewardType5',
  'Round': 'round',
  'WeekDay': 'weekDay',
} as const;

export const BigFiveCupRewardMeta = {
  tableName: 'BigFiveCupReward',
  dataFileName: 'BigFiveCupReward.json',
  primaryKey: 'id',
  searchFields: ['roundName', 'name'],
  fieldsCount: 20,
  requiredFields: ['id', 'roundName', 'name', 'num1', 'num2', 'num3', 'num4', 'num5', 'reward1', 'reward2', 'reward3', 'reward4', 'reward5', 'rewardType1', 'rewardType2', 'rewardType3', 'rewardType4', 'rewardType5', 'round', 'weekDay'],
  optionalFields: [],
  renamedFieldsCount: 18,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: BigFiveCupRewardFieldMappings,
  reverseFieldMappings: BigFiveCupRewardReverseFieldMappings,
} as const;

export type BigFiveCupRewardConfigMeta = typeof BigFiveCupRewardMeta;
export type BigFiveCupRewardFieldMapping = typeof BigFiveCupRewardFieldMappings;
export type BigFiveCupRewardReverseFieldMapping = typeof BigFiveCupRewardReverseFieldMappings;
