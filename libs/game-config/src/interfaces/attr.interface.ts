// Auto-generated from Attr.json
// Generated at: 2025-07-20T12:55:59.070Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface AttrDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 速度, 弹跳
  aMATRate: number; // 比率 例: 0.08, 1.48 (原: AMATRate)
  aMDFRate: number; // 比率 例: 0 (原: AMDFRate)
  dCATRate: number; // 比率 例: 0 (原: DCATRate)
  dCDFRate: number; // 比率 例: 0.06, 0.95 (原: DCDFRate)
  dLATRate: number; // 比率 例: 1, 0 (原: DLATRate)
  dLDFRate: number; // 比率 例: 5.52, 0.08 (原: DLDFRate)
  dMATRate: number; // 比率 例: 0, 0.48 (原: DMATRate)
  dMDFRate: number; // 比率 例: 0.18, 1 (原: DMDFRate)
  dRATRate: number; // 比率 例: 1, 0 (原: DRATRate)
  dRDFRate: number; // 比率 例: 5.52, 0.08 (原: DRDFRate)
  gKATRate: number; // 比率 例: 0 (原: GKATRate)
  gKDFRate: number; // 比率 例: 0.74, 0.55 (原: GKDFRate)
  mCATRate: number; // 比率 例: 0, 0.17 (原: MCATRate)
  mCDFRate: number; // 比率 例: 0.17, 1.66 (原: MCDFRate)
  mLATRate: number; // 比率 例: 0.56, 0.13 (原: MLATRate)
  mLDFRate: number; // 比率 例: 0, 1.85 (原: MLDFRate)
  mRATRate: number; // 比率 例: 0.74, 0.13 (原: MRATRate)
  mRDFRate: number; // 比率 例: 0, 1.85 (原: MRDFRate)
  sTATRate: number; // 比率 例: 2.04, 0.06 (原: STATRate)
  sTDFRate: number; // 比率 例: 0 (原: STDFRate)
  wLATRate: number; // 比率 例: 3.88, 0.08 (原: WLATRate)
  wLDFRate: number; // 比率 例: 0 (原: WLDFRate)
  wRATRate: number; // 比率 例: 3.9, 0.08 (原: WRATRate)
  wRDFRate: number; // 比率 例: 0 (原: WRDFRate)
}

// 字段映射：新字段名 -> 原始字段名
export const AttrFieldMappings = {
  aMATRate: 'AMATRate',
  aMDFRate: 'AMDFRate',
  dCATRate: 'DCATRate',
  dCDFRate: 'DCDFRate',
  dLATRate: 'DLATRate',
  dLDFRate: 'DLDFRate',
  dMATRate: 'DMATRate',
  dMDFRate: 'DMDFRate',
  dRATRate: 'DRATRate',
  dRDFRate: 'DRDFRate',
  gKATRate: 'GKATRate',
  gKDFRate: 'GKDFRate',
  mCATRate: 'MCATRate',
  mCDFRate: 'MCDFRate',
  mLATRate: 'MLATRate',
  mLDFRate: 'MLDFRate',
  mRATRate: 'MRATRate',
  mRDFRate: 'MRDFRate',
  sTATRate: 'STATRate',
  sTDFRate: 'STDFRate',
  wLATRate: 'WLATRate',
  wLDFRate: 'WLDFRate',
  wRATRate: 'WRATRate',
  wRDFRate: 'WRDFRate',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const AttrReverseFieldMappings = {
  'AMATRate': 'aMATRate',
  'AMDFRate': 'aMDFRate',
  'DCATRate': 'dCATRate',
  'DCDFRate': 'dCDFRate',
  'DLATRate': 'dLATRate',
  'DLDFRate': 'dLDFRate',
  'DMATRate': 'dMATRate',
  'DMDFRate': 'dMDFRate',
  'DRATRate': 'dRATRate',
  'DRDFRate': 'dRDFRate',
  'GKATRate': 'gKATRate',
  'GKDFRate': 'gKDFRate',
  'MCATRate': 'mCATRate',
  'MCDFRate': 'mCDFRate',
  'MLATRate': 'mLATRate',
  'MLDFRate': 'mLDFRate',
  'MRATRate': 'mRATRate',
  'MRDFRate': 'mRDFRate',
  'STATRate': 'sTATRate',
  'STDFRate': 'sTDFRate',
  'WLATRate': 'wLATRate',
  'WLDFRate': 'wLDFRate',
  'WRATRate': 'wRATRate',
  'WRDFRate': 'wRDFRate',
} as const;

export const AttrMeta = {
  tableName: 'Attr',
  dataFileName: 'Attr.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 26,
  requiredFields: ['id', 'name', 'aMATRate', 'aMDFRate', 'dCATRate', 'dCDFRate', 'dLATRate', 'dLDFRate', 'dMATRate', 'dMDFRate', 'dRATRate', 'dRDFRate', 'gKATRate', 'gKDFRate', 'mCATRate', 'mCDFRate', 'mLATRate', 'mLDFRate', 'mRATRate', 'mRDFRate', 'sTATRate', 'sTDFRate', 'wLATRate', 'wLDFRate', 'wRATRate', 'wRDFRate'],
  optionalFields: [],
  renamedFieldsCount: 24,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: AttrFieldMappings,
  reverseFieldMappings: AttrReverseFieldMappings,
} as const;

export type AttrConfigMeta = typeof AttrMeta;
export type AttrFieldMapping = typeof AttrFieldMappings;
export type AttrReverseFieldMapping = typeof AttrReverseFieldMappings;
