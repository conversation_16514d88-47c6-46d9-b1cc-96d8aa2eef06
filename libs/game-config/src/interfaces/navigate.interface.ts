// Auto-generated from Navigate.json
// Generated at: 2025-07-20T12:56:05.388Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface NavigateDefinition {
  id: number; // 唯一标识符 例: 1, 2
  bz: string; // 字符串 例: 球员列表, 球员技能界面
  function: number; // 数值 例: 1, 2 (原: Function)
  icon: number; // 图标 例: 0, 50604 (原: Icon)
  tap: number; // 数值 例: 0, 1 (原: Tap)
}

// 字段映射：新字段名 -> 原始字段名
export const NavigateFieldMappings = {
  function: 'Function',
  icon: 'Icon',
  tap: 'Tap',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const NavigateReverseFieldMappings = {
  'Function': 'function',
  'Icon': 'icon',
  'Tap': 'tap',
} as const;

export const NavigateMeta = {
  tableName: 'Navigate',
  dataFileName: 'Navigate.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 5,
  requiredFields: ['id', 'bz', 'function', 'icon', 'tap'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: NavigateFieldMappings,
  reverseFieldMappings: NavigateReverseFieldMappings,
} as const;

export type NavigateConfigMeta = typeof NavigateMeta;
export type NavigateFieldMapping = typeof NavigateFieldMappings;
export type NavigateReverseFieldMapping = typeof NavigateReverseFieldMappings;
