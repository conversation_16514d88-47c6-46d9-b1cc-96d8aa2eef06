import { Injectable, Logger } from '@nestjs/common';
import { InjectedContext } from '@libs/common/types';
import {
  getInjectedContext,
  getTrustedUserId,
  getTrustedCharacterId,
  getTrustedServerId,
  prepareForMicroserviceCall
} from '@libs/common/utils';

/**
 * 上下文提取器服务 v2.0
 *
 * 🎯 优化内容：
 * - 适配新的 injectedContext 结构
 * - 提供类型安全的上下文提取
 * - 支持向后兼容的数据读取
 * - 增强的调试和日志功能
 */
@Injectable()
export class ContextExtractorService {
  private readonly logger = new Logger(ContextExtractorService.name);

  /**
   * 从调用数据中提取区服ID
   * 🎯 优化：使用统一的工具函数，保持一致性
   */
  extractServerId(data?: any): string | null {
    if (!data) {
      return this.getDefaultServerId();
    }

    // 🎯 使用统一的工具函数提取区服ID
    const trustedServerId = getTrustedServerId(data);
    if (trustedServerId) {
      this.logger.debug(`✅ 提取到区服ID: ${trustedServerId}`);
      return trustedServerId;
    }

    // 6. 返回默认区服ID
    const defaultServerId = this.getDefaultServerId();
    if (defaultServerId) {
      this.logger.debug(`⚙️ 使用默认区服ID: ${defaultServerId}`);
    } else {
      this.logger.warn(`⚠️ 无法提取区服ID，所有方法都失败了`);
    }
    return defaultServerId;
  }

  /**
   * 🎯 从调用数据中提取用户ID（使用工具函数）
   * @deprecated 建议直接使用 getTrustedUserId 工具函数
   */
  extractUserId(data?: any): string | null {
    return getTrustedUserId(data);
  }

  /**
   * 🎯 从调用数据中提取角色ID（使用工具函数）
   * @deprecated 建议直接使用 getTrustedCharacterId 工具函数
   */
  extractCharacterId(data?: any): string | null {
    return getTrustedCharacterId(data);
  }

  /**
   * 🎯 从调用数据中提取完整的注入上下文（使用工具函数）
   * @deprecated 建议直接使用 getInjectedContext 工具函数
   */
  extractInjectedContext(data?: any): InjectedContext | null {
    return getInjectedContext(data);
  }

  /**
   * 获取默认区服ID
   */
  private getDefaultServerId(): string | null {
    return process.env.SERVER_ID || null;
  }
}