import { Injectable, Inject, Optional, Logger } from '@nestjs/common';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';
import { timeout } from 'rxjs/operators';
import { MicroserviceKitConfig } from '../config/microservice.config';
import { MicroserviceName } from '@libs/shared/constants';

// 导入ServiceMesh组件（内部引用）
import { ServerAwareRegistryService } from '../registry/server-aware-registry.service';
import { GlobalServiceRegistryService } from '../registry/global-service-registry.service';
// 导入连接池服务
import { ConnectionPoolService } from './connection-pool.service';
// 导入工具服务
import { ContextExtractorService } from './utils/context-extractor.service';
import { PortManager } from '@libs/common/port-manager';

// 🚀 使用统一的服务实例接口定义
import {
  UniversalServiceInstance,
  ServerAwareServiceInstance,
  GlobalServiceInstance,
  isServerAwareServiceInstance,
  isGlobalServiceInstance,
  createGlobalServiceInstance,
  createServerAwareServiceInstance
} from '../interfaces/service-instance.interfaces';



/**
 * 微服务客户端服务
 * 提供统一的微服务调用接口
 */
@Injectable()
export class MicroserviceClientService {
  private readonly logger = new Logger(MicroserviceClientService.name);
  private clients = new Map<MicroserviceName, ClientProxy>();
  private connectedServices: MicroserviceName[];
  private clientOptions: any;

  constructor(
      @Inject('MICROSERVICE_CONFIG') private config: MicroserviceKitConfig,
      @Inject('CONNECTED_SERVICES') connectedServices: MicroserviceName[],
      // 🚀 新增：客户端选项配置
      @Inject('MICROSERVICE_CLIENT_OPTIONS') clientOptions: any,
      // 🚀 ServiceMesh组件注入
      @Optional() private serverAwareRegistry?: ServerAwareRegistryService,
      @Optional() private globalRegistry?: GlobalServiceRegistryService,
      // 新增：可选注入连接池服务
      @Optional() private connectionPool?: ConnectionPoolService,
      // 新增：可选注入工具服务
      @Optional() private contextExtractor?: ContextExtractorService,
  ) {
    this.connectedServices = connectedServices;
    this.clientOptions = clientOptions;
    this.initializeClients();
  }

  /**
   * 初始化客户端连接
   */
  private initializeClients() {
    // 只初始化已连接的服务
    this.connectedServices.forEach(serviceName => {
      const service = this.config.services[serviceName];
      if (!service) {
        console.warn(`⚠️ 服务配置未找到: ${serviceName}`);
        return;
      }

      try {
        // 使用 ClientProxyFactory 直接创建客户端
        const client = ClientProxyFactory.create({
          transport: service.transport,
          options: service.options,
        });

        this.clients.set(serviceName, client);
        console.log(`✅ 微服务客户端已创建: ${serviceName} -> ${service.name}`);
        console.log(`🔗 连接配置: ${JSON.stringify(service.options)}`);
      } catch (error) {
        console.error(`❌ 微服务客户端创建失败: ${serviceName}`, error);
      }
    });
  }

  /**
   * 获取指定服务的客户端
   */
  getClient(serviceName: MicroserviceName): ClientProxy {
    const client = this.clients.get(serviceName);
    if (!client) {
      const availableServices = this.getAvailableServices().join(', ');
      throw new Error(
        `微服务客户端未找到: ${serviceName}。` +
        `可用服务: [${availableServices}]。` +
        `请检查服务是否已在模块中注册。`
      );
    }
    return client;
  }

  /**
   * 🚀 统一调用方法（智能路由版 - 区分全局服务和区服服务）
   * 新架构：根据服务类型智能选择调用方式
   */
  async call<T = any>(serviceName: MicroserviceName, pattern: string, payload?: any): Promise<T> {
    // 🎯 检查服务白名单（如果配置了的话）
    if (!this.isServiceAllowed(serviceName)) {
      throw new Error(`🚨 服务 ${serviceName} 不在允许的服务白名单中`);
    }

    // 🧠 智能判断服务类型
    const isGlobalService = this.isGlobalService(serviceName);

    if (isGlobalService) {
      // 🌍 全局服务调用
      this.logger.log(`🌍 全局服务调用: ${serviceName}.${pattern}`);
      return this.callGlobal(serviceName, pattern, payload);
    } else {
      // 🏰 区服服务调用
      const serverId = this.contextExtractor?.extractServerId(payload);

      if (!serverId) {
        const defaultServerId = this.getDefaultServerId();
        this.logger.log(`🎯 未提供区服ID，使用默认区服: ${defaultServerId} for ${serviceName}.${pattern}`);
        return this.callServerAware(serviceName, defaultServerId, pattern, payload);
      }
      return this.callServerAware(serviceName, serverId, pattern, payload);
    }
  }

  /**
   * 🌍 全局服务调用
   */
  private async callGlobal<T>(
    serviceName: MicroserviceName,
    pattern: string,
    payload?: any
  ): Promise<T> {
    this.logger.log(`🌍 全局服务调用: ${serviceName}.${pattern}`);

    let instance;

    // 🚀 优先使用ServiceMesh的全局服务注册中心（集成负载均衡）
    if (this.globalRegistry) {
      this.logger.debug(`🌍 使用ServiceMesh全局负载均衡: ${serviceName}`);
      instance = await this.globalRegistry.selectInstance(serviceName, 'round-robin');

      if (instance) {
        this.logger.debug(`🌍 ServiceMesh选择全局服务实例: ${instance.id} (${instance.host}:${instance.port})`);
        // 🚀 直接使用标准接口，无需转换
        return this.callInstance(instance, pattern, payload);
      }
    }

    // 🔧 Fallback：使用配置中的默认实例（当ServiceMesh不可用时）
    this.logger.warn(`⚠️ ServiceMesh全局注册中心不可用，使用fallback逻辑: ${serviceName}`);
    const serviceConfig = this.config.services[serviceName];
    if (!serviceConfig) {
      const error = new Error(`🚨 服务配置未找到: ${serviceName}`);
      this.logger.error(error.message);
      throw error;
    }

    // 🚀 使用标准工厂函数创建全局服务实例
    const fallbackInstance = createGlobalServiceInstance({
      serviceName,
      host: 'localhost', // 🔧 全局服务使用localhost
      port: this.getServicePort(serviceName, 'global', 0), // ✅ 支持动态端口计算
      metadata: { fallback: true, reason: 'ServiceMesh不可用' }
    });

    this.logger.debug(`🌍 Fallback选择全局服务实例: ${fallbackInstance.id} (${fallbackInstance.host}:${fallbackInstance.port})`);
    return this.callInstance(fallbackInstance, pattern, payload);
  }

  /**
   * 🧠 判断是否为全局服务
   */
  private isGlobalService(serviceName: MicroserviceName): boolean {
    // 🧠 根据服务名称判断服务类型
    // Auth服务是全局服务，其他服务是区服服务
    const globalServices = ['auth', 'notification'];
    return globalServices.includes(serviceName);
  }



  /**
   * 🔧 获取服务端口（支持动态端口）
   */
  private getServicePort(serviceName: MicroserviceName, serverId?: string, instanceId: number = 0): number {
    try {
      // 使用PortManager计算动态端口
      return PortManager.calculatePort(serviceName, serverId, instanceId);
    } catch (error) {
      // 降级到传统端口配置
      this.logger.warn(`⚠️ 动态端口计算失败，使用固定端口配置: ${error.message}`);

      const serviceNameUpper = serviceName.toUpperCase();
      const envPort = process.env[`${serviceNameUpper}_PORT`] ||
                     process.env.PORT;

      if (envPort) {
        const port = parseInt(envPort.toString());
        if (!isNaN(port) && port > 0) {
          return port;
        }
      }

      return this.getDefaultPortByService(serviceName);
    }
  }

  /**
   * 获取服务的默认端口
   */
  private getDefaultPortByService(serviceName: string): number {
    const defaultPorts: Record<string, number> = {
      'auth': 3001,
      'character': 3002,
      'hero': 3003,
      'economy': 3004,
      'activity': 3005,
      'match': 3006,
      'guild': 3007,
      'social': 3008,
      'notification': 3009,
      'gateway': 3000,
    };
    return defaultPorts[serviceName] || 3000;
  }

  /**
   * 🚀 区服感知调用（使用ServiceMesh负载均衡）
   * 新架构：优先使用ServiceMesh负载均衡，fallback到简单选择
   */
  private async callServerAware<T>(
    serviceName: MicroserviceName,
    serverId: string,
    pattern: string,
    payload?: any
  ): Promise<T> {
    this.logger.log(`🎯 区服感知调用: ${serviceName}.${pattern}@${serverId}`);

    let instance;

    // 🚀 优先使用ServiceMesh的区服感知注册中心（集成负载均衡）
    if (this.serverAwareRegistry) {
      this.logger.debug(`🎯 使用ServiceMesh区服感知负载均衡: ${serviceName}@${serverId}`);
      instance = await this.serverAwareRegistry.selectInstance(serviceName, serverId, 'round-robin');

      if (instance) {
        this.logger.debug(`🎯 ServiceMesh选择区服实例: ${instance.instanceName} (${instance.host}:${instance.port})`);

        // 🚀 直接使用标准接口，无需转换
        return this.callInstance(instance, pattern, payload);
      }
    }

    // 🔧 Fallback：使用简单实例选择（当ServiceMesh不可用时）
    this.logger.warn(`⚠️ ServiceMesh负载均衡器不可用，使用fallback逻辑: ${serviceName}@${serverId}`);
    instance = await this.fallbackSelectInstance(serviceName, serverId);

    if (!instance) {
      const error = new Error(`🚨 未找到健康的服务实例: ${serviceName}@${serverId}`);
      this.logger.error(error.message);
      throw error;
    }

    return this.callInstance(instance, pattern, payload);
  }

  /**
   * 获取所有可用的服务列表
   */
  getAvailableServices(): MicroserviceName[] {
    return Array.from(this.clients.keys());
  }

  /**
   * 检查服务是否可用
   */
  isServiceAvailable(serviceName: MicroserviceName): boolean {
    return this.clients.has(serviceName);
  }

  /**
   * 调用特定的服务实例（优化版本，支持连接池）
   * 使用标准 UniversalServiceInstance 接口
   */
  async callInstance<T = any>(instance: UniversalServiceInstance, pattern: string, payload?: any): Promise<T> {
    try {
      let client: ClientProxy;
      let shouldCloseClient = false;

      // 使用连接池（如果可用）
      if (this.connectionPool) {
        client = await this.connectionPool.getConnection(instance);
      } else {
        // 降级到原有逻辑：创建临时客户端，使用与服务相同的传输配置
        const serviceConfig = this.config.services[instance.serviceName];
        if (!serviceConfig) {
          throw new Error(`服务配置未找到: ${instance.serviceName}`);
        }

        client = ClientProxyFactory.create({
          transport: serviceConfig.transport,
          options: serviceConfig.options,
        });
        shouldCloseClient = true;
      }

      // 设置超时时间
      const timeoutMs = instance.serviceName === 'auth' ? 3000 : 5000;

      const result = await client.send<T>(pattern, payload)
        .pipe(timeout(timeoutMs))
        .toPromise();

      // 🚀 使用统一的实例标识符
      const instanceId = isServerAwareServiceInstance(instance) ? instance.instanceName : instance.id;
      this.logger.log(`📡 实例调用成功: ${instanceId}.${pattern}`);

      // 如果是临时客户端，需要关闭
      if (shouldCloseClient) {
        await client.close();
      }

      return result;
    } catch (error) {
      // 🚀 使用统一的实例标识符
      const instanceId = isServerAwareServiceInstance(instance) ? instance.instanceName : instance.id;
      this.logger.error(`❌ 实例调用失败: ${instanceId}.${pattern}`, error);

      // 更新连接池错误统计
      if (this.connectionPool) {
        const instanceKey = `${instance.host}:${instance.port}`;
        this.connectionPool.updateErrorStats(instanceKey);
      }

      throw error;
    }
  }

  /**
   * 批量调用多个服务
   */
  async callMultiple<T = any>(
      calls: Array<{ serviceName: MicroserviceName; pattern: string; payload?: any }>
  ): Promise<T[]> {
    const promises = calls.map(call =>
        this.call<T>(call.serviceName, call.pattern, call.payload)
    );
    return Promise.all(promises);
  }

  /**
   * 降级选择实例（当负载均衡器不可用时）
   * 返回标准的 ServerAwareServiceInstance
   */
  private async fallbackSelectInstance(serviceName: MicroserviceName, serverId: string): Promise<ServerAwareServiceInstance | null> {
    if (!this.serverAwareRegistry) {
      return null;
    }

    try {
      const instances = await this.serverAwareRegistry.getHealthyInstances(serviceName, serverId);
      if (!instances || instances.length === 0) {
        return null;
      }

      // 简单选择第一个实例
      return instances[0];
    } catch (error) {
      this.logger.error(`降级选择实例失败: ${serviceName}@${serverId}`, error);
      return null;
    }
  }

  /**
   * 🚀 检查服务是否在白名单中
   */
  private isServiceAllowed(serviceName: MicroserviceName): boolean {
    // 如果没有配置白名单，则允许所有已连接的服务
    return this.connectedServices.includes(serviceName);
  }

  /**
   * 🚀 获取默认区服ID
   */
  private getDefaultServerId(): string {
    return process.env.SERVER_ID || 'server_001';
  }
}
