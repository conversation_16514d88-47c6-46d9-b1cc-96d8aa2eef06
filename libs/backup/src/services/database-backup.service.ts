import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import { BackupResult, RestoreResult, RestoreOptions, BackupMetadata } from '../interfaces/backup.interface';
import { CompressionService } from './compression.service';
import { EncryptionService } from './encryption.service';

const execAsync = promisify(exec);

@Injectable()
export class DatabaseBackupService {
  private readonly logger = new Logger(DatabaseBackupService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly compressionService: CompressionService,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * 创建数据库备份
   */
  async createBackup(backupId: string): Promise<BackupResult> {
    const timestamp = new Date().toISOString();
    const backupPath = this.getBackupPath(backupId, 'mongodb');

    try {
      this.logger.log(`开始创建MongoDB备份: ${backupId}`);

      // 确保备份目录存在
      await fs.mkdir(backupPath, { recursive: true });

      // 使用 mongodump 创建备份
      const dumpCommand = this.buildMongoDumpCommand(backupPath);
      this.logger.debug(`执行命令: ${dumpCommand}`);
      
      const { stdout, stderr } = await execAsync(dumpCommand);
      if (stderr && !stderr.includes('done dumping')) {
        this.logger.warn(`mongodump警告: ${stderr}`);
      }

      // 压缩备份文件
      let finalPath = backupPath;
      const backupConfig = this.configService.get('backup');
      if (backupConfig?.compressionEnabled) {
        finalPath = await this.compressionService.compressDirectory(backupPath);
        // 删除原始目录
        await fs.rm(backupPath, { recursive: true, force: true });
      }

      // 加密备份文件（可选）
      if (this.configService.get<boolean>('backup.encryptionEnabled', false)) {
        finalPath = await this.encryptionService.encryptFile(finalPath);
      }

      const metadata = await this.generateBackupMetadata(finalPath, 'mongodb');

      this.logger.log(`MongoDB备份完成: ${backupId}, 大小: ${this.formatBytes(metadata.size)}`);

      return {
        id: backupId,
        type: 'mongodb',
        status: 'success',
        path: finalPath,
        size: metadata.size,
        timestamp,
        metadata,
      };

    } catch (error) {
      this.logger.error(`MongoDB备份失败: ${backupId}`, error);
      
      // 清理失败的备份文件
      try {
        await fs.rm(backupPath, { recursive: true, force: true });
      } catch (cleanupError) {
        this.logger.warn(`清理失败备份文件时出错: ${cleanupError.message}`);
      }

      return {
        id: backupId,
        type: 'mongodb',
        status: 'failed',
        error: error.message,
        timestamp,
      };
    }
  }

  /**
   * 创建增量备份
   */
  async createIncrementalBackup(backupId: string, since: Date): Promise<BackupResult> {
    const timestamp = new Date().toISOString();
    const backupPath = this.getBackupPath(backupId, 'mongodb-incremental');

    try {
      this.logger.log(`开始创建MongoDB增量备份: ${backupId}, 起始时间: ${since.toISOString()}`);

      // 确保备份目录存在
      await fs.mkdir(backupPath, { recursive: true });

      // 执行真正的增量备份（基于集合和时间戳查询）
      const result = await this.executeIncrementalBackup(backupPath, since);

      // 压缩和加密
      let finalPath = await this.processBackupFile(backupPath);

      const metadata = await this.generateBackupMetadata(finalPath, 'mongodb-incremental');
      metadata.collections = result.collections;
      metadata.documentsBackedUp = result.totalDocuments;
      metadata.since = since.toISOString();

      this.logger.log(`MongoDB增量备份完成: ${backupId}, 大小: ${this.formatBytes(metadata.size)}, 集合数: ${result.collections.length}, 文档数: ${result.totalDocuments}`);

      return {
        id: backupId,
        type: 'mongodb',
        status: 'success',
        path: finalPath,
        size: metadata.size,
        timestamp,
        metadata,
      };

    } catch (error) {
      this.logger.error(`MongoDB增量备份失败: ${backupId}`, error);
      
      return {
        id: backupId,
        type: 'mongodb',
        status: 'failed',
        error: error.message,
        timestamp,
      };
    }
  }

  /**
   * 恢复数据库
   */
  async restore(backupId: string, options: RestoreOptions): Promise<RestoreResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始恢复MongoDB备份: ${backupId}`);

      // 获取备份文件路径
      const backupPath = await this.getBackupFilePath(backupId);
      
      // 下载备份文件（如果在云存储中）
      const localPath = await this.downloadBackupIfNeeded(backupPath);

      // 解密备份文件
      const decryptedPath = await this.decryptBackupIfNeeded(localPath);

      // 解压备份文件
      const extractedPath = await this.extractBackupIfNeeded(decryptedPath);

      // 执行恢复
      const restoreCommand = this.buildMongoRestoreCommand(extractedPath, options);
      this.logger.debug(`执行恢复命令: ${restoreCommand}`);
      
      const { stdout, stderr } = await execAsync(restoreCommand);
      if (stderr && !stderr.includes('done')) {
        this.logger.warn(`mongorestore警告: ${stderr}`);
      }

      // 清理临时文件
      await this.cleanupTempFiles([localPath, decryptedPath, extractedPath]);

      const duration = Date.now() - startTime;
      this.logger.log(`MongoDB恢复完成: ${backupId}, 耗时: ${duration}ms`);

      return {
        id: backupId,
        type: 'mongodb',
        status: 'success',
        timestamp: new Date().toISOString(),
        duration,
      };

    } catch (error) {
      this.logger.error(`MongoDB恢复失败: ${backupId}`, error);
      
      return {
        id: backupId,
        type: 'mongodb',
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
      };
    }
  }

  // 私有方法
  private buildMongoDumpCommand(outputPath: string): string {
    const mongoConfig = this.configService.get('backup.mongodb');
    const uri = mongoConfig?.uri || this.configService.get<string>('MONGODB_URI');
    const excludeCollections = this.configService.get<string[]>('backup.database.excludeCollections', []);
    
    let command = `mongodump --uri="${uri}" --out="${outputPath}" --gzip`;

    // 注意：--oplog选项只能用于完整数据库备份，不能与指定数据库一起使用
    // 如果需要oplog支持，需要备份整个MongoDB实例而不是特定数据库

    // 排除指定集合
    for (const collection of excludeCollections) {
      command += ` --excludeCollection="${collection}"`;
    }

    return command;
  }

  private buildIncrementalDumpCommand(outputPath: string, since: Date): string {
    // 增量备份不使用单一命令，而是为每个集合单独执行
    // 这个方法将被重构为支持集合级别的增量备份
    throw new Error('增量备份需要使用 executeIncrementalBackup 方法');
  }

  /**
   * 执行真正的增量备份
   * 为每个集合单独执行基于时间戳的查询备份
   */
  private async executeIncrementalBackup(outputPath: string, since: Date): Promise<{
    collections: string[];
    totalDocuments: number;
  }> {
    const mongoConfig = this.configService.get('backup.mongodb');
    const uri = mongoConfig?.uri || this.configService.get<string>('MONGODB_URI');
    const sinceISOString = since.toISOString();

    // 定义需要进行增量备份的集合
    // 这些集合应该有 createdAt 或 updatedAt 时间戳字段
    const collectionsToBackup = [
      'users',
      'clubs',
      'players',
      'matches',
      'cards',
      'notifications',
      'sessions',
      'logs'
    ];

    const backedUpCollections: string[] = [];
    let totalDocuments = 0;

    for (const collection of collectionsToBackup) {
      try {
        // 构建增量查询：查找自指定时间以来创建或更新的文档
        const query = JSON.stringify({
          $or: [
            { createdAt: { $gte: { $date: sinceISOString } } },
            { updatedAt: { $gte: { $date: sinceISOString } } }
          ]
        });

        // 为每个集合单独执行 mongodump
        const command = `mongodump --uri="${uri}" --collection="${collection}" --out="${outputPath}" --query='${query}'`;

        this.logger.debug(`备份集合 ${collection}: ${command}`);

        const { stdout, stderr } = await execAsync(command);

        // 检查是否有文档被备份
        const documentMatch = stdout.match(/(\d+) document/);
        const documentCount = documentMatch ? parseInt(documentMatch[1]) : 0;

        if (documentCount > 0) {
          backedUpCollections.push(collection);
          totalDocuments += documentCount;
          this.logger.log(`集合 ${collection} 增量备份完成: ${documentCount} 个文档`);
        } else {
          this.logger.debug(`集合 ${collection} 无增量数据`);
        }

        if (stderr && !stderr.includes('done dumping')) {
          this.logger.warn(`集合 ${collection} 备份警告: ${stderr}`);
        }

      } catch (error) {
        // 集合可能不存在或无权限访问，记录警告但继续其他集合
        this.logger.warn(`集合 ${collection} 备份失败: ${error.message}`);
      }
    }

    this.logger.log(`增量备份完成: ${backedUpCollections.length} 个集合, 总计 ${totalDocuments} 个文档`);

    return {
      collections: backedUpCollections,
      totalDocuments
    };
  }

  private buildMongoRestoreCommand(inputPath: string, options: RestoreOptions): string {
    const mongoConfig = this.configService.get('backup.mongodb');
    const uri = options.targetDatabase || mongoConfig?.uri || this.configService.get<string>('MONGODB_URI');
    let command = `mongorestore --uri="${uri}" --gzip`;
    
    if (options.dropBeforeRestore) {
      command += ' --drop';
    }

    // 如果是oplog备份，需要特殊处理
    const oplogPath = path.join(inputPath, 'oplog.bson');
    if (this.fileExists(oplogPath)) {
      command += ' --oplogReplay';
    }

    command += ` "${inputPath}"`;
    return command;
  }

  private getBackupPath(backupId: string, type: string): string {
    const basePath = this.configService.get<string>('backup.localPath', '/app/backups');
    return path.join(basePath, type, backupId);
  }

  private async processBackupFile(backupPath: string): Promise<string> {
    let finalPath = backupPath;

    // 压缩
    if (this.configService.get<boolean>('backup.compressionEnabled', true)) {
      finalPath = await this.compressionService.compressDirectory(backupPath);
      await fs.rm(backupPath, { recursive: true, force: true });
    }

    // 加密
    if (this.configService.get<boolean>('backup.encryptionEnabled', false)) {
      finalPath = await this.encryptionService.encryptFile(finalPath);
    }

    return finalPath;
  }

  private async generateBackupMetadata(filePath: string, type: string): Promise<BackupMetadata> {
    const stats = await fs.stat(filePath);
    const checksum = await this.calculateFileChecksum(filePath);

    return {
      size: stats.size,
      checksum,
      compression: this.configService.get<boolean>('backup.compressionEnabled', true) ? 'gzip' : 'none',
      encryption: this.configService.get<boolean>('backup.encryptionEnabled', false),
      collections: await this.getCollectionNames(),
      // 增量备份相关字段将在调用方设置
      documentsBackedUp: undefined,
      since: undefined,
    };
  }

  private async calculateFileChecksum(filePath: string): Promise<string> {
    const hash = crypto.createHash('sha256');
    const data = await fs.readFile(filePath);
    hash.update(data);
    return hash.digest('hex');
  }

  private async getCollectionNames(): Promise<string[]> {
    // 这里应该连接到MongoDB获取集合名称
    // 暂时返回空数组
    return [];
  }

  private async getBackupFilePath(backupId: string): Promise<string> {
    // 从元数据服务获取备份文件路径
    // 这里需要实现实际的逻辑
    return '';
  }

  private async downloadBackupIfNeeded(backupPath: string): Promise<string> {
    // 如果备份在云存储中，下载到本地
    return backupPath;
  }

  private async decryptBackupIfNeeded(filePath: string): Promise<string> {
    // 加密功能已简化
    return filePath;
  }

  private async extractBackupIfNeeded(filePath: string): Promise<string> {
    const backupConfig = this.configService.get('backup');
    if (backupConfig?.compressionEnabled) {
      return await this.compressionService.extractArchive(filePath);
    }
    return filePath;
  }

  private async cleanupTempFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        if (filePath && await this.fileExists(filePath)) {
          await fs.rm(filePath, { recursive: true, force: true });
        }
      } catch (error) {
        this.logger.warn(`清理临时文件失败: ${filePath}`, error);
      }
    }
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
