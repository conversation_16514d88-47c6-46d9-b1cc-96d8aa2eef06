import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as crypto from 'crypto';

@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 16; // 128 bits
  private readonly tagLength = 16; // 128 bits

  constructor(private readonly configService: ConfigService) {}

  /**
   * 加密文件
   */
  async encryptFile(filePath: string): Promise<string> {
    const outputPath = `${filePath}.enc`;

    try {
      this.logger.debug(`加密文件: ${filePath} -> ${outputPath}`);

      const key = this.getEncryptionKey();
      const iv = crypto.randomBytes(this.ivLength);

      // 使用正确的Node.js crypto API for GCM mode
      const cipher = crypto.createCipheriv(this.algorithm, key, iv);
      cipher.setAAD(Buffer.from(filePath)); // 使用文件路径作为附加认证数据

      const data = await fs.readFile(filePath);

      const encrypted = Buffer.concat([
        cipher.update(data),
        cipher.final()
      ]);

      const tag = cipher.getAuthTag();

      // 文件格式: IV (16 bytes) + Tag (16 bytes) + Encrypted Data
      const result = Buffer.concat([iv, tag, encrypted]);
      await fs.writeFile(outputPath, result);

      this.logger.debug(`文件加密完成: ${outputPath}`);
      return outputPath;

    } catch (error) {
      this.logger.error(`加密文件失败: ${filePath}`, error);
      throw new Error(`加密失败: ${error.message}`);
    }
  }

  /**
   * 解密文件
   */
  async decryptFile(filePath: string): Promise<string> {
    const outputPath = filePath.replace(/\.enc$/, '');
    
    try {
      this.logger.debug(`解密文件: ${filePath} -> ${outputPath}`);
      
      const key = this.getEncryptionKey();
      const data = await fs.readFile(filePath);
      
      // 解析文件格式: IV (16 bytes) + Tag (16 bytes) + Encrypted Data
      const iv = data.slice(0, this.ivLength);
      const tag = data.slice(this.ivLength, this.ivLength + this.tagLength);
      const encrypted = data.slice(this.ivLength + this.tagLength);
      
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
      decipher.setAuthTag(tag);
      decipher.setAAD(Buffer.from(outputPath)); // 使用原始文件路径作为AAD
      
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ]);
      
      await fs.writeFile(outputPath, decrypted);

      this.logger.debug(`文件解密完成: ${outputPath}`);
      return outputPath;

    } catch (error) {
      this.logger.error(`解密文件失败: ${filePath}`, error);
      throw new Error(`解密失败: ${error.message}`);
    }
  }

  /**
   * 加密字符串
   */
  encryptString(text: string): string {
    try {
      const key = this.getEncryptionKey();
      const iv = crypto.randomBytes(this.ivLength);
      
      const cipher = crypto.createCipheriv(this.algorithm, key, iv);
      
      const encrypted = Buffer.concat([
        cipher.update(text, 'utf8'),
        cipher.final()
      ]);
      
      const tag = cipher.getAuthTag();
      
      // 返回格式: base64(IV + Tag + Encrypted)
      const result = Buffer.concat([iv, tag, encrypted]);
      return result.toString('base64');

    } catch (error) {
      this.logger.error('加密字符串失败', error);
      throw new Error(`加密失败: ${error.message}`);
    }
  }

  /**
   * 解密字符串
   */
  decryptString(encryptedText: string): string {
    try {
      const key = this.getEncryptionKey();
      const data = Buffer.from(encryptedText, 'base64');
      
      const iv = data.slice(0, this.ivLength);
      const tag = data.slice(this.ivLength, this.ivLength + this.tagLength);
      const encrypted = data.slice(this.ivLength + this.tagLength);
      
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
      decipher.setAuthTag(tag);
      
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ]);
      
      return decrypted.toString('utf8');

    } catch (error) {
      this.logger.error('解密字符串失败', error);
      throw new Error(`解密失败: ${error.message}`);
    }
  }

  /**
   * 生成文件哈希
   */
  async generateFileHash(filePath: string, algorithm: string = 'sha256'): Promise<string> {
    try {
      const data = await fs.readFile(filePath);
      const hash = crypto.createHash(algorithm);
      hash.update(data);
      return hash.digest('hex');

    } catch (error) {
      this.logger.error(`生成文件哈希失败: ${filePath}`, error);
      throw new Error(`生成哈希失败: ${error.message}`);
    }
  }

  /**
   * 验证文件完整性
   */
  async verifyFileIntegrity(filePath: string, expectedHash: string, algorithm: string = 'sha256'): Promise<boolean> {
    try {
      const actualHash = await this.generateFileHash(filePath, algorithm);
      return actualHash === expectedHash;

    } catch (error) {
      this.logger.error(`验证文件完整性失败: ${filePath}`, error);
      return false;
    }
  }

  /**
   * 生成随机密钥
   */
  generateRandomKey(): string {
    return crypto.randomBytes(this.keyLength).toString('hex');
  }

  /**
   * 从密码派生密钥
   */
  deriveKeyFromPassword(password: string, salt?: string): string {
    const actualSalt = salt || crypto.randomBytes(16).toString('hex');
    const key = crypto.pbkdf2Sync(password, actualSalt, 100000, this.keyLength, 'sha256');
    return key.toString('hex');
  }

  /**
   * 验证加密配置
   */
  validateEncryptionConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    const encryptionEnabled = this.configService.get<boolean>('backup.encryptionEnabled', false);
    if (!encryptionEnabled) {
      return { valid: true, errors: [] }; // 如果未启用加密，跳过验证
    }

    const encryptionKey = this.configService.get<string>('backup.encryptionKey');
    if (!encryptionKey) {
      errors.push('启用加密时必须提供加密密钥');
    } else if (encryptionKey.length < 32) {
      errors.push('加密密钥长度至少需要32个字符');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // 私有方法
  private getEncryptionKey(): Buffer {
    const keyString = this.configService.get<string>('backup.encryptionKey');
    if (!keyString) {
      throw new Error('未配置加密密钥');
    }

    // 如果密钥长度不足，使用PBKDF2派生
    if (keyString.length < this.keyLength * 2) { // hex编码需要双倍长度
      const derived = crypto.pbkdf2Sync(keyString, 'backup-salt', 100000, this.keyLength, 'sha256');
      return derived;
    }

    // 如果是hex编码的密钥
    if (/^[0-9a-fA-F]+$/.test(keyString) && keyString.length === this.keyLength * 2) {
      return Buffer.from(keyString, 'hex');
    }

    // 否则作为字符串处理，截取或填充到正确长度
    const keyBuffer = Buffer.from(keyString, 'utf8');
    if (keyBuffer.length === this.keyLength) {
      return keyBuffer;
    } else if (keyBuffer.length > this.keyLength) {
      return keyBuffer.slice(0, this.keyLength);
    } else {
      // 使用PBKDF2派生到正确长度
      return crypto.pbkdf2Sync(keyString, 'backup-salt', 100000, this.keyLength, 'sha256');
    }
  }
}
